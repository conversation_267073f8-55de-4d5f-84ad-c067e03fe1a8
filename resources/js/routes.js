// Import page components
import Dashboard from './pages/Dashboard.vue';
import POS from './pages/POS.vue';
import Pharmacy from './pages/Pharmacy.vue';
import Inventory from './pages/Inventory.vue';
import Reports from './pages/Reports.vue';
import Settings from './pages/Settings.vue';

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: 'Dashboard',
      requiresAuth: true
    }
  },
  {
    path: '/pos',
    name: 'POS',
    component: POS,
    meta: {
      title: 'Point of Sale',
      requiresAuth: true
    }
  },
  {
    path: '/pharmacy',
    name: 'Pharmacy',
    component: Pharmacy,
    meta: {
      title: 'Pharmacy Management',
      requiresAuth: true
    }
  },
  {
    path: '/inventory',
    name: 'Inventory',
    component: Inventory,
    meta: {
      title: 'Inventory Management',
      requiresAuth: true
    }
  },
  {
    path: '/reports',
    name: 'Reports',
    component: Reports,
    meta: {
      title: 'Reports',
      requiresAuth: true
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: {
      title: 'Settings',
      requiresAuth: true
    }
  }
];

export default routes;
