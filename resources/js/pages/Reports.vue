<template>
  <div>
    <v-row>
      <v-col cols="12">
        <h1 class="text-h4 mb-4">Reports</h1>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12" md="6">
        <v-card>
          <v-card-title>Sales Report</v-card-title>
          <v-card-text>
            <div class="text-h4 primary--text mb-2">$45,678</div>
            <div class="text-subtitle-1">Total Sales This Month</div>
            <v-btn color="primary" class="mt-3">
              <v-icon left>mdi-download</v-icon>
              Download Report
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="6">
        <v-card>
          <v-card-title>Inventory Report</v-card-title>
          <v-card-text>
            <div class="text-h4 success--text mb-2">1,234</div>
            <div class="text-subtitle-1">Total Products in Stock</div>
            <v-btn color="success" class="mt-3">
              <v-icon left>mdi-download</v-icon>
              Download Report
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <v-row class="mt-4">
      <v-col cols="12">
        <v-card>
          <v-card-title>Monthly Sales Chart</v-card-title>
          <v-card-text>
            <div class="text-center text-grey">
              <v-icon size="64">mdi-chart-line</v-icon>
              <div class="text-h6 mt-2">Chart visualization would be implemented here</div>
              <div class="text-subtitle-1">Using Chart.js or similar library</div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script>
export default {
  name: 'Reports'
}
</script>
