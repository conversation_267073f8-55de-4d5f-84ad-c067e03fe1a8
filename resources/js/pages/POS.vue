<template>
  <div>
    <v-row>
      <v-col cols="12">
        <h1 class="text-h4 mb-4">Point of Sale</h1>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12" md="8">
        <v-card>
          <v-card-title>Products</v-card-title>
          <v-card-text>
            <v-text-field
              v-model="search"
              label="Search products..."
              prepend-inner-icon="mdi-magnify"
              outlined
              dense
            />
            <v-row>
              <v-col
                v-for="product in filteredProducts"
                :key="product.id"
                cols="6"
                md="4"
              >
                <v-card
                  @click="addToCart(product)"
                  class="product-card"
                  hover
                >
                  <v-card-text class="text-center">
                    <v-icon size="48" color="primary">mdi-pill</v-icon>
                    <div class="text-subtitle-1 mt-2">{{ product.name }}</div>
                    <div class="text-h6 primary--text">${{ product.price }}</div>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="4">
        <v-card>
          <v-card-title>Cart</v-card-title>
          <v-card-text>
            <div v-if="cart.length === 0" class="text-center text-grey">
              Cart is empty
            </div>
            <div v-else>
              <div
                v-for="item in cart"
                :key="item.id"
                class="d-flex justify-space-between align-center mb-2"
              >
                <div>
                  <div class="text-subtitle-2">{{ item.name }}</div>
                  <div class="text-caption">{{ item.quantity }} x ${{ item.price }}</div>
                </div>
                <div class="text-subtitle-1">${{ (item.quantity * item.price).toFixed(2) }}</div>
              </div>
              <v-divider class="my-3" />
              <div class="d-flex justify-space-between text-h6">
                <span>Total:</span>
                <span>${{ cartTotal.toFixed(2) }}</span>
              </div>
              <v-btn
                block
                color="success"
                class="mt-3"
                @click="processPayment"
              >
                Process Payment
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script>
export default {
  name: 'POS',
  data() {
    return {
      search: '',
      cart: [],
      products: [
        { id: 1, name: 'Aspirin', price: 5.99 },
        { id: 2, name: 'Ibuprofen', price: 7.50 },
        { id: 3, name: 'Acetaminophen', price: 6.25 },
        { id: 4, name: 'Vitamin C', price: 12.99 },
        { id: 5, name: 'Multivitamin', price: 15.75 },
        { id: 6, name: 'Bandages', price: 3.50 }
      ]
    }
  },
  computed: {
    filteredProducts() {
      return this.products.filter(product =>
        product.name.toLowerCase().includes(this.search.toLowerCase())
      )
    },
    cartTotal() {
      return this.cart.reduce((total, item) => total + (item.quantity * item.price), 0)
    }
  },
  methods: {
    addToCart(product) {
      const existingItem = this.cart.find(item => item.id === product.id)
      if (existingItem) {
        existingItem.quantity++
      } else {
        this.cart.push({ ...product, quantity: 1 })
      }
    },
    processPayment() {
      alert('Payment processed successfully!')
      this.cart = []
    }
  }
}
</script>

<style scoped>
.product-card {
  cursor: pointer;
  transition: transform 0.2s;
}
.product-card:hover {
  transform: translateY(-2px);
}
</style>
