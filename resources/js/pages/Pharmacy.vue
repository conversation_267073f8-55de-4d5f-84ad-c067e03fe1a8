<template>
  <div>
    <v-row>
      <v-col cols="12">
        <h1 class="text-h4 mb-4">Pharmacy Management</h1>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            Prescription Management
            <v-spacer />
            <v-btn color="primary" @click="addPrescription">
              <v-icon left>mdi-plus</v-icon>
              New Prescription
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-data-table
              :headers="prescriptionHeaders"
              :items="prescriptions"
              :items-per-page="10"
              class="elevation-1"
            >
              <template v-slot:item.status="{ item }">
                <v-chip
                  :color="getStatusColor(item.status)"
                  dark
                  small
                >
                  {{ item.status }}
                </v-chip>
              </template>
              <template v-slot:item.actions="{ item }">
                <v-btn icon small @click="editPrescription(item)">
                  <v-icon>mdi-pencil</v-icon>
                </v-btn>
                <v-btn icon small @click="deletePrescription(item)">
                  <v-icon>mdi-delete</v-icon>
                </v-btn>
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script>
export default {
  name: 'Pharmacy',
  data() {
    return {
      prescriptionHeaders: [
        { text: 'ID', value: 'id' },
        { text: 'Patient', value: 'patient' },
        { text: 'Doctor', value: 'doctor' },
        { text: 'Medication', value: 'medication' },
        { text: 'Status', value: 'status' },
        { text: 'Date', value: 'date' },
        { text: 'Actions', value: 'actions', sortable: false }
      ],
      prescriptions: [
        {
          id: 1,
          patient: 'John Doe',
          doctor: 'Dr. Smith',
          medication: 'Amoxicillin 500mg',
          status: 'Pending',
          date: '2024-01-15'
        },
        {
          id: 2,
          patient: 'Jane Smith',
          doctor: 'Dr. Johnson',
          medication: 'Lisinopril 10mg',
          status: 'Ready',
          date: '2024-01-14'
        },
        {
          id: 3,
          patient: 'Bob Wilson',
          doctor: 'Dr. Brown',
          medication: 'Metformin 500mg',
          status: 'Dispensed',
          date: '2024-01-13'
        }
      ]
    }
  },
  methods: {
    getStatusColor(status) {
      switch (status) {
        case 'Pending': return 'orange'
        case 'Ready': return 'green'
        case 'Dispensed': return 'blue'
        default: return 'grey'
      }
    },
    addPrescription() {
      alert('Add prescription functionality would be implemented here')
    },
    editPrescription(item) {
      alert(`Edit prescription ${item.id}`)
    },
    deletePrescription(item) {
      alert(`Delete prescription ${item.id}`)
    }
  }
}
</script>
