<template>
  <div>
    <v-row>
      <v-col cols="12">
        <h1 class="text-h4 mb-4">Inventory Management</h1>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>
            Product Inventory
            <v-spacer />
            <v-btn color="primary" @click="addProduct">
              <v-icon left>mdi-plus</v-icon>
              Add Product
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-data-table
              :headers="inventoryHeaders"
              :items="inventory"
              :items-per-page="10"
              class="elevation-1"
            >
              <template v-slot:item.stock="{ item }">
                <v-chip
                  :color="getStockColor(item.stock)"
                  dark
                  small
                >
                  {{ item.stock }}
                </v-chip>
              </template>
              <template v-slot:item.actions="{ item }">
                <v-btn icon small @click="editProduct(item)">
                  <v-icon>mdi-pencil</v-icon>
                </v-btn>
                <v-btn icon small @click="deleteProduct(item)">
                  <v-icon>mdi-delete</v-icon>
                </v-btn>
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script>
export default {
  name: 'Inventory',
  data() {
    return {
      inventoryHeaders: [
        { text: 'ID', value: 'id' },
        { text: 'Name', value: 'name' },
        { text: 'Category', value: 'category' },
        { text: 'Price', value: 'price' },
        { text: 'Stock', value: 'stock' },
        { text: 'Expiry Date', value: 'expiry' },
        { text: 'Actions', value: 'actions', sortable: false }
      ],
      inventory: [
        {
          id: 1,
          name: 'Aspirin 325mg',
          category: 'Pain Relief',
          price: '$5.99',
          stock: 150,
          expiry: '2025-06-15'
        },
        {
          id: 2,
          name: 'Ibuprofen 200mg',
          category: 'Pain Relief',
          price: '$7.50',
          stock: 25,
          expiry: '2025-03-20'
        },
        {
          id: 3,
          name: 'Vitamin C 1000mg',
          category: 'Vitamins',
          price: '$12.99',
          stock: 5,
          expiry: '2025-12-31'
        }
      ]
    }
  },
  methods: {
    getStockColor(stock) {
      if (stock <= 10) return 'red'
      if (stock <= 25) return 'orange'
      return 'green'
    },
    addProduct() {
      alert('Add product functionality would be implemented here')
    },
    editProduct(item) {
      alert(`Edit product ${item.id}`)
    },
    deleteProduct(item) {
      alert(`Delete product ${item.id}`)
    }
  }
}
</script>
