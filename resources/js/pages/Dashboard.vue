<template>
  <div>
    <v-row>
      <v-col cols="12">
        <h1 class="text-h4 mb-4">Dashboard</h1>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12" md="3">
        <v-card>
          <v-card-text>
            <div class="text-h6">Total Sales</div>
            <div class="text-h4 primary--text">$12,345</div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" md="3">
        <v-card>
          <v-card-text>
            <div class="text-h6">Products</div>
            <div class="text-h4 success--text">1,234</div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" md="3">
        <v-card>
          <v-card-text>
            <div class="text-h6">Low Stock</div>
            <div class="text-h4 warning--text">23</div>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" md="3">
        <v-card>
          <v-card-text>
            <div class="text-h6">Orders</div>
            <div class="text-h4 info--text">456</div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <v-row class="mt-4">
      <v-col cols="12" md="8">
        <v-card>
          <v-card-title>Recent Transactions</v-card-title>
          <v-card-text>
            <v-data-table
              :headers="transactionHeaders"
              :items="recentTransactions"
              :items-per-page="5"
              class="elevation-1"
            />
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" md="4">
        <v-card>
          <v-card-title>Quick Actions</v-card-title>
          <v-card-text>
            <v-btn
              block
              color="primary"
              class="mb-2"
              to="/pos"
            >
              <v-icon left>mdi-point-of-sale</v-icon>
              New Sale
            </v-btn>
            <v-btn
              block
              color="success"
              class="mb-2"
              to="/inventory"
            >
              <v-icon left>mdi-package-variant-plus</v-icon>
              Add Product
            </v-btn>
            <v-btn
              block
              color="info"
              to="/reports"
            >
              <v-icon left>mdi-chart-line</v-icon>
              View Reports
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  data() {
    return {
      transactionHeaders: [
        { text: 'ID', value: 'id' },
        { text: 'Customer', value: 'customer' },
        { text: 'Amount', value: 'amount' },
        { text: 'Date', value: 'date' }
      ],
      recentTransactions: [
        { id: 1, customer: 'John Doe', amount: '$45.99', date: '2024-01-15' },
        { id: 2, customer: 'Jane Smith', amount: '$23.50', date: '2024-01-15' },
        { id: 3, customer: 'Bob Johnson', amount: '$67.25', date: '2024-01-14' },
        { id: 4, customer: 'Alice Brown', amount: '$12.75', date: '2024-01-14' },
        { id: 5, customer: 'Charlie Wilson', amount: '$89.99', date: '2024-01-13' }
      ]
    }
  }
}
</script>
