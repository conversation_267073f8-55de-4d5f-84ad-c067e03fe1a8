<template>
  <div class="dashboard">
    <!-- Header -->
    <v-row>
      <v-col cols="12">
        <div class="d-flex justify-space-between align-center mb-6">
          <div>
            <h1 class="text-h4 text-primary mb-2">
              Welcome back, {{ authStore.user?.name }}!
            </h1>
            <p class="text-subtitle-1 text-medium-emphasis">
              {{ currentDate }} • {{ authStore.user?.role?.charAt(0).toUpperCase() + authStore.user?.role?.slice(1) }}
            </p>
          </div>
          <v-btn
            color="primary"
            size="large"
            prepend-icon="mdi-point-of-sale"
            to="/pos"
          >
            New Sale
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <!-- Stats Cards -->
    <v-row>
      <v-col
        v-for="stat in stats"
        :key="stat.title"
        cols="12"
        sm="6"
        md="3"
      >
        <v-card
          class="stat-card"
          :color="stat.color"
          variant="tonal"
          height="140"
        >
          <v-card-text class="d-flex align-center justify-space-between pa-6">
            <div>
              <div class="text-subtitle-2 text-medium-emphasis mb-2">
                {{ stat.title }}
              </div>
              <div class="text-h4 font-weight-bold" :class="`text-${stat.color}`">
                {{ stat.value }}
              </div>
              <div class="text-caption mt-1" :class="stat.trend.color">
                <v-icon size="small" :icon="stat.trend.icon" />
                {{ stat.trend.text }}
              </div>
            </div>
            <v-avatar
              size="56"
              :color="stat.color"
              variant="flat"
            >
              <v-icon
                size="28"
                color="white"
                :icon="stat.icon"
              />
            </v-avatar>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Main Content -->
    <v-row class="mt-4">
      <!-- Recent Transactions -->
      <v-col cols="12" lg="8">
        <v-card class="h-100">
          <v-card-title class="d-flex justify-space-between align-center">
            <span>Recent Transactions</span>
            <v-btn
              variant="text"
              color="primary"
              size="small"
              to="/sales"
            >
              View All
            </v-btn>
          </v-card-title>
          <v-card-text>
            <v-data-table
              :headers="transactionHeaders"
              :items="recentTransactions"
              :items-per-page="5"
              hide-default-footer
              class="transaction-table"
            >
              <template #item.amount="{ item }">
                <span class="font-weight-bold text-success">
                  {{ formatCurrency(item.amount) }}
                </span>
              </template>
              <template #item.status="{ item }">
                <v-chip
                  :color="getStatusColor(item.status)"
                  size="small"
                  variant="tonal"
                >
                  {{ item.status }}
                </v-chip>
              </template>
              <template #item.date="{ item }">
                <span class="text-caption">
                  {{ formatDate(item.date) }}
                </span>
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Quick Actions & Alerts -->
      <v-col cols="12" lg="4">
        <v-row>
          <!-- Quick Actions -->
          <v-col cols="12">
            <v-card>
              <v-card-title>
                <v-icon start>mdi-lightning-bolt</v-icon>
                Quick Actions
              </v-card-title>
              <v-card-text>
                <div class="d-flex flex-column gap-3">
                  <v-btn
                    v-for="action in quickActions"
                    :key="action.title"
                    :color="action.color"
                    :to="action.to"
                    variant="outlined"
                    size="large"
                    block
                  >
                    <v-icon start :icon="action.icon" />
                    {{ action.title }}
                  </v-btn>
                </div>
              </v-card-text>
            </v-card>
          </v-col>

          <!-- Low Stock Alerts -->
          <v-col cols="12">
            <v-card>
              <v-card-title class="text-warning">
                <v-icon start>mdi-alert</v-icon>
                Low Stock Alerts
              </v-card-title>
              <v-card-text>
                <div v-if="lowStockItems.length === 0" class="text-center py-4">
                  <v-icon size="48" color="success" class="mb-2">
                    mdi-check-circle
                  </v-icon>
                  <p class="text-body-2 text-medium-emphasis">
                    All items are well stocked
                  </p>
                </div>
                <div v-else>
                  <v-list density="compact">
                    <v-list-item
                      v-for="item in lowStockItems"
                      :key="item.id"
                      :to="`/inventory/products/${item.id}`"
                    >
                      <template #prepend>
                        <v-avatar size="32" color="warning" variant="tonal">
                          <v-icon size="16">mdi-package-variant</v-icon>
                        </v-avatar>
                      </template>
                      <v-list-item-title class="text-body-2">
                        {{ item.name }}
                      </v-list-item-title>
                      <v-list-item-subtitle>
                        {{ item.stock }} units remaining
                      </v-list-item-subtitle>
                    </v-list-item>
                  </v-list>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useAppStore } from '@/stores/app';

// Stores
const authStore = useAuthStore();
const appStore = useAppStore();

// Reactive data
const stats = ref([
  {
    title: 'Total Sales',
    value: '$12,345',
    icon: 'mdi-cash-register',
    color: 'primary',
    trend: {
      text: '+12% from last month',
      icon: 'mdi-trending-up',
      color: 'text-success',
    },
  },
  {
    title: 'Products',
    value: '1,234',
    icon: 'mdi-package-variant',
    color: 'success',
    trend: {
      text: '+5 new products',
      icon: 'mdi-trending-up',
      color: 'text-success',
    },
  },
  {
    title: 'Low Stock',
    value: '23',
    icon: 'mdi-alert',
    color: 'warning',
    trend: {
      text: '3 critical items',
      icon: 'mdi-alert-circle',
      color: 'text-error',
    },
  },
  {
    title: 'Orders',
    value: '456',
    icon: 'mdi-shopping',
    color: 'info',
    trend: {
      text: '+8% this week',
      icon: 'mdi-trending-up',
      color: 'text-success',
    },
  },
]);

const transactionHeaders = [
  { title: 'ID', key: 'id', sortable: false },
  { title: 'Customer', key: 'customer' },
  { title: 'Amount', key: 'amount' },
  { title: 'Status', key: 'status' },
  { title: 'Date', key: 'date' },
];

const recentTransactions = ref([
  {
    id: 'TXN-001',
    customer: 'John Doe',
    amount: 45.99,
    status: 'completed',
    date: new Date('2024-01-15T10:30:00'),
  },
  {
    id: 'TXN-002',
    customer: 'Jane Smith',
    amount: 23.50,
    status: 'completed',
    date: new Date('2024-01-15T09:15:00'),
  },
  {
    id: 'TXN-003',
    customer: 'Bob Johnson',
    amount: 67.25,
    status: 'pending',
    date: new Date('2024-01-14T16:45:00'),
  },
  {
    id: 'TXN-004',
    customer: 'Alice Brown',
    amount: 12.75,
    status: 'completed',
    date: new Date('2024-01-14T14:20:00'),
  },
  {
    id: 'TXN-005',
    customer: 'Charlie Wilson',
    amount: 89.99,
    status: 'refunded',
    date: new Date('2024-01-13T11:10:00'),
  },
]);

const quickActions = [
  {
    title: 'Add Product',
    icon: 'mdi-package-variant-plus',
    color: 'success',
    to: '/inventory/products/create',
  },
  {
    title: 'View Reports',
    icon: 'mdi-chart-line',
    color: 'info',
    to: '/reports',
  },
  {
    title: 'Manage Users',
    icon: 'mdi-account-group',
    color: 'secondary',
    to: '/users',
  },
];

const lowStockItems = ref([
  { id: 1, name: 'Aspirin 500mg', stock: 5 },
  { id: 2, name: 'Vitamin C Tablets', stock: 8 },
  { id: 3, name: 'Bandages', stock: 12 },
]);

// Computed properties
const currentDate = computed(() => {
  return new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
});

// Methods
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

const getStatusColor = (status) => {
  const statusColors = {
    completed: 'success',
    pending: 'warning',
    refunded: 'error',
    cancelled: 'grey',
  };
  return statusColors[status] || 'grey';
};

// Lifecycle
onMounted(() => {
  // Load dashboard data
  loadDashboardData();
});

const loadDashboardData = async () => {
  try {
    // In a real app, this would fetch data from APIs
    // For now, we're using mock data
    console.log('Loading dashboard data...');

    // Show welcome notification for new users
    if (authStore.user?.login_count === 1) {
      appStore.showNotification({
        message: 'Welcome to MediPOS! Let\'s get started.',
        color: 'success',
        icon: 'mdi-hand-wave',
        timeout: 8000,
      });
    }
  } catch (error) {
    console.error('Failed to load dashboard data:', error);
    appStore.showNotification({
      message: 'Failed to load dashboard data',
      color: 'error',
      icon: 'mdi-alert-circle',
    });
  }
};
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.stat-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.transaction-table {
  border-radius: 8px;
}

.transaction-table :deep(.v-data-table__wrapper) {
  border-radius: 8px;
}

.gap-3 {
  gap: 12px;
}

/* Custom card styling */
.v-card {
  border-radius: 12px;
  transition: all 0.2s ease;
}

.v-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* List item hover effects */
.v-list-item {
  border-radius: 8px;
  margin-bottom: 4px;
  transition: background-color 0.2s ease;
}

.v-list-item:hover {
  background-color: rgba(var(--v-theme-primary), 0.05);
}

/* Button styling */
.v-btn {
  text-transform: none;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 960px) {
  .dashboard {
    padding: 0 8px;
  }

  .stat-card {
    margin-bottom: 16px;
  }
}

/* Animation for cards */
.v-card {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Stagger animation for stat cards */
.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }
</style>
