import axios from 'axios';

// Set up axios defaults
window.axios = axios;
window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
window.axios.defaults.headers.common['Accept'] = 'application/json';
window.axios.defaults.headers.common['Content-Type'] = 'application/json';

// Set base URL for API requests
window.axios.defaults.baseURL = window.location.origin;

// CSRF token setup
const token = document.head.querySelector('meta[name="csrf-token"]');
if (token) {
    window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
} else {
    console.error('CSRF token not found: https://laravel.com/docs/csrf#csrf-x-csrf-token');
}

// Request interceptor to add auth token
window.axios.interceptors.request.use(
    (config) => {
        // Add auth token if available
        const authToken = localStorage.getItem('auth_token');
        if (authToken) {
            config.headers.Authorization = `Bearer ${authToken}`;
        }

        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Response interceptor to handle auth errors
window.axios.interceptors.response.use(
    (response) => {
        return response;
    },
    async (error) => {
        const originalRequest = error.config;

        // Handle 401 Unauthorized errors
        if (error.response?.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            // Try to refresh token
            const refreshToken = localStorage.getItem('refresh_token');
            if (refreshToken) {
                try {
                    const response = await window.axios.post('/api/v1/auth/refresh', {
                        refresh_token: refreshToken,
                    });

                    if (response.data.success) {
                        const { token, refresh_token } = response.data.data;

                        // Update stored tokens
                        localStorage.setItem('auth_token', token);
                        if (refresh_token) {
                            localStorage.setItem('refresh_token', refresh_token);
                        }

                        // Update axios default header
                        window.axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

                        // Retry original request
                        originalRequest.headers.Authorization = `Bearer ${token}`;
                        return window.axios(originalRequest);
                    }
                } catch (refreshError) {
                    console.error('Token refresh failed:', refreshError);
                }
            }

            // If refresh failed or no refresh token, clear auth data and redirect to login
            localStorage.removeItem('auth_token');
            localStorage.removeItem('refresh_token');
            delete window.axios.defaults.headers.common['Authorization'];

            // Only redirect if we're not already on an auth page
            if (!window.location.pathname.startsWith('/auth/')) {
                window.location.href = '/auth/login?message=session_expired';
            }
        }

        // Handle 403 Forbidden errors
        if (error.response?.status === 403) {
            // Redirect to unauthorized page
            if (!window.location.pathname.includes('/unauthorized')) {
                window.location.href = '/unauthorized';
            }
        }

        // Handle 419 CSRF token mismatch
        if (error.response?.status === 419) {
            // Reload the page to get a fresh CSRF token
            window.location.reload();
        }

        return Promise.reject(error);
    }
);
