<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Location extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'phone',
        'email',
        'license_number',
        'is_active',
        'is_main',
        'settings',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'is_main' => 'boolean',
            'settings' => 'array',
        ];
    }

    /**
     * Get the users assigned to this location.
     */
    public function users(): Has<PERSON>any
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the inventory for this location.
     */
    public function inventory(): HasMany
    {
        return $this->hasMany(Inventory::class);
    }

    /**
     * Get the sales for this location.
     */
    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    /**
     * Get the purchase orders for this location.
     */
    public function purchaseOrders(): Has<PERSON><PERSON>
    {
        return $this->hasMany(PurchaseOrder::class);
    }

    /**
     * Get the stock adjustments for this location.
     */
    public function stockAdjustments(): HasMany
    {
        return $this->hasMany(StockAdjustment::class);
    }

    /**
     * Get the full address.
     */
    public function getFullAddressAttribute(): string
    {
        return trim($this->address . ', ' . $this->city . ', ' . $this->state . ' ' . $this->postal_code);
    }

    /**
     * Scope to get only active locations.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get the main location.
     */
    public function scopeMain($query)
    {
        return $query->where('is_main', true);
    }
}
