<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'generic_name',
        'brand',
        'sku',
        'barcode',
        'category_id',
        'supplier_id',
        'strength',
        'dosage_form',
        'unit_price',
        'selling_price',
        'mrp',
        'reorder_level',
        'max_stock_level',
        'is_prescription_required',
        'is_controlled_substance',
        'storage_conditions',
        'manufacturer',
        'is_active',
        'description',
        'image',
        'metadata',
    ];

    protected function casts(): array
    {
        return [
            'unit_price' => 'decimal:2',
            'selling_price' => 'decimal:2',
            'mrp' => 'decimal:2',
            'is_prescription_required' => 'boolean',
            'is_controlled_substance' => 'boolean',
            'is_active' => 'boolean',
            'metadata' => 'array',
        ];
    }

    /**
     * Get the category that the product belongs to.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the supplier that supplies this product.
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Get the inventory records for this product.
     */
    public function inventory(): HasMany
    {
        return $this->hasMany(Inventory::class);
    }

    /**
     * Get the sale items for this product.
     */
    public function saleItems(): HasMany
    {
        return $this->hasMany(SaleItem::class);
    }

    /**
     * Get the purchase items for this product.
     */
    public function purchaseItems(): HasMany
    {
        return $this->hasMany(PurchaseItem::class);
    }

    /**
     * Get the stock adjustments for this product.
     */
    public function stockAdjustments(): HasMany
    {
        return $this->hasMany(StockAdjustment::class);
    }

    /**
     * Get the prescription items for this product.
     */
    public function prescriptionItems(): HasMany
    {
        return $this->hasMany(PrescriptionItem::class);
    }

    /**
     * Scope to get only active products.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get prescription products.
     */
    public function scopePrescription($query)
    {
        return $query->where('is_prescription_required', true);
    }

    /**
     * Scope to get OTC products.
     */
    public function scopeOtc($query)
    {
        return $query->where('is_prescription_required', false);
    }

    /**
     * Scope to get low stock products.
     */
    public function scopeLowStock($query)
    {
        return $query->whereHas('inventory', function ($q) {
            $q->whereRaw('quantity_available <= reorder_level');
        });
    }

    /**
     * Get the total available quantity across all locations.
     */
    public function getTotalQuantityAttribute(): int
    {
        return $this->inventory()->sum('quantity_available');
    }

    /**
     * Get the product display name.
     */
    public function getDisplayNameAttribute(): string
    {
        $name = $this->name;
        if ($this->strength) {
            $name .= ' ' . $this->strength;
        }
        if ($this->dosage_form) {
            $name .= ' (' . $this->dosage_form . ')';
        }
        return $name;
    }

    /**
     * Check if product is low in stock.
     */
    public function isLowStock(): bool
    {
        return $this->total_quantity <= $this->reorder_level;
    }
}
