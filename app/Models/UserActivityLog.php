<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserActivityLog extends Model
{
    protected $fillable = [
        'user_id',
        'action',
        'description',
        'ip_address',
        'user_agent',
        'url',
        'method',
        'payload',
        'response_code',
        'location_id',
    ];

    protected function casts(): array
    {
        return [
            'payload' => 'array',
        ];
    }

    /**
     * Get the user that performed the activity.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the location where the activity occurred.
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Activity type constants.
     */
    public const ACTION_LOGIN = 'login';
    public const ACTION_LOGOUT = 'logout';
    public const ACTION_FAILED_LOGIN = 'failed_login';
    public const ACTION_PASSWORD_CHANGE = 'password_change';
    public const ACTION_PROFILE_UPDATE = 'profile_update';
    public const ACTION_ROLE_CHANGE = 'role_change';
    public const ACTION_ACCOUNT_LOCKED = 'account_locked';
    public const ACTION_ACCOUNT_UNLOCKED = 'account_unlocked';
    public const ACTION_ACCOUNT_ACTIVATION = 'account_activation';
    public const ACTION_ACCOUNT_DEACTIVATION = 'account_deactivation';
    public const ACTION_ACCOUNT_UNLOCK = 'account_unlock';
    public const ACTION_CREATE_USER = 'create_user';
    public const ACTION_UPDATE_USER = 'update_user';
    public const ACTION_DELETE_USER = 'delete_user';
    public const ACTION_CREATE_PRODUCT = 'create_product';
    public const ACTION_UPDATE_PRODUCT = 'update_product';
    public const ACTION_DELETE_PRODUCT = 'delete_product';
    public const ACTION_PROCESS_SALE = 'process_sale';
    public const ACTION_REFUND_SALE = 'refund_sale';
    public const ACTION_STOCK_ADJUSTMENT = 'stock_adjustment';
    public const ACTION_CREATE_PURCHASE = 'create_purchase';
    public const ACTION_RECEIVE_PURCHASE = 'receive_purchase';

    /**
     * Log user activity.
     */
    public static function log(
        string $action,
        string $description,
        ?User $user = null,
        ?array $payload = null,
        ?int $responseCode = null
    ): self {
        $request = request();

        return self::create([
            'user_id' => $user?->id ?? auth()->id(),
            'action' => $action,
            'description' => $description,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'payload' => $payload,
            'response_code' => $responseCode,
            'location_id' => $user?->location_id ?? auth()->user()?->location_id,
        ]);
    }

    /**
     * Scope to get activities by action.
     */
    public function scopeByAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope to get activities by user.
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get activities by date range.
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }
}
