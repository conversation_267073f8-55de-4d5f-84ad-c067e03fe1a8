<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Inventory extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'product_id',
        'product_variant_id',
        'location_id',
        'batch_number',
        'expiry_date',
        'manufacture_date',
        'vendor',
        'quantity_received',
        'quantity_available',
        'quantity_reserved',
        'quantity_sold',
        'cost_price',
        'selling_price',
        'storage_location',
        'status',
        'notes',
        'metadata',
    ];

    protected function casts(): array
    {
        return [
            'expiry_date' => 'datetime',
            'manufacture_date' => 'datetime',
            'cost_price' => 'decimal:2',
            'selling_price' => 'decimal:2',
            'metadata' => 'array',
        ];
    }

    // Status constants
    public const STATUS_ACTIVE = 'active';
    public const STATUS_EXPIRED = 'expired';
    public const STATUS_RECALLED = 'recalled';
    public const STATUS_DAMAGED = 'damaged';

    /**
     * Get the product that this inventory belongs to.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the product variant that this inventory belongs to.
     */
    public function productVariant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class);
    }

    /**
     * Get the location where this inventory is stored.
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get the stock adjustments for this inventory.
     */
    public function stockAdjustments(): HasMany
    {
        return $this->hasMany(StockAdjustment::class);
    }

    /**
     * Get the sale items from this inventory batch.
     */
    public function saleItems(): HasMany
    {
        return $this->hasMany(SaleItem::class);
    }

    /**
     * Scope to get only active inventory.
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope to get expired inventory.
     */
    public function scopeExpired($query)
    {
        return $query->where('status', self::STATUS_EXPIRED)
                    ->orWhere('expiry_date', '<', now());
    }

    /**
     * Scope to get inventory expiring soon.
     */
    public function scopeExpiringSoon($query, $days = 30)
    {
        return $query->where('expiry_date', '<=', now()->addDays($days))
                    ->where('expiry_date', '>', now())
                    ->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope to get low stock inventory.
     */
    public function scopeLowStock($query)
    {
        return $query->whereHas('product', function ($q) {
            $q->whereRaw('inventory.quantity_available <= products.reorder_level');
        });
    }

    /**
     * Scope to get inventory by location.
     */
    public function scopeByLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    /**
     * Check if this inventory batch is expired.
     */
    public function isExpired(): bool
    {
        return $this->expiry_date && $this->expiry_date < now();
    }

    /**
     * Check if this inventory batch is expiring soon.
     */
    public function isExpiringSoon($days = 30): bool
    {
        return $this->expiry_date &&
               $this->expiry_date <= now()->addDays($days) &&
               $this->expiry_date > now();
    }

    /**
     * Get the days until expiry.
     */
    public function getDaysUntilExpiryAttribute(): ?int
    {
        if (!$this->expiry_date) {
            return null;
        }

        return now()->diffInDays($this->expiry_date, false);
    }

    /**
     * Get the total value of this inventory batch.
     */
    public function getTotalValueAttribute(): float
    {
        return $this->quantity_available * $this->cost_price;
    }

    /**
     * Get the potential selling value of this inventory batch.
     */
    public function getSellingValueAttribute(): float
    {
        return $this->quantity_available * $this->selling_price;
    }

    /**
     * Get the profit margin for this batch.
     */
    public function getProfitMarginAttribute(): float
    {
        if ($this->cost_price == 0) {
            return 0;
        }

        return (($this->selling_price - $this->cost_price) / $this->cost_price) * 100;
    }

    /**
     * Check if quantity is available for sale.
     */
    public function hasAvailableQuantity($requestedQuantity): bool
    {
        return $this->quantity_available >= $requestedQuantity;
    }

    /**
     * Reserve quantity for a sale.
     */
    public function reserveQuantity($quantity): bool
    {
        if (!$this->hasAvailableQuantity($quantity)) {
            return false;
        }

        $this->quantity_available -= $quantity;
        $this->quantity_reserved += $quantity;

        return $this->save();
    }

    /**
     * Release reserved quantity.
     */
    public function releaseReservedQuantity($quantity): bool
    {
        if ($this->quantity_reserved < $quantity) {
            return false;
        }

        $this->quantity_available += $quantity;
        $this->quantity_reserved -= $quantity;

        return $this->save();
    }

    /**
     * Sell quantity from this batch.
     */
    public function sellQuantity($quantity): bool
    {
        if ($this->quantity_reserved < $quantity) {
            return false;
        }

        $this->quantity_reserved -= $quantity;
        $this->quantity_sold += $quantity;

        return $this->save();
    }
}
