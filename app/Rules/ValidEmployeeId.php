<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidEmployeeId implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!is_string($value)) {
            $fail('The :attribute must be a string.');
            return;
        }

        $employeeId = $value;
        $errors = [];

        // Check minimum length
        if (strlen($employeeId) < 3) {
            $errors[] = 'at least 3 characters';
        }

        // Check maximum length
        if (strlen($employeeId) > 20) {
            $errors[] = 'no more than 20 characters';
        }

        // Check format (alphanumeric with optional hyphens and underscores)
        if (!preg_match('/^[A-Za-z0-9_-]+$/', $employeeId)) {
            $errors[] = 'only letters, numbers, hyphens, and underscores';
        }

        // Check that it starts with a letter or number
        if (!preg_match('/^[A-Za-z0-9]/', $employeeId)) {
            $errors[] = 'start with a letter or number';
        }

        // Check that it doesn't end with a hyphen or underscore
        if (preg_match('/[-_]$/', $employeeId)) {
            $errors[] = 'not end with a hyphen or underscore';
        }

        // Check for consecutive special characters
        if (preg_match('/[-_]{2,}/', $employeeId)) {
            $errors[] = 'no consecutive hyphens or underscores';
        }

        if (!empty($errors)) {
            $fail("The :attribute must contain " . implode(', ', $errors) . '.');
        }
    }
}
