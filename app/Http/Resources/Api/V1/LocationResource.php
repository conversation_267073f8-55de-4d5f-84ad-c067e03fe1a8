<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LocationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'type' => $this->type,
            'type_display' => $this->getTypeDisplayAttribute(),
            'parent_id' => $this->parent_id,
            'aisle' => $this->aisle,
            'shelf' => $this->shelf,
            'bin' => $this->bin,
            'capacity' => $this->capacity,
            'current_stock' => $this->current_stock,
            'temperature_min' => $this->temperature_min,
            'temperature_max' => $this->temperature_max,
            'humidity_min' => $this->humidity_min,
            'humidity_max' => $this->humidity_max,
            'is_active' => $this->is_active,
            'description' => $this->description,
            
            // Calculated fields
            'full_path' => $this->getFullPathAttribute(),
            'depth_level' => $this->getDepthLevelAttribute(),
            'capacity_utilization' => $this->capacity > 0 ? 
                round(($this->current_stock / $this->capacity) * 100, 2) : 0,
            'available_capacity' => max(0, $this->capacity - $this->current_stock),
            'is_over_capacity' => $this->current_stock > $this->capacity,
            'is_near_capacity' => $this->capacity > 0 && 
                ($this->current_stock / $this->capacity) >= 0.9,
            
            // Storage conditions
            'has_temperature_control' => $this->temperature_min !== null || $this->temperature_max !== null,
            'has_humidity_control' => $this->humidity_min !== null || $this->humidity_max !== null,
            'storage_conditions_summary' => $this->getStorageConditionsSummary(),
            
            // Hierarchy information
            'children_count' => $this->when($this->relationLoaded('children'), function () {
                return $this->children->count();
            }),
            'total_descendants' => $this->when($this->relationLoaded('allChildren'), function () {
                return $this->allChildren->count();
            }),
            'inventory_count' => $this->when($this->relationLoaded('inventory'), function () {
                return $this->inventory->count();
            }),
            'total_inventory_value' => $this->when($this->relationLoaded('inventory'), function () {
                return $this->inventory->sum(function ($item) {
                    return $item->quantity_available * $item->unit_cost;
                });
            }),
            
            // Address information
            'full_address' => $this->getFullAddressAttribute(),
            'location_path' => $this->getLocationPathAttribute(),
            
            // Relationships
            'parent' => new LocationResource($this->whenLoaded('parent')),
            'children' => LocationResource::collection($this->whenLoaded('children')),
            'inventory' => InventoryResource::collection($this->whenLoaded('inventory')),
            'users' => UserResource::collection($this->whenLoaded('users')),
            
            // Metadata
            'metadata' => $this->metadata,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
    
    /**
     * Get storage conditions summary
     */
    private function getStorageConditionsSummary(): string
    {
        $conditions = [];
        
        if ($this->temperature_min !== null || $this->temperature_max !== null) {
            $temp = '';
            if ($this->temperature_min !== null && $this->temperature_max !== null) {
                $temp = "{$this->temperature_min}°C - {$this->temperature_max}°C";
            } elseif ($this->temperature_min !== null) {
                $temp = "Min: {$this->temperature_min}°C";
            } else {
                $temp = "Max: {$this->temperature_max}°C";
            }
            $conditions[] = "Temperature: {$temp}";
        }
        
        if ($this->humidity_min !== null || $this->humidity_max !== null) {
            $humidity = '';
            if ($this->humidity_min !== null && $this->humidity_max !== null) {
                $humidity = "{$this->humidity_min}% - {$this->humidity_max}%";
            } elseif ($this->humidity_min !== null) {
                $humidity = "Min: {$this->humidity_min}%";
            } else {
                $humidity = "Max: {$this->humidity_max}%";
            }
            $conditions[] = "Humidity: {$humidity}";
        }
        
        return empty($conditions) ? 'Standard conditions' : implode(', ', $conditions);
    }
}
