<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PrescriptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'prescription_number' => $this->prescription_number,
            'customer_id' => $this->customer_id,
            'doctor_name' => $this->doctor_name,
            'doctor_license' => $this->doctor_license,
            'doctor_phone' => $this->doctor_phone,
            'doctor_address' => $this->doctor_address,
            'prescribed_date' => $this->prescribed_date,
            'expiry_date' => $this->expiry_date,
            'status' => $this->status,
            'status_display' => $this->getStatusDisplayAttribute(),
            'diagnosis' => $this->diagnosis,
            'patient_weight' => $this->patient_weight,
            'patient_age' => $this->patient_age,
            'special_instructions' => $this->special_instructions,
            'refills_allowed' => $this->refills_allowed,
            'refills_used' => $this->refills_used,
            'notes' => $this->notes,
            
            // Calculated fields
            'refills_remaining' => max(0, $this->refills_allowed - $this->refills_used),
            'has_refills_available' => ($this->refills_allowed - $this->refills_used) > 0,
            'is_expired' => $this->expiry_date ? now()->isAfter($this->expiry_date) : false,
            'days_to_expiry' => $this->expiry_date ? now()->diffInDays($this->expiry_date, false) : null,
            'is_near_expiry' => $this->expiry_date ? 
                now()->addDays(30)->isAfter($this->expiry_date) && now()->isBefore($this->expiry_date) : false,
            'days_since_prescribed' => $this->prescribed_date ? now()->diffInDays($this->prescribed_date) : null,
            'is_active' => $this->status === 'active' && 
                         (!$this->expiry_date || now()->isBefore($this->expiry_date)),
            
            // Items information
            'items_count' => $this->when($this->relationLoaded('items'), function () {
                return $this->items->count();
            }),
            'total_medications' => $this->when($this->relationLoaded('items'), function () {
                return $this->items->count();
            }),
            'controlled_substances_count' => $this->when($this->relationLoaded('items'), function () {
                return $this->items->filter(function ($item) {
                    return $item->product && $item->product->is_controlled_substance;
                })->count();
            }),
            'has_controlled_substances' => $this->when($this->relationLoaded('items'), function () {
                return $this->items->filter(function ($item) {
                    return $item->product && $item->product->is_controlled_substance;
                })->count() > 0;
            }),
            
            // Fulfillment information
            'total_quantity_prescribed' => $this->when($this->relationLoaded('items'), function () {
                return $this->items->sum('quantity_prescribed');
            }),
            'total_quantity_dispensed' => $this->when($this->relationLoaded('items'), function () {
                return $this->items->sum('quantity_dispensed');
            }),
            'is_fully_dispensed' => $this->when($this->relationLoaded('items'), function () {
                return $this->items->every(function ($item) {
                    return $item->quantity_dispensed >= $item->quantity_prescribed;
                });
            }),
            'dispensing_percentage' => $this->when($this->relationLoaded('items'), function () {
                $totalPrescribed = $this->items->sum('quantity_prescribed');
                $totalDispensed = $this->items->sum('quantity_dispensed');
                return $totalPrescribed > 0 ? 
                    round(($totalDispensed / $totalPrescribed) * 100, 2) : 0;
            }),
            
            // Sales information
            'sales_count' => $this->when($this->relationLoaded('sales'), function () {
                return $this->sales->count();
            }),
            'total_sales_amount' => $this->when($this->relationLoaded('sales'), function () {
                return $this->sales->sum('total_amount');
            }),
            'last_dispensed_date' => $this->when($this->relationLoaded('sales'), function () {
                return $this->sales->latest('sale_date')->first()?->sale_date;
            }),
            'days_since_last_dispensed' => $this->when($this->relationLoaded('sales'), function () {
                $lastSale = $this->sales->latest('sale_date')->first();
                return $lastSale ? now()->diffInDays($lastSale->sale_date) : null;
            }),
            
            // Doctor information
            'doctor_full_info' => $this->getDoctorFullInfo(),
            'doctor_license_valid' => $this->isDoctorLicenseValid(),
            'doctor_contact_available' => !empty($this->doctor_phone),
            
            // Patient information
            'patient_info_complete' => $this->isPatientInfoComplete(),
            'patient_age_category' => $this->getPatientAgeCategory(),
            'requires_special_handling' => $this->requiresSpecialHandling(),
            
            // Compliance and safety
            'compliance_score' => $this->getComplianceScore(),
            'safety_alerts' => $this->getSafetyAlerts(),
            'drug_interactions' => $this->when($this->relationLoaded('items'), function () {
                return $this->checkDrugInteractions();
            }),
            
            // Prescription validity
            'is_valid_for_dispensing' => $this->isValidForDispensing(),
            'validation_errors' => $this->getValidationErrors(),
            'can_be_refilled' => $this->canBeRefilled(),
            'next_refill_date' => $this->getNextRefillDate(),
            
            // Relationships
            'customer' => new CustomerResource($this->whenLoaded('customer')),
            'items' => PrescriptionItemResource::collection($this->whenLoaded('items')),
            'sales' => SaleResource::collection($this->whenLoaded('sales')),
            
            // Metadata
            'metadata' => $this->metadata,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
    
    /**
     * Get doctor full information
     */
    private function getDoctorFullInfo(): string
    {
        $info = $this->doctor_name;
        if ($this->doctor_license) {
            $info .= " (License: {$this->doctor_license})";
        }
        return $info;
    }
    
    /**
     * Check if doctor license is valid format
     */
    private function isDoctorLicenseValid(): bool
    {
        if (!$this->doctor_license) {
            return false;
        }
        
        // Basic validation - should be alphanumeric and 6-15 characters
        return preg_match('/^[A-Z0-9]{6,15}$/', $this->doctor_license);
    }
    
    /**
     * Check if patient information is complete
     */
    private function isPatientInfoComplete(): bool
    {
        return !empty($this->patient_age) && !empty($this->patient_weight);
    }
    
    /**
     * Get patient age category
     */
    private function getPatientAgeCategory(): string
    {
        if (!$this->patient_age) {
            return 'unknown';
        }
        
        if ($this->patient_age < 2) {
            return 'infant';
        } elseif ($this->patient_age < 12) {
            return 'child';
        } elseif ($this->patient_age < 18) {
            return 'adolescent';
        } elseif ($this->patient_age < 65) {
            return 'adult';
        } else {
            return 'senior';
        }
    }
    
    /**
     * Check if requires special handling
     */
    private function requiresSpecialHandling(): bool
    {
        // Special handling for pediatric or geriatric patients
        if ($this->patient_age && ($this->patient_age < 18 || $this->patient_age > 65)) {
            return true;
        }
        
        // Special handling for controlled substances
        if ($this->relationLoaded('items')) {
            return $this->items->filter(function ($item) {
                return $item->product && $item->product->is_controlled_substance;
            })->count() > 0;
        }
        
        return false;
    }
    
    /**
     * Get compliance score
     */
    private function getComplianceScore(): int
    {
        $score = 100;
        
        // Deduct points for missing information
        if (empty($this->doctor_license)) $score -= 10;
        if (empty($this->patient_age)) $score -= 5;
        if (empty($this->patient_weight)) $score -= 5;
        if (empty($this->diagnosis)) $score -= 10;
        
        // Deduct points for expired prescription
        if ($this->expiry_date && now()->isAfter($this->expiry_date)) {
            $score -= 50;
        }
        
        // Deduct points for overused refills
        if ($this->refills_used > $this->refills_allowed) {
            $score -= 30;
        }
        
        return max(0, $score);
    }
    
    /**
     * Get safety alerts
     */
    private function getSafetyAlerts(): array
    {
        $alerts = [];
        
        if ($this->expiry_date && now()->isAfter($this->expiry_date)) {
            $alerts[] = 'Prescription has expired';
        }
        
        if ($this->refills_used > $this->refills_allowed) {
            $alerts[] = 'Refills exceeded allowed limit';
        }
        
        if ($this->patient_age && $this->patient_age < 18) {
            $alerts[] = 'Pediatric patient - verify dosing';
        }
        
        if ($this->patient_age && $this->patient_age > 65) {
            $alerts[] = 'Geriatric patient - check for interactions';
        }
        
        return $alerts;
    }
    
    /**
     * Check drug interactions (simplified)
     */
    private function checkDrugInteractions(): array
    {
        // This would typically integrate with a drug interaction database
        // For now, return empty array
        return [];
    }
    
    /**
     * Check if valid for dispensing
     */
    private function isValidForDispensing(): bool
    {
        return $this->status === 'active' && 
               (!$this->expiry_date || now()->isBefore($this->expiry_date)) &&
               $this->refills_used < $this->refills_allowed;
    }
    
    /**
     * Get validation errors
     */
    private function getValidationErrors(): array
    {
        $errors = [];
        
        if ($this->status !== 'active') {
            $errors[] = 'Prescription is not active';
        }
        
        if ($this->expiry_date && now()->isAfter($this->expiry_date)) {
            $errors[] = 'Prescription has expired';
        }
        
        if ($this->refills_used >= $this->refills_allowed) {
            $errors[] = 'No refills remaining';
        }
        
        return $errors;
    }
    
    /**
     * Check if can be refilled
     */
    private function canBeRefilled(): bool
    {
        return $this->isValidForDispensing() && 
               ($this->refills_allowed - $this->refills_used) > 0;
    }
    
    /**
     * Get next refill date
     */
    private function getNextRefillDate(): ?string
    {
        if (!$this->canBeRefilled()) {
            return null;
        }
        
        // Typically, refills can be done after 75% of the previous supply is used
        // This is a simplified calculation
        if ($this->relationLoaded('sales')) {
            $lastSale = $this->sales->latest('sale_date')->first();
            if ($lastSale) {
                return $lastSale->sale_date->addDays(30)->toDateString();
            }
        }
        
        return now()->toDateString();
    }
}
