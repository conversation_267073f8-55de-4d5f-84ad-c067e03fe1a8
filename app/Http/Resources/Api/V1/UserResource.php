<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'role' => $this->role,
            'role_display' => $this->getRoleDisplayAttribute(),
            'status' => $this->status,
            'status_display' => $this->getStatusDisplayAttribute(),
            'employee_id' => $this->employee_id,
            'department' => $this->department,
            'position' => $this->position,
            'hire_date' => $this->hire_date,
            'license_number' => $this->license_number,
            'license_expiry' => $this->license_expiry,
            'permissions' => $this->permissions,
            'last_login_at' => $this->last_login_at,
            'email_verified_at' => $this->email_verified_at,
            'is_active' => $this->is_active,

            // Calculated fields
            'full_name' => $this->name,
            'initials' => $this->getInitials(),
            'is_email_verified' => $this->email_verified_at !== null,
            'days_since_hire' => $this->hire_date ? now()->diffInDays($this->hire_date) : null,
            'years_of_service' => $this->hire_date ? now()->diffInYears($this->hire_date) : null,
            'is_license_valid' => $this->isLicenseValid(),
            'license_expires_soon' => $this->licenseExpiresSoon(),
            'days_to_license_expiry' => $this->license_expiry ?
                now()->diffInDays($this->license_expiry, false) : null,

            // Login information
            'days_since_last_login' => $this->last_login_at ?
                now()->diffInDays($this->last_login_at) : null,
            'is_recently_active' => $this->last_login_at ?
                now()->diffInDays($this->last_login_at) <= 7 : false,
            'login_frequency' => $this->getLoginFrequency(),

            // Role and permissions
            'is_admin' => $this->role === 'admin',
            'is_pharmacist' => $this->role === 'pharmacist',
            'is_technician' => $this->role === 'technician',
            'is_cashier' => $this->role === 'cashier',
            'can_dispense_medications' => in_array($this->role, ['pharmacist', 'technician']),
            'can_approve_prescriptions' => $this->role === 'pharmacist',
            'can_manage_inventory' => in_array($this->role, ['admin', 'pharmacist', 'manager']),
            'can_process_sales' => in_array($this->role, ['pharmacist', 'technician', 'cashier']),
            'permission_level' => $this->getPermissionLevel(),

            // Activity statistics
            'sales_processed' => $this->when($this->relationLoaded('sales'), function () {
                return $this->sales->count();
            }),
            'total_sales_amount' => $this->when($this->relationLoaded('sales'), function () {
                return $this->sales->sum('total_amount');
            }),
            'prescriptions_processed' => $this->when($this->relationLoaded('processedPrescriptions'), function () {
                return $this->processedPrescriptions->count();
            }),
            'stock_adjustments_made' => $this->when($this->relationLoaded('stockAdjustments'), function () {
                return $this->stockAdjustments->count();
            }),

            // Performance metrics
            'average_sale_amount' => $this->when($this->relationLoaded('sales'), function () {
                $count = $this->sales->count();
                return $count > 0 ? round($this->sales->sum('total_amount') / $count, 2) : 0;
            }),
            'sales_this_month' => $this->when($this->relationLoaded('sales'), function () {
                return $this->sales->where('sale_date', '>=', now()->startOfMonth())->count();
            }),
            'performance_score' => $this->getPerformanceScore(),

            // Contact and emergency information
            'primary_contact' => $this->phone ?: $this->email,
            'emergency_contact' => $this->emergency_contact_name,
            'emergency_phone' => $this->emergency_contact_phone,
            'has_emergency_contact' => !empty($this->emergency_contact_name),

            // Professional information
            'professional_title' => $this->getProfessionalTitle(),
            'certification_status' => $this->getCertificationStatus(),
            'training_required' => $this->getTrainingRequired(),
            'compliance_status' => $this->getComplianceStatus(),

            // Security information
            'two_factor_enabled' => $this->two_factor_enabled ?? false,
            'password_last_changed' => $this->password_changed_at,
            'password_expires_soon' => $this->passwordExpiresSoon(),
            'account_locked' => $this->status === 'locked',
            'failed_login_attempts' => $this->failed_login_attempts ?? 0,

            // Relationships (limited for security)
            'activity_logs' => $this->when($this->relationLoaded('activityLogs') && $request->user()?->can('view-user-activities'), function () {
                return $this->activityLogs->take(10)->map(function ($log) {
                    return [
                        'action' => $log->action,
                        'description' => $log->description,
                        'created_at' => $log->created_at,
                    ];
                });
            }),

            // Metadata
            'metadata' => $this->metadata,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * Get user initials
     */
    private function getInitials(): string
    {
        $names = explode(' ', $this->name);
        $initials = '';

        foreach ($names as $name) {
            if (!empty($name)) {
                $initials .= strtoupper(substr($name, 0, 1));
            }
        }

        return $initials;
    }

    /**
     * Check if license is valid
     */
    private function isLicenseValid(): bool
    {
        if (!$this->license_expiry) {
            return !empty($this->license_number); // Valid if has license but no expiry
        }

        return now()->isBefore($this->license_expiry);
    }

    /**
     * Check if license expires soon (within 30 days)
     */
    private function licenseExpiresSoon(): bool
    {
        if (!$this->license_expiry) {
            return false;
        }

        return now()->addDays(30)->isAfter($this->license_expiry) &&
               now()->isBefore($this->license_expiry);
    }

    /**
     * Get login frequency category
     */
    private function getLoginFrequency(): string
    {
        if (!$this->last_login_at) {
            return 'never';
        }

        $daysSinceLogin = now()->diffInDays($this->last_login_at);

        if ($daysSinceLogin <= 1) {
            return 'daily';
        } elseif ($daysSinceLogin <= 7) {
            return 'weekly';
        } elseif ($daysSinceLogin <= 30) {
            return 'monthly';
        } else {
            return 'infrequent';
        }
    }

    /**
     * Get permission level
     */
    private function getPermissionLevel(): string
    {
        switch ($this->role) {
            case 'admin':
            case 'owner':
                return 'full';
            case 'manager':
            case 'pharmacist':
                return 'high';
            case 'technician':
                return 'medium';
            case 'cashier':
            case 'intern':
                return 'limited';
            default:
                return 'basic';
        }
    }

    /**
     * Get performance score
     */
    private function getPerformanceScore(): int
    {
        $score = 50; // Base score

        // Add points for recent activity
        if ($this->last_login_at && now()->diffInDays($this->last_login_at) <= 7) {
            $score += 20;
        }

        // Add points for valid license
        if ($this->isLicenseValid()) {
            $score += 15;
        }

        // Add points for sales activity
        if ($this->relationLoaded('sales')) {
            $salesThisMonth = $this->sales->where('sale_date', '>=', now()->startOfMonth())->count();
            $score += min(15, $salesThisMonth); // Max 15 points for sales
        }

        return min(100, $score);
    }

    /**
     * Get professional title
     */
    private function getProfessionalTitle(): string
    {
        $title = $this->position ?: ucfirst($this->role);

        if ($this->role === 'pharmacist' && $this->license_number) {
            $title .= ', PharmD';
        }

        return $title;
    }

    /**
     * Get certification status
     */
    private function getCertificationStatus(): string
    {
        if (!$this->license_number) {
            return 'not_required';
        }

        if (!$this->isLicenseValid()) {
            return 'expired';
        }

        if ($this->licenseExpiresSoon()) {
            return 'expiring_soon';
        }

        return 'valid';
    }

    /**
     * Get training required
     */
    private function getTrainingRequired(): array
    {
        $training = [];

        if ($this->licenseExpiresSoon()) {
            $training[] = 'License renewal training';
        }

        if ($this->role === 'pharmacist' && !$this->last_login_at) {
            $training[] = 'System orientation';
        }

        return $training;
    }

    /**
     * Get compliance status
     */
    private function getComplianceStatus(): string
    {
        if (!$this->isLicenseValid()) {
            return 'non_compliant';
        }

        if ($this->licenseExpiresSoon()) {
            return 'attention_required';
        }

        return 'compliant';
    }

    /**
     * Check if password expires soon
     */
    private function passwordExpiresSoon(): bool
    {
        if (!$this->password_changed_at) {
            return true; // Assume needs change if never changed
        }

        // Password expires after 90 days
        return now()->diffInDays($this->password_changed_at) > 80;
    }
}
