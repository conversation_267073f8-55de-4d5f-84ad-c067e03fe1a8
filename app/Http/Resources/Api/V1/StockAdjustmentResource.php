<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StockAdjustmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'inventory_id' => $this->inventory_id,
            'adjustment_type' => $this->adjustment_type,
            'adjustment_type_display' => $this->getAdjustmentTypeDisplayAttribute(),
            'quantity_before' => $this->quantity_before,
            'quantity_adjusted' => $this->quantity_adjusted,
            'quantity_after' => $this->quantity_after,
            'unit_cost_before' => $this->unit_cost_before,
            'unit_cost_after' => $this->unit_cost_after,
            'reason' => $this->reason,
            'reference_number' => $this->reference_number,
            'notes' => $this->notes,
            'status' => $this->status,
            'status_display' => $this->getStatusDisplayAttribute(),
            'created_by' => $this->created_by,
            'approved_by' => $this->approved_by,
            'approved_at' => $this->approved_at,
            
            // Calculated fields
            'cost_impact' => $this->getCostImpact(),
            'percentage_change' => $this->getPercentageChange(),
            'is_increase' => $this->quantity_adjusted > 0,
            'is_decrease' => $this->quantity_adjusted < 0,
            'absolute_quantity' => abs($this->quantity_adjusted),
            'days_since_adjustment' => $this->created_at ? now()->diffInDays($this->created_at) : null,
            'requires_approval' => $this->requiresApproval(),
            'is_approved' => $this->status === 'approved',
            'is_pending' => $this->status === 'pending',
            
            // Value calculations
            'value_before' => $this->quantity_before * $this->unit_cost_before,
            'value_after' => $this->quantity_after * $this->unit_cost_after,
            'value_change' => ($this->quantity_after * $this->unit_cost_after) - 
                            ($this->quantity_before * $this->unit_cost_before),
            
            // Approval information
            'approval_required' => $this->isApprovalRequired(),
            'can_be_approved' => $this->canBeApproved(),
            'approval_deadline' => $this->getApprovalDeadline(),
            'is_overdue_approval' => $this->isOverdueApproval(),
            
            // Relationships
            'inventory' => new InventoryResource($this->whenLoaded('inventory')),
            'created_by_user' => new UserResource($this->whenLoaded('createdBy')),
            'approved_by_user' => new UserResource($this->whenLoaded('approvedBy')),
            
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
    
    /**
     * Get cost impact of the adjustment
     */
    private function getCostImpact(): float
    {
        $costBefore = $this->quantity_before * $this->unit_cost_before;
        $costAfter = $this->quantity_after * $this->unit_cost_after;
        
        return round($costAfter - $costBefore, 2);
    }
    
    /**
     * Get percentage change in quantity
     */
    private function getPercentageChange(): float
    {
        if ($this->quantity_before == 0) {
            return $this->quantity_adjusted > 0 ? 100 : 0;
        }
        
        return round(($this->quantity_adjusted / $this->quantity_before) * 100, 2);
    }
    
    /**
     * Check if adjustment requires approval
     */
    private function requiresApproval(): bool
    {
        // Large quantity adjustments require approval
        if (abs($this->quantity_adjusted) > 100) {
            return true;
        }
        
        // High value adjustments require approval
        if (abs($this->getCostImpact()) > 1000) {
            return true;
        }
        
        // Certain adjustment types always require approval
        $approvalRequiredTypes = ['damage', 'theft', 'loss', 'found'];
        if (in_array($this->adjustment_type, $approvalRequiredTypes)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if approval is required
     */
    private function isApprovalRequired(): bool
    {
        return $this->requiresApproval();
    }
    
    /**
     * Check if adjustment can be approved
     */
    private function canBeApproved(): bool
    {
        return $this->status === 'pending' && $this->requiresApproval();
    }
    
    /**
     * Get approval deadline
     */
    private function getApprovalDeadline(): ?string
    {
        if (!$this->requiresApproval() || $this->status !== 'pending') {
            return null;
        }
        
        // Approval deadline is 7 days from creation
        return $this->created_at?->addDays(7)->toDateString();
    }
    
    /**
     * Check if approval is overdue
     */
    private function isOverdueApproval(): bool
    {
        if (!$this->requiresApproval() || $this->status !== 'pending') {
            return false;
        }
        
        $deadline = $this->created_at?->addDays(7);
        return $deadline && now()->isAfter($deadline);
    }
}
