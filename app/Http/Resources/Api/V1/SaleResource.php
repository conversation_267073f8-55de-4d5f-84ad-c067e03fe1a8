<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SaleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'sale_number' => $this->sale_number,
            'customer_id' => $this->customer_id,
            'prescription_id' => $this->prescription_id,
            'sale_date' => $this->sale_date,
            'sale_type' => $this->sale_type,
            'sale_type_display' => $this->getSaleTypeDisplayAttribute(),
            'payment_method' => $this->payment_method,
            'payment_method_display' => $this->getPaymentMethodDisplayAttribute(),
            'payment_status' => $this->payment_status,
            'payment_status_display' => $this->getPaymentStatusDisplayAttribute(),
            'subtotal' => $this->subtotal,
            'discount_amount' => $this->discount_amount,
            'tax_amount' => $this->tax_amount,
            'total_amount' => $this->total_amount,
            'amount_paid' => $this->amount_paid,
            'change_amount' => $this->change_amount,
            'insurance_claim_number' => $this->insurance_claim_number,
            'insurance_amount' => $this->insurance_amount,
            'notes' => $this->notes,
            'created_by' => $this->created_by,

            // Calculated fields
            'items_count' => $this->when($this->relationLoaded('items'), function () {
                return $this->items->count();
            }),
            'total_quantity' => $this->when($this->relationLoaded('items'), function () {
                return $this->items->sum('quantity_sold');
            }),
            'outstanding_amount' => max(0, $this->total_amount - ($this->amount_paid ?? 0)),
            'is_fully_paid' => ($this->amount_paid ?? 0) >= $this->total_amount,
            'is_overpaid' => ($this->amount_paid ?? 0) > $this->total_amount,
            'payment_percentage' => $this->total_amount > 0 ?
                round((($this->amount_paid ?? 0) / $this->total_amount) * 100, 2) : 0,

            // Profit calculations
            'total_cost' => $this->when($this->relationLoaded('items'), function () {
                return $this->items->sum(function ($item) {
                    return ($item->inventory->unit_cost ?? 0) * $item->quantity_sold;
                });
            }),
            'total_profit' => $this->when($this->relationLoaded('items'), function () {
                $totalCost = $this->items->sum(function ($item) {
                    return ($item->inventory->unit_cost ?? 0) * $item->quantity_sold;
                });
                return $this->total_amount - $totalCost;
            }),
            'profit_margin' => $this->when($this->relationLoaded('items'), function () {
                $totalCost = $this->items->sum(function ($item) {
                    return ($item->inventory->unit_cost ?? 0) * $item->quantity_sold;
                });
                return $this->total_amount > 0 ?
                    round((($this->total_amount - $totalCost) / $this->total_amount) * 100, 2) : 0;
            }),

            // Discount information
            'discount_percentage' => $this->subtotal > 0 ?
                round(($this->discount_amount / $this->subtotal) * 100, 2) : 0,
            'has_discount' => $this->discount_amount > 0,

            // Tax information
            'tax_percentage' => $this->subtotal > 0 ?
                round(($this->tax_amount / $this->subtotal) * 100, 2) : 0,
            'effective_tax_rate' => ($this->subtotal - $this->discount_amount) > 0 ?
                round(($this->tax_amount / ($this->subtotal - $this->discount_amount)) * 100, 2) : 0,

            // Time information
            'days_since_sale' => $this->sale_date ? now()->diffInDays($this->sale_date) : null,
            'sale_time' => $this->created_at?->format('H:i:s'),
            'is_today' => $this->sale_date ? $this->sale_date->isToday() : false,
            'is_this_week' => $this->sale_date ? $this->sale_date->isCurrentWeek() : false,
            'is_this_month' => $this->sale_date ? $this->sale_date->isCurrentMonth() : false,

            // Return/refund information
            'can_be_refunded' => $this->canBeRefunded(),
            'refund_deadline' => $this->getRefundDeadline(),
            'is_refundable' => $this->isRefundable(),
            'has_prescription_items' => $this->when($this->relationLoaded('items'), function () {
                return $this->items->whereNotNull('prescription_item_id')->count() > 0;
            }),

            // Insurance information
            'is_insurance_sale' => $this->sale_type === 'insurance' || $this->payment_method === 'insurance',
            'insurance_coverage' => $this->insurance_amount && $this->total_amount > 0 ?
                round(($this->insurance_amount / $this->total_amount) * 100, 2) : 0,
            'patient_responsibility' => $this->is_insurance_sale ?
                max(0, $this->total_amount - ($this->insurance_amount ?? 0)) : $this->total_amount,

            // Relationships
            'customer' => new CustomerResource($this->whenLoaded('customer')),
            'prescription' => new PrescriptionResource($this->whenLoaded('prescription')),
            'items' => SaleItemResource::collection($this->whenLoaded('items')),
            'created_by_user' => new UserResource($this->whenLoaded('createdBy')),

            // Metadata
            'metadata' => $this->metadata,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * Check if sale can be refunded
     */
    private function canBeRefunded(): bool
    {
        // Only paid or partially paid sales can be refunded
        if (!in_array($this->payment_status, ['paid', 'partial'])) {
            return false;
        }

        // Check if within refund period (30 days)
        if ($this->sale_date && now()->diffInDays($this->sale_date) > 30) {
            return false;
        }

        // Check if already refunded
        if ($this->payment_status === 'refunded') {
            return false;
        }

        return true;
    }

    /**
     * Get refund deadline
     */
    private function getRefundDeadline(): ?string
    {
        if (!$this->canBeRefunded()) {
            return null;
        }

        return $this->sale_date?->addDays(30)->toDateString();
    }

    /**
     * Check if sale is refundable
     */
    private function isRefundable(): bool
    {
        return $this->canBeRefunded();
    }
}
