<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'generic_name' => $this->generic_name,
            'brand' => $this->brand,
            'sku' => $this->sku,
            'barcode' => $this->barcode,
            'strength' => $this->strength,
            'dosage_form' => $this->dosage_form,
            'unit_price' => $this->unit_price,
            'selling_price' => $this->selling_price,
            'mrp' => $this->mrp,
            'reorder_level' => $this->reorder_level,
            'max_stock_level' => $this->max_stock_level,
            'is_prescription_required' => $this->is_prescription_required,
            'is_controlled_substance' => $this->is_controlled_substance,
            'storage_conditions' => $this->storage_conditions,
            'manufacturer' => $this->manufacturer,
            'is_active' => $this->is_active,
            'description' => $this->description,
            'image' => $this->image,
            'display_name' => $this->display_name,
            'total_quantity' => $this->when($this->relationLoaded('inventory'), function () {
                return $this->inventory->sum('quantity_available');
            }),
            'is_low_stock' => $this->when($this->relationLoaded('inventory'), function () {
                return $this->isLowStock();
            }),

            // Relationships
            'category' => new CategoryResource($this->whenLoaded('category')),
            'supplier' => new SupplierResource($this->whenLoaded('supplier')),
            'inventory' => InventoryResource::collection($this->whenLoaded('inventory')),

            // Metadata
            'metadata' => $this->metadata,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
