<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PurchaseOrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'order_number' => $this->order_number,
            'supplier_id' => $this->supplier_id,
            'order_date' => $this->order_date,
            'expected_delivery_date' => $this->expected_delivery_date,
            'actual_delivery_date' => $this->actual_delivery_date,
            'status' => $this->status,
            'status_display' => $this->getStatusDisplayAttribute(),
            'subtotal' => $this->subtotal,
            'tax_amount' => $this->tax_amount,
            'discount_amount' => $this->discount_amount,
            'shipping_cost' => $this->shipping_cost,
            'total_amount' => $this->total_amount,
            'payment_status' => $this->payment_status,
            'payment_method' => $this->payment_method,
            'payment_date' => $this->payment_date,
            'reference_number' => $this->reference_number,
            'notes' => $this->notes,
            'approved_by' => $this->approved_by,
            'approved_at' => $this->approved_at,
            'received_by' => $this->received_by,
            'received_at' => $this->received_at,
            
            // Calculated fields
            'items_count' => $this->when($this->relationLoaded('items'), function () {
                return $this->items->count();
            }),
            'total_quantity' => $this->when($this->relationLoaded('items'), function () {
                return $this->items->sum('quantity_ordered');
            }),
            'received_quantity' => $this->when($this->relationLoaded('items'), function () {
                return $this->items->sum('quantity_received');
            }),
            'pending_quantity' => $this->when($this->relationLoaded('items'), function () {
                return $this->items->sum(function ($item) {
                    return $item->quantity_ordered - $item->quantity_received;
                });
            }),
            'is_fully_received' => $this->when($this->relationLoaded('items'), function () {
                return $this->items->every(function ($item) {
                    return $item->quantity_received >= $item->quantity_ordered;
                });
            }),
            'days_since_order' => $this->order_date ? now()->diffInDays($this->order_date) : null,
            'is_overdue' => $this->expected_delivery_date ? 
                now()->isAfter($this->expected_delivery_date) && $this->status !== 'received' : false,
            
            // Relationships
            'supplier' => new SupplierResource($this->whenLoaded('supplier')),
            'items' => PurchaseItemResource::collection($this->whenLoaded('items')),
            'approved_by_user' => new UserResource($this->whenLoaded('approvedBy')),
            'received_by_user' => new UserResource($this->whenLoaded('receivedBy')),
            'created_by_user' => new UserResource($this->whenLoaded('createdBy')),
            
            // Metadata
            'metadata' => $this->metadata,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
