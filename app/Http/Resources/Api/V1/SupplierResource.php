<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SupplierResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'contact_person' => $this->contact_person,
            'email' => $this->email,
            'phone' => $this->phone,
            'address' => $this->address,
            'city' => $this->city,
            'state' => $this->state,
            'postal_code' => $this->postal_code,
            'country' => $this->country,
            'tax_number' => $this->tax_number,
            'license_number' => $this->license_number,
            'credit_limit' => $this->credit_limit,
            'payment_terms' => $this->payment_terms,
            'discount_percentage' => $this->discount_percentage,
            'is_active' => $this->is_active,
            'notes' => $this->notes,

            // Calculated fields
            'outstanding_balance' => $this->when($this->relationLoaded('purchaseOrders'), function () {
                return $this->purchaseOrders()
                    ->whereIn('status', ['pending', 'approved', 'received'])
                    ->sum('total_amount');
            }),
            'total_purchases' => $this->when($this->relationLoaded('purchaseOrders'), function () {
                return $this->purchaseOrders()->count();
            }),
            'products_count' => $this->when($this->relationLoaded('products'), function () {
                return $this->products->count();
            }),
            'last_purchase_date' => $this->when($this->relationLoaded('purchaseOrders'), function () {
                return $this->purchaseOrders()->latest()->first()?->order_date;
            }),

            // Relationships
            'products' => ProductResource::collection($this->whenLoaded('products')),
            'purchase_orders' => PurchaseOrderResource::collection($this->whenLoaded('purchaseOrders')),

            // Metadata
            'metadata' => $this->metadata,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
