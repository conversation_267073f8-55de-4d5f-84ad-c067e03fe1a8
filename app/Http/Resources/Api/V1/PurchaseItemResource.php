<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PurchaseItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'purchase_order_id' => $this->purchase_order_id,
            'product_id' => $this->product_id,
            'product_variant_id' => $this->product_variant_id,
            'quantity_ordered' => $this->quantity_ordered,
            'quantity_received' => $this->quantity_received,
            'unit_cost' => $this->unit_cost,
            'discount_amount' => $this->discount_amount,
            'tax_amount' => $this->tax_amount,
            'total_cost' => $this->total_cost,
            'batch_number' => $this->batch_number,
            'expiry_date' => $this->expiry_date,
            'manufacturing_date' => $this->manufacturing_date,
            'notes' => $this->notes,
            
            // Calculated fields
            'pending_quantity' => $this->quantity_ordered - $this->quantity_received,
            'is_fully_received' => $this->quantity_received >= $this->quantity_ordered,
            'received_percentage' => $this->quantity_ordered > 0 ? 
                round(($this->quantity_received / $this->quantity_ordered) * 100, 2) : 0,
            'subtotal' => $this->quantity_ordered * $this->unit_cost,
            'net_cost' => ($this->quantity_ordered * $this->unit_cost) - $this->discount_amount + $this->tax_amount,
            
            // Expiry information
            'days_to_expiry' => $this->expiry_date ? now()->diffInDays($this->expiry_date, false) : null,
            'is_expired' => $this->expiry_date ? now()->isAfter($this->expiry_date) : false,
            'is_near_expiry' => $this->expiry_date ? 
                now()->addDays(30)->isAfter($this->expiry_date) && now()->isBefore($this->expiry_date) : false,
            
            // Relationships
            'product' => new ProductResource($this->whenLoaded('product')),
            'product_variant' => new ProductVariantResource($this->whenLoaded('productVariant')),
            'purchase_order' => new PurchaseOrderResource($this->whenLoaded('purchaseOrder')),
            
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
