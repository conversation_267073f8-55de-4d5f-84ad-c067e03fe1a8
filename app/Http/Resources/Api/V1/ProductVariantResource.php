<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductVariantResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'product_id' => $this->product_id,
            'variant_type' => $this->variant_type,
            'variant_value' => $this->variant_value,
            'sku' => $this->sku,
            'barcode' => $this->barcode,
            'unit_price' => $this->unit_price,
            'selling_price' => $this->selling_price,
            'mrp' => $this->mrp,
            'weight' => $this->weight,
            'dimensions' => $this->dimensions,
            'packaging_type' => $this->packaging_type,
            'units_per_package' => $this->units_per_package,
            'conversion_factor' => $this->conversion_factor,
            'is_active' => $this->is_active,
            'image' => $this->image,
            
            // Calculated fields
            'display_name' => $this->getDisplayNameAttribute(),
            'full_sku' => $this->getFullSkuAttribute(),
            'price_difference' => $this->when($this->relationLoaded('product'), function () {
                return $this->selling_price - $this->product->selling_price;
            }),
            'price_percentage' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->selling_price > 0 ? 
                    round((($this->selling_price - $this->product->selling_price) / $this->product->selling_price) * 100, 2) : 0;
            }),
            'total_inventory' => $this->when($this->relationLoaded('inventory'), function () {
                return $this->inventory->sum('quantity_available');
            }),
            'is_low_stock' => $this->when($this->relationLoaded('inventory'), function () {
                $totalStock = $this->inventory->sum('quantity_available');
                return $totalStock <= ($this->product->reorder_level ?? 0);
            }),
            
            // Unit conversion
            'base_unit_quantity' => $this->conversion_factor * $this->units_per_package,
            'unit_cost_per_base' => $this->conversion_factor > 0 ? 
                $this->unit_price / $this->conversion_factor : $this->unit_price,
            
            // Barcode validation
            'barcode_valid' => $this->barcode ? $this->validateBarcode() : null,
            'barcode_type' => $this->barcode ? $this->getBarcodeType() : null,
            
            // Relationships
            'product' => new ProductResource($this->whenLoaded('product')),
            'inventory' => InventoryResource::collection($this->whenLoaded('inventory')),
            'sale_items' => SaleItemResource::collection($this->whenLoaded('saleItems')),
            'purchase_items' => PurchaseItemResource::collection($this->whenLoaded('purchaseItems')),
            
            // Metadata
            'metadata' => $this->metadata,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
    
    /**
     * Validate barcode format
     */
    private function validateBarcode(): bool
    {
        if (!$this->barcode) {
            return false;
        }
        
        // EAN-13 validation
        if (strlen($this->barcode) === 13 && is_numeric($this->barcode)) {
            return $this->validateEan13Checksum();
        }
        
        // UPC-A validation
        if (strlen($this->barcode) === 12 && is_numeric($this->barcode)) {
            return $this->validateUpcAChecksum();
        }
        
        return false;
    }
    
    /**
     * Get barcode type
     */
    private function getBarcodeType(): ?string
    {
        if (!$this->barcode) {
            return null;
        }
        
        if (strlen($this->barcode) === 13 && is_numeric($this->barcode)) {
            return 'EAN-13';
        }
        
        if (strlen($this->barcode) === 12 && is_numeric($this->barcode)) {
            return 'UPC-A';
        }
        
        return 'Unknown';
    }
    
    /**
     * Validate EAN-13 checksum
     */
    private function validateEan13Checksum(): bool
    {
        $digits = str_split($this->barcode);
        $checksum = 0;
        
        for ($i = 0; $i < 12; $i++) {
            $checksum += $digits[$i] * (($i % 2 === 0) ? 1 : 3);
        }
        
        $calculatedCheck = (10 - ($checksum % 10)) % 10;
        return $calculatedCheck == $digits[12];
    }
    
    /**
     * Validate UPC-A checksum
     */
    private function validateUpcAChecksum(): bool
    {
        $digits = str_split($this->barcode);
        $checksum = 0;
        
        for ($i = 0; $i < 11; $i++) {
            $checksum += $digits[$i] * (($i % 2 === 0) ? 3 : 1);
        }
        
        $calculatedCheck = (10 - ($checksum % 10)) % 10;
        return $calculatedCheck == $digits[11];
    }
}
