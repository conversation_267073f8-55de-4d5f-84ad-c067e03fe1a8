<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InventoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'product_id' => $this->product_id,
            'product_variant_id' => $this->product_variant_id,
            'location_id' => $this->location_id,
            'batch_number' => $this->batch_number,
            'quantity_available' => $this->quantity_available,
            'quantity_reserved' => $this->quantity_reserved,
            'quantity_sold' => $this->quantity_sold,
            'unit_cost' => $this->unit_cost,
            'selling_price' => $this->selling_price,
            'mrp' => $this->mrp,
            'manufacturing_date' => $this->manufacturing_date,
            'expiry_date' => $this->expiry_date,
            'supplier_id' => $this->supplier_id,
            'purchase_order_id' => $this->purchase_order_id,
            'valuation_method' => $this->valuation_method,
            'status' => $this->status,
            'notes' => $this->notes,

            // Calculated fields
            'total_quantity' => $this->quantity_available + $this->quantity_reserved + $this->quantity_sold,
            'available_for_sale' => $this->quantity_available - $this->quantity_reserved,
            'inventory_value' => $this->quantity_available * $this->unit_cost,
            'potential_revenue' => $this->quantity_available * $this->selling_price,
            'profit_margin' => $this->selling_price > 0 ?
                round((($this->selling_price - $this->unit_cost) / $this->selling_price) * 100, 2) : 0,

            // Expiry information
            'days_to_expiry' => $this->expiry_date ? now()->diffInDays($this->expiry_date, false) : null,
            'is_expired' => $this->expiry_date ? now()->isAfter($this->expiry_date) : false,
            'is_near_expiry' => $this->expiry_date ?
                now()->addDays(30)->isAfter($this->expiry_date) && now()->isBefore($this->expiry_date) : false,
            'expiry_status' => $this->getExpiryStatus(),

            // Stock status
            'is_low_stock' => $this->when($this->relationLoaded('product'), function () {
                return $this->quantity_available <= ($this->product->reorder_level ?? 0);
            }),
            'is_out_of_stock' => $this->quantity_available <= 0,
            'stock_status' => $this->getStockStatus(),

            // Age information
            'days_in_stock' => $this->manufacturing_date ? now()->diffInDays($this->manufacturing_date) : null,
            'shelf_life_remaining' => $this->getShelfLifeRemaining(),

            // Turnover information
            'turnover_rate' => $this->when($this->relationLoaded('saleItems'), function () {
                $totalSold = $this->saleItems->sum('quantity_sold');
                $avgStock = ($this->quantity_available + $this->quantity_sold) / 2;
                return $avgStock > 0 ? round($totalSold / $avgStock, 2) : 0;
            }),

            // Relationships
            'product' => new ProductResource($this->whenLoaded('product')),
            'product_variant' => new ProductVariantResource($this->whenLoaded('productVariant')),
            'location' => new LocationResource($this->whenLoaded('location')),
            'supplier' => new SupplierResource($this->whenLoaded('supplier')),
            'purchase_order' => new PurchaseOrderResource($this->whenLoaded('purchaseOrder')),
            'stock_adjustments' => StockAdjustmentResource::collection($this->whenLoaded('stockAdjustments')),
            'sale_items' => SaleItemResource::collection($this->whenLoaded('saleItems')),

            // Metadata
            'metadata' => $this->metadata,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * Get expiry status
     */
    private function getExpiryStatus(): string
    {
        if (!$this->expiry_date) {
            return 'no_expiry';
        }

        if (now()->isAfter($this->expiry_date)) {
            return 'expired';
        }

        if (now()->addDays(30)->isAfter($this->expiry_date)) {
            return 'near_expiry';
        }

        if (now()->addDays(90)->isAfter($this->expiry_date)) {
            return 'approaching_expiry';
        }

        return 'fresh';
    }

    /**
     * Get stock status
     */
    private function getStockStatus(): string
    {
        if ($this->quantity_available <= 0) {
            return 'out_of_stock';
        }

        if ($this->relationLoaded('product') && $this->quantity_available <= ($this->product->reorder_level ?? 0)) {
            return 'low_stock';
        }

        if ($this->relationLoaded('product') && $this->quantity_available >= ($this->product->max_stock_level ?? PHP_INT_MAX)) {
            return 'overstock';
        }

        return 'in_stock';
    }

    /**
     * Get shelf life remaining percentage
     */
    private function getShelfLifeRemaining(): ?float
    {
        if (!$this->manufacturing_date || !$this->expiry_date) {
            return null;
        }

        $totalShelfLife = $this->manufacturing_date->diffInDays($this->expiry_date);
        $remainingDays = now()->diffInDays($this->expiry_date, false);

        if ($totalShelfLife <= 0) {
            return 0;
        }

        return round(($remainingDays / $totalShelfLife) * 100, 2);
    }
}
