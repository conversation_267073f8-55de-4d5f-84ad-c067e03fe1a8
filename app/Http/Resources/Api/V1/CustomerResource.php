<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'customer_number' => $this->customer_number,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'full_name' => $this->getFullNameAttribute(),
            'email' => $this->email,
            'phone' => $this->phone,
            'date_of_birth' => $this->date_of_birth,
            'gender' => $this->gender,
            'address' => $this->address,
            'city' => $this->city,
            'state' => $this->state,
            'postal_code' => $this->postal_code,
            'country' => $this->country,
            'emergency_contact_name' => $this->emergency_contact_name,
            'emergency_contact_phone' => $this->emergency_contact_phone,
            'medical_conditions' => $this->medical_conditions,
            'allergies' => $this->allergies,
            'insurance_provider' => $this->insurance_provider,
            'insurance_number' => $this->insurance_number,
            'insurance_group' => $this->insurance_group,
            'loyalty_program_number' => $this->loyalty_program_number,
            'loyalty_points' => $this->loyalty_points,
            'credit_limit' => $this->credit_limit,
            'is_active' => $this->is_active,
            'notes' => $this->notes,
            
            // Calculated fields
            'age' => $this->date_of_birth ? now()->diffInYears($this->date_of_birth) : null,
            'is_senior' => $this->date_of_birth ? now()->diffInYears($this->date_of_birth) >= 65 : false,
            'is_minor' => $this->date_of_birth ? now()->diffInYears($this->date_of_birth) < 18 : false,
            'has_insurance' => !empty($this->insurance_provider),
            'has_loyalty_program' => !empty($this->loyalty_program_number),
            'has_medical_conditions' => !empty($this->medical_conditions),
            'has_allergies' => !empty($this->allergies),
            
            // Purchase history
            'total_purchases' => $this->when($this->relationLoaded('sales'), function () {
                return $this->sales->count();
            }),
            'total_spent' => $this->when($this->relationLoaded('sales'), function () {
                return $this->sales->sum('total_amount');
            }),
            'average_purchase' => $this->when($this->relationLoaded('sales'), function () {
                $count = $this->sales->count();
                return $count > 0 ? round($this->sales->sum('total_amount') / $count, 2) : 0;
            }),
            'last_purchase_date' => $this->when($this->relationLoaded('sales'), function () {
                return $this->sales->latest('sale_date')->first()?->sale_date;
            }),
            'days_since_last_purchase' => $this->when($this->relationLoaded('sales'), function () {
                $lastSale = $this->sales->latest('sale_date')->first();
                return $lastSale ? now()->diffInDays($lastSale->sale_date) : null;
            }),
            
            // Outstanding balance
            'outstanding_balance' => $this->when($this->relationLoaded('sales'), function () {
                return $this->sales()
                    ->whereIn('payment_status', ['pending', 'partial'])
                    ->sum('total_amount') - 
                    $this->sales()
                    ->whereIn('payment_status', ['pending', 'partial'])
                    ->sum('amount_paid');
            }),
            'credit_available' => $this->when($this->relationLoaded('sales'), function () {
                $outstanding = $this->sales()
                    ->whereIn('payment_status', ['pending', 'partial'])
                    ->sum('total_amount') - 
                    $this->sales()
                    ->whereIn('payment_status', ['pending', 'partial'])
                    ->sum('amount_paid');
                return max(0, $this->credit_limit - $outstanding);
            }),
            'is_over_credit_limit' => $this->when($this->relationLoaded('sales'), function () {
                $outstanding = $this->sales()
                    ->whereIn('payment_status', ['pending', 'partial'])
                    ->sum('total_amount') - 
                    $this->sales()
                    ->whereIn('payment_status', ['pending', 'partial'])
                    ->sum('amount_paid');
                return $outstanding > $this->credit_limit;
            }),
            
            // Loyalty program
            'loyalty_tier' => $this->getLoyaltyTier(),
            'points_to_next_tier' => $this->getPointsToNextTier(),
            'points_value' => $this->loyalty_points * 0.01, // 1 point = $0.01
            'can_redeem_points' => $this->loyalty_points >= 100, // Minimum 100 points to redeem
            
            // Prescription information
            'active_prescriptions' => $this->when($this->relationLoaded('prescriptions'), function () {
                return $this->prescriptions->where('status', 'active')->count();
            }),
            'total_prescriptions' => $this->when($this->relationLoaded('prescriptions'), function () {
                return $this->prescriptions->count();
            }),
            'has_active_prescriptions' => $this->when($this->relationLoaded('prescriptions'), function () {
                return $this->prescriptions->where('status', 'active')->count() > 0;
            }),
            
            // Contact information
            'full_address' => $this->getFullAddressAttribute(),
            'primary_contact' => $this->phone ?: $this->email,
            'has_emergency_contact' => !empty($this->emergency_contact_name) && !empty($this->emergency_contact_phone),
            
            // Customer status
            'customer_status' => $this->getCustomerStatus(),
            'risk_level' => $this->getRiskLevel(),
            'customer_type' => $this->getCustomerType(),
            
            // Relationships
            'sales' => SaleResource::collection($this->whenLoaded('sales')),
            'prescriptions' => PrescriptionResource::collection($this->whenLoaded('prescriptions')),
            
            // Metadata
            'metadata' => $this->metadata,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
    
    /**
     * Get loyalty tier based on points
     */
    private function getLoyaltyTier(): string
    {
        if ($this->loyalty_points >= 10000) {
            return 'Platinum';
        } elseif ($this->loyalty_points >= 5000) {
            return 'Gold';
        } elseif ($this->loyalty_points >= 1000) {
            return 'Silver';
        } elseif ($this->loyalty_points >= 100) {
            return 'Bronze';
        }
        
        return 'Basic';
    }
    
    /**
     * Get points needed for next tier
     */
    private function getPointsToNextTier(): int
    {
        if ($this->loyalty_points >= 10000) {
            return 0; // Already at highest tier
        } elseif ($this->loyalty_points >= 5000) {
            return 10000 - $this->loyalty_points;
        } elseif ($this->loyalty_points >= 1000) {
            return 5000 - $this->loyalty_points;
        } elseif ($this->loyalty_points >= 100) {
            return 1000 - $this->loyalty_points;
        }
        
        return 100 - $this->loyalty_points;
    }
    
    /**
     * Get customer status
     */
    private function getCustomerStatus(): string
    {
        if (!$this->is_active) {
            return 'inactive';
        }
        
        if ($this->relationLoaded('sales')) {
            $lastSale = $this->sales->latest('sale_date')->first();
            if (!$lastSale) {
                return 'new';
            }
            
            $daysSinceLastPurchase = now()->diffInDays($lastSale->sale_date);
            if ($daysSinceLastPurchase > 365) {
                return 'dormant';
            } elseif ($daysSinceLastPurchase > 90) {
                return 'inactive_buyer';
            } elseif ($daysSinceLastPurchase <= 30) {
                return 'active';
            }
            
            return 'occasional';
        }
        
        return 'active';
    }
    
    /**
     * Get risk level
     */
    private function getRiskLevel(): string
    {
        if ($this->relationLoaded('sales')) {
            $outstanding = $this->sales()
                ->whereIn('payment_status', ['pending', 'partial'])
                ->sum('total_amount') - 
                $this->sales()
                ->whereIn('payment_status', ['pending', 'partial'])
                ->sum('amount_paid');
            
            if ($outstanding > $this->credit_limit) {
                return 'high';
            } elseif ($outstanding > ($this->credit_limit * 0.8)) {
                return 'medium';
            }
        }
        
        return 'low';
    }
    
    /**
     * Get customer type
     */
    private function getCustomerType(): string
    {
        if ($this->relationLoaded('sales')) {
            $totalSpent = $this->sales->sum('total_amount');
            $purchaseCount = $this->sales->count();
            
            if ($totalSpent > 10000 || $purchaseCount > 50) {
                return 'vip';
            } elseif ($totalSpent > 5000 || $purchaseCount > 20) {
                return 'premium';
            } elseif ($totalSpent > 1000 || $purchaseCount > 5) {
                return 'regular';
            }
        }
        
        return 'new';
    }
}
