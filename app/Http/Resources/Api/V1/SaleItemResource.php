<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SaleItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'sale_id' => $this->sale_id,
            'product_id' => $this->product_id,
            'product_variant_id' => $this->product_variant_id,
            'inventory_id' => $this->inventory_id,
            'quantity_sold' => $this->quantity_sold,
            'unit_price' => $this->unit_price,
            'discount_amount' => $this->discount_amount,
            'tax_amount' => $this->tax_amount,
            'total_amount' => $this->total_amount,
            'prescription_item_id' => $this->prescription_item_id,
            'batch_number' => $this->batch_number,
            'expiry_date' => $this->expiry_date,
            'notes' => $this->notes,
            
            // Calculated fields
            'subtotal' => $this->quantity_sold * $this->unit_price,
            'net_amount' => ($this->quantity_sold * $this->unit_price) - $this->discount_amount + $this->tax_amount,
            'discount_percentage' => $this->unit_price > 0 ? 
                round(($this->discount_amount / ($this->quantity_sold * $this->unit_price)) * 100, 2) : 0,
            'tax_percentage' => $this->unit_price > 0 ? 
                round(($this->tax_amount / ($this->quantity_sold * $this->unit_price)) * 100, 2) : 0,
            
            // Profit calculations
            'unit_cost' => $this->when($this->relationLoaded('inventory'), function () {
                return $this->inventory->unit_cost ?? 0;
            }),
            'total_cost' => $this->when($this->relationLoaded('inventory'), function () {
                return ($this->inventory->unit_cost ?? 0) * $this->quantity_sold;
            }),
            'profit_per_unit' => $this->when($this->relationLoaded('inventory'), function () {
                return $this->unit_price - ($this->inventory->unit_cost ?? 0);
            }),
            'total_profit' => $this->when($this->relationLoaded('inventory'), function () {
                $unitProfit = $this->unit_price - ($this->inventory->unit_cost ?? 0);
                return $unitProfit * $this->quantity_sold;
            }),
            'profit_margin' => $this->when($this->relationLoaded('inventory'), function () {
                $unitCost = $this->inventory->unit_cost ?? 0;
                return $this->unit_price > 0 ? 
                    round((($this->unit_price - $unitCost) / $this->unit_price) * 100, 2) : 0;
            }),
            
            // Prescription information
            'is_prescription_item' => $this->prescription_item_id !== null,
            'prescription_required' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->is_prescription_required ?? false;
            }),
            'controlled_substance' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->is_controlled_substance ?? false;
            }),
            
            // Expiry information
            'days_to_expiry' => $this->expiry_date ? now()->diffInDays($this->expiry_date, false) : null,
            'is_expired' => $this->expiry_date ? now()->isAfter($this->expiry_date) : false,
            'is_near_expiry' => $this->expiry_date ? 
                now()->addDays(30)->isAfter($this->expiry_date) && now()->isBefore($this->expiry_date) : false,
            
            // Product information
            'product_name' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->name;
            }),
            'product_sku' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->sku;
            }),
            'product_barcode' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->barcode;
            }),
            'variant_name' => $this->when($this->relationLoaded('productVariant'), function () {
                return $this->productVariant->variant_value;
            }),
            
            // Return/refund information
            'can_be_returned' => $this->canBeReturned(),
            'return_deadline' => $this->getReturnDeadline(),
            'is_returnable' => $this->isReturnable(),
            
            // Relationships
            'sale' => new SaleResource($this->whenLoaded('sale')),
            'product' => new ProductResource($this->whenLoaded('product')),
            'product_variant' => new ProductVariantResource($this->whenLoaded('productVariant')),
            'inventory' => new InventoryResource($this->whenLoaded('inventory')),
            'prescription_item' => new PrescriptionItemResource($this->whenLoaded('prescriptionItem')),
            
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
    
    /**
     * Check if item can be returned
     */
    private function canBeReturned(): bool
    {
        // Check if sale is completed and not refunded
        if ($this->relationLoaded('sale')) {
            if (!in_array($this->sale->payment_status, ['paid', 'partial'])) {
                return false;
            }
        }
        
        // Check if within return period (30 days)
        if ($this->created_at && now()->diffInDays($this->created_at) > 30) {
            return false;
        }
        
        // Check if prescription item (usually non-returnable)
        if ($this->prescription_item_id) {
            return false;
        }
        
        // Check if controlled substance (non-returnable)
        if ($this->relationLoaded('product') && $this->product->is_controlled_substance) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Get return deadline
     */
    private function getReturnDeadline(): ?string
    {
        if (!$this->canBeReturned()) {
            return null;
        }
        
        return $this->created_at?->addDays(30)->toDateString();
    }
    
    /**
     * Check if item is returnable
     */
    private function isReturnable(): bool
    {
        return $this->canBeReturned();
    }
}
