<?php

namespace App\Http\Resources\Api\V1;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PrescriptionItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'prescription_id' => $this->prescription_id,
            'product_id' => $this->product_id,
            'product_variant_id' => $this->product_variant_id,
            'quantity_prescribed' => $this->quantity_prescribed,
            'quantity_dispensed' => $this->quantity_dispensed,
            'dosage' => $this->dosage,
            'frequency' => $this->frequency,
            'frequency_display' => $this->getFrequencyDisplayAttribute(),
            'duration' => $this->duration,
            'duration_unit' => $this->duration_unit,
            'instructions' => $this->instructions,
            'generic_substitution_allowed' => $this->generic_substitution_allowed,
            'brand_required' => $this->brand_required,
            'notes' => $this->notes,
            
            // Calculated fields
            'quantity_remaining' => max(0, $this->quantity_prescribed - $this->quantity_dispensed),
            'is_fully_dispensed' => $this->quantity_dispensed >= $this->quantity_prescribed,
            'dispensing_percentage' => $this->quantity_prescribed > 0 ? 
                round(($this->quantity_dispensed / $this->quantity_prescribed) * 100, 2) : 0,
            'can_be_dispensed' => $this->quantity_dispensed < $this->quantity_prescribed,
            
            // Dosage calculations
            'total_daily_dose' => $this->getTotalDailyDose(),
            'duration_in_days' => $this->getDurationInDays(),
            'total_course_quantity' => $this->getTotalCourseQuantity(),
            'days_supply' => $this->getDaysSupply(),
            
            // Product information
            'product_name' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->name;
            }),
            'product_strength' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->strength;
            }),
            'product_form' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->dosage_form;
            }),
            'is_controlled_substance' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->is_controlled_substance ?? false;
            }),
            'requires_prescription' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->is_prescription_required ?? false;
            }),
            
            // Generic substitution
            'generic_available' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->generic_products()->exists();
            }),
            'brand_available' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->brand_products()->exists();
            }),
            'substitution_options' => $this->when($this->relationLoaded('product'), function () {
                if ($this->generic_substitution_allowed) {
                    return $this->product->generic_products()->count() + 
                           $this->product->brand_products()->count();
                }
                return 0;
            }),
            
            // Safety information
            'drug_class' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->drug_class;
            }),
            'therapeutic_class' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->therapeutic_class;
            }),
            'contraindications' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->contraindications;
            }),
            'side_effects' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->side_effects;
            }),
            
            // Dispensing information
            'last_dispensed_date' => $this->when($this->relationLoaded('saleItems'), function () {
                return $this->saleItems->latest('created_at')->first()?->created_at;
            }),
            'times_dispensed' => $this->when($this->relationLoaded('saleItems'), function () {
                return $this->saleItems->count();
            }),
            'total_dispensed_amount' => $this->when($this->relationLoaded('saleItems'), function () {
                return $this->saleItems->sum('total_amount');
            }),
            
            // Compliance tracking
            'adherence_score' => $this->getAdherenceScore(),
            'compliance_alerts' => $this->getComplianceAlerts(),
            'refill_due_date' => $this->getRefillDueDate(),
            'is_overdue_refill' => $this->isOverdueRefill(),
            
            // Inventory availability
            'stock_available' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->inventory()->sum('quantity_available');
            }),
            'sufficient_stock' => $this->when($this->relationLoaded('product'), function () {
                $available = $this->product->inventory()->sum('quantity_available');
                return $available >= $this->quantity_remaining;
            }),
            'estimated_cost' => $this->when($this->relationLoaded('product'), function () {
                return $this->quantity_remaining * ($this->product->selling_price ?? 0);
            }),
            
            // Special handling
            'requires_counseling' => $this->requiresCounseling(),
            'requires_monitoring' => $this->requiresMonitoring(),
            'storage_requirements' => $this->when($this->relationLoaded('product'), function () {
                return $this->product->storage_requirements;
            }),
            
            // Relationships
            'prescription' => new PrescriptionResource($this->whenLoaded('prescription')),
            'product' => new ProductResource($this->whenLoaded('product')),
            'product_variant' => new ProductVariantResource($this->whenLoaded('productVariant')),
            'sale_items' => SaleItemResource::collection($this->whenLoaded('saleItems')),
            
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
    
    /**
     * Get total daily dose
     */
    private function getTotalDailyDose(): ?float
    {
        if (!$this->dosage || !$this->frequency) {
            return null;
        }
        
        // Extract numeric values from dosage and frequency
        preg_match('/(\d+(?:\.\d+)?)/', $this->dosage, $dosageMatches);
        preg_match('/(\d+)/', $this->frequency, $frequencyMatches);
        
        if (empty($dosageMatches) || empty($frequencyMatches)) {
            return null;
        }
        
        $dosageAmount = (float) $dosageMatches[1];
        $frequencyPerDay = (int) $frequencyMatches[1];
        
        return $dosageAmount * $frequencyPerDay;
    }
    
    /**
     * Get duration in days
     */
    private function getDurationInDays(): ?int
    {
        if (!$this->duration || !$this->duration_unit) {
            return null;
        }
        
        $duration = (int) $this->duration;
        
        switch (strtolower($this->duration_unit)) {
            case 'days':
            case 'day':
                return $duration;
            case 'weeks':
            case 'week':
                return $duration * 7;
            case 'months':
            case 'month':
                return $duration * 30;
            case 'years':
            case 'year':
                return $duration * 365;
            default:
                return null;
        }
    }
    
    /**
     * Get total course quantity
     */
    private function getTotalCourseQuantity(): ?float
    {
        $dailyDose = $this->getTotalDailyDose();
        $durationDays = $this->getDurationInDays();
        
        if ($dailyDose && $durationDays) {
            return $dailyDose * $durationDays;
        }
        
        return null;
    }
    
    /**
     * Get days supply based on current dispensed quantity
     */
    private function getDaysSupply(): ?int
    {
        $dailyDose = $this->getTotalDailyDose();
        
        if ($dailyDose && $dailyDose > 0) {
            return (int) floor($this->quantity_dispensed / $dailyDose);
        }
        
        return null;
    }
    
    /**
     * Get adherence score
     */
    private function getAdherenceScore(): int
    {
        // Simplified adherence calculation
        if ($this->quantity_prescribed == 0) {
            return 0;
        }
        
        $expectedDispensed = $this->quantity_prescribed;
        $actualDispensed = $this->quantity_dispensed;
        
        return min(100, (int) round(($actualDispensed / $expectedDispensed) * 100));
    }
    
    /**
     * Get compliance alerts
     */
    private function getComplianceAlerts(): array
    {
        $alerts = [];
        
        if ($this->quantity_dispensed > $this->quantity_prescribed) {
            $alerts[] = 'Quantity dispensed exceeds prescribed amount';
        }
        
        if ($this->relationLoaded('product') && $this->product->is_controlled_substance) {
            $alerts[] = 'Controlled substance - verify DEA requirements';
        }
        
        if ($this->brand_required && $this->generic_substitution_allowed) {
            $alerts[] = 'Conflicting substitution requirements';
        }
        
        return $alerts;
    }
    
    /**
     * Get refill due date
     */
    private function getRefillDueDate(): ?string
    {
        $daysSupply = $this->getDaysSupply();
        
        if ($daysSupply && $this->relationLoaded('saleItems')) {
            $lastDispensed = $this->saleItems->latest('created_at')->first();
            if ($lastDispensed) {
                // Allow refill when 75% of supply is used
                $refillDays = (int) ($daysSupply * 0.75);
                return $lastDispensed->created_at->addDays($refillDays)->toDateString();
            }
        }
        
        return null;
    }
    
    /**
     * Check if refill is overdue
     */
    private function isOverdueRefill(): bool
    {
        $refillDueDate = $this->getRefillDueDate();
        
        if ($refillDueDate && $this->quantity_dispensed < $this->quantity_prescribed) {
            return now()->isAfter($refillDueDate);
        }
        
        return false;
    }
    
    /**
     * Check if requires counseling
     */
    private function requiresCounseling(): bool
    {
        if ($this->relationLoaded('product')) {
            return $this->product->is_controlled_substance || 
                   $this->product->requires_counseling ||
                   !empty($this->product->black_box_warning);
        }
        
        return false;
    }
    
    /**
     * Check if requires monitoring
     */
    private function requiresMonitoring(): bool
    {
        if ($this->relationLoaded('product')) {
            return $this->product->requires_monitoring ||
                   in_array($this->product->therapeutic_class, [
                       'anticoagulants', 'antiarrhythmics', 'immunosuppressants'
                   ]);
        }
        
        return false;
    }
}
