<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'role' => $this->role,
            'role_display_name' => $this->role_display_name,
            'phone' => $this->phone,
            'address' => $this->address,
            'employee_id' => $this->employee_id,
            'salary' => $this->when($request->user()->isAdmin(), $this->salary),
            'hire_date' => $this->hire_date,
            'avatar_url' => $this->avatar_url,
            'location_id' => $this->location_id,
            'location' => new LocationResource($this->whenLoaded('location')),
            'emergency_contact_name' => $this->emergency_contact_name,
            'emergency_contact_phone' => $this->emergency_contact_phone,
            'notes' => $this->when($request->user()->isAdmin(), $this->notes),
            'permissions' => $this->permissions,
            'is_active' => $this->is_active,
            'last_login_at' => $this->last_login_at,
            'login_count' => $this->login_count,
            'failed_login_attempts' => $this->when($request->user()->isAdmin(), $this->failed_login_attempts),
            'locked_until' => $this->when($request->user()->isAdmin(), $this->locked_until),
            'password_changed_at' => $this->password_changed_at,
            'must_change_password' => $this->needsPasswordChange(),
            'two_factor_enabled' => $this->two_factor_enabled,
            'account_status' => $this->getAccountStatus(),
            'recent_activities' => ActivityLogResource::collection($this->whenLoaded('activityLogs')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * Get account status.
     */
    private function getAccountStatus(): string
    {
        if (!$this->is_active) {
            return 'deactivated';
        }

        if ($this->isLocked()) {
            return 'locked';
        }

        if ($this->needsPasswordChange()) {
            return 'password_change_required';
        }

        return 'active';
    }
}
