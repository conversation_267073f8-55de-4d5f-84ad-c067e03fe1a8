<?php

namespace App\Http\Requests;

use App\Rules\StrongPassword;
use App\Rules\ValidEmployeeId;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'min:2',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/', // Only letters, spaces, hyphens, apostrophes, and dots
            ],
            'email' => [
                'required',
                'string',
                'email:rfc,dns',
                'max:255',
                'unique:users,email',
                'regex:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
            ],
            'password' => [
                'required',
                'confirmed',
                new StrongPassword(),
            ],
            'role' => [
                'required',
                Rule::in(['admin', 'pharmacist', 'cashier']),
            ],
            'phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/', // Phone number format
            ],
            'address' => [
                'nullable',
                'string',
                'max:500',
                'min:10',
            ],
            'employee_id' => [
                'required',
                'string',
                'unique:users,employee_id',
                new ValidEmployeeId(),
            ],
            'salary' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999.99',
            ],
            'hire_date' => [
                'nullable',
                'date',
                'before_or_equal:today',
                'after:1900-01-01',
            ],
            'location_id' => [
                'required',
                'integer',
                'exists:locations,id',
            ],
            'emergency_contact_name' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'emergency_contact_phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/',
            ],
            'notes' => [
                'nullable',
                'string',
                'max:1000',
            ],
            'permissions' => [
                'nullable',
                'array',
                'max:50', // Maximum 50 permissions
            ],
            'permissions.*' => [
                'string',
                'max:100',
            ],
            'must_change_password' => [
                'boolean',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.regex' => 'The name may only contain letters, spaces, hyphens, apostrophes, and dots.',
            'email.email' => 'The email must be a valid email address.',
            'email.regex' => 'The email format is invalid.',
            'phone.regex' => 'The phone number format is invalid.',
            'address.min' => 'The address must be at least 10 characters.',
            'emergency_contact_name.regex' => 'The emergency contact name may only contain letters, spaces, hyphens, apostrophes, and dots.',
            'emergency_contact_phone.regex' => 'The emergency contact phone number format is invalid.',
            'hire_date.before_or_equal' => 'The hire date cannot be in the future.',
            'hire_date.after' => 'The hire date must be after 1900.',
            'salary.max' => 'The salary cannot exceed 999,999.99.',
            'permissions.max' => 'Cannot assign more than 50 permissions.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'employee_id' => 'employee ID',
            'location_id' => 'location',
            'emergency_contact_name' => 'emergency contact name',
            'emergency_contact_phone' => 'emergency contact phone',
            'hire_date' => 'hire date',
            'must_change_password' => 'must change password flag',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check if emergency contact phone is provided when emergency contact name is provided
            if ($this->filled('emergency_contact_name') && !$this->filled('emergency_contact_phone')) {
                $validator->errors()->add('emergency_contact_phone', 'Emergency contact phone is required when emergency contact name is provided.');
            }

            // Check if emergency contact name is provided when emergency contact phone is provided
            if ($this->filled('emergency_contact_phone') && !$this->filled('emergency_contact_name')) {
                $validator->errors()->add('emergency_contact_name', 'Emergency contact name is required when emergency contact phone is provided.');
            }

            // Validate role-based permissions
            if ($this->filled('permissions')) {
                $this->validateRolePermissions($validator);
            }

            // Check if user can assign this role
            if ($this->filled('role')) {
                $this->validateRoleAssignment($validator);
            }
        });
    }

    /**
     * Validate role-based permissions.
     */
    private function validateRolePermissions($validator): void
    {
        $role = $this->input('role');
        $permissions = $this->input('permissions', []);

        $allowedPermissions = $this->getAllowedPermissionsForRole($role);

        foreach ($permissions as $permission) {
            if (!in_array($permission, $allowedPermissions)) {
                $validator->errors()->add('permissions', "Permission '{$permission}' is not allowed for role '{$role}'.");
            }
        }
    }

    /**
     * Validate role assignment.
     */
    private function validateRoleAssignment($validator): void
    {
        $requestedRole = $this->input('role');
        $currentUser = $this->user();

        // Only admins can create other admins
        if ($requestedRole === 'admin' && !$currentUser->isAdmin()) {
            $validator->errors()->add('role', 'Only administrators can create admin users.');
        }
    }

    /**
     * Get allowed permissions for a role.
     */
    private function getAllowedPermissionsForRole(string $role): array
    {
        $permissions = [
            'admin' => [
                'manage_users', 'manage_products', 'manage_inventory', 'manage_sales',
                'manage_purchases', 'manage_suppliers', 'manage_customers', 'view_reports',
                'manage_locations', 'manage_categories', 'process_refunds', 'adjust_stock',
                'view_analytics', 'export_data', 'manage_settings'
            ],
            'pharmacist' => [
                'manage_products', 'manage_inventory', 'manage_sales', 'manage_purchases',
                'manage_suppliers', 'manage_customers', 'view_reports', 'process_refunds',
                'adjust_stock', 'view_analytics'
            ],
            'cashier' => [
                'manage_sales', 'manage_customers', 'view_reports'
            ],
        ];

        return $permissions[$role] ?? [];
    }
}
