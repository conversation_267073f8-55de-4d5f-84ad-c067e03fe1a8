<?php

namespace App\Http\Requests;

use App\Rules\ValidEmployeeId;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $targetUser = $this->route('user');
        $currentUser = $this->user();

        // Admins can update any user
        if ($currentUser->isAdmin()) {
            return true;
        }

        // Users can only update users from their location
        return $targetUser->location_id === $currentUser->location_id;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $userId = $this->route('user')->id;

        return [
            'name' => [
                'required',
                'string',
                'min:2',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'email' => [
                'required',
                'string',
                'email:rfc,dns',
                'max:255',
                Rule::unique('users', 'email')->ignore($userId),
                'regex:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
            ],
            'role' => [
                'required',
                Rule::in(['admin', 'pharmacist', 'cashier']),
            ],
            'phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/',
            ],
            'address' => [
                'nullable',
                'string',
                'max:500',
                'min:10',
            ],
            'employee_id' => [
                'required',
                'string',
                Rule::unique('users', 'employee_id')->ignore($userId),
                new ValidEmployeeId(),
            ],
            'salary' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999.99',
            ],
            'hire_date' => [
                'nullable',
                'date',
                'before_or_equal:today',
                'after:1900-01-01',
            ],
            'location_id' => [
                'required',
                'integer',
                'exists:locations,id',
            ],
            'emergency_contact_name' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'emergency_contact_phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/',
            ],
            'notes' => [
                'nullable',
                'string',
                'max:1000',
            ],
            'permissions' => [
                'nullable',
                'array',
                'max:50',
            ],
            'permissions.*' => [
                'string',
                'max:100',
            ],
            'is_active' => [
                'boolean',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.regex' => 'The name may only contain letters, spaces, hyphens, apostrophes, and dots.',
            'email.email' => 'The email must be a valid email address.',
            'email.regex' => 'The email format is invalid.',
            'phone.regex' => 'The phone number format is invalid.',
            'address.min' => 'The address must be at least 10 characters.',
            'emergency_contact_name.regex' => 'The emergency contact name may only contain letters, spaces, hyphens, apostrophes, and dots.',
            'emergency_contact_phone.regex' => 'The emergency contact phone number format is invalid.',
            'hire_date.before_or_equal' => 'The hire date cannot be in the future.',
            'hire_date.after' => 'The hire date must be after 1900.',
            'salary.max' => 'The salary cannot exceed 999,999.99.',
            'permissions.max' => 'Cannot assign more than 50 permissions.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'employee_id' => 'employee ID',
            'location_id' => 'location',
            'emergency_contact_name' => 'emergency contact name',
            'emergency_contact_phone' => 'emergency contact phone',
            'hire_date' => 'hire date',
            'is_active' => 'active status',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $targetUser = $this->route('user');
            $currentUser = $this->user();

            // Prevent self-deactivation
            if ($this->has('is_active') && !$this->boolean('is_active') && $targetUser->id === $currentUser->id) {
                $validator->errors()->add('is_active', 'You cannot deactivate your own account.');
            }

            // Check emergency contact validation
            if ($this->filled('emergency_contact_name') && !$this->filled('emergency_contact_phone')) {
                $validator->errors()->add('emergency_contact_phone', 'Emergency contact phone is required when emergency contact name is provided.');
            }

            if ($this->filled('emergency_contact_phone') && !$this->filled('emergency_contact_name')) {
                $validator->errors()->add('emergency_contact_name', 'Emergency contact name is required when emergency contact phone is provided.');
            }

            // Validate role-based permissions
            if ($this->filled('permissions')) {
                $this->validateRolePermissions($validator);
            }

            // Check role change permissions
            if ($this->filled('role') && $this->input('role') !== $targetUser->role) {
                $this->validateRoleChange($validator, $targetUser);
            }

            // Check location change permissions
            if ($this->filled('location_id') && $this->input('location_id') != $targetUser->location_id) {
                $this->validateLocationChange($validator, $currentUser);
            }
        });
    }

    /**
     * Validate role-based permissions.
     */
    private function validateRolePermissions($validator): void
    {
        $role = $this->input('role');
        $permissions = $this->input('permissions', []);

        $allowedPermissions = $this->getAllowedPermissionsForRole($role);

        foreach ($permissions as $permission) {
            if (!in_array($permission, $allowedPermissions)) {
                $validator->errors()->add('permissions', "Permission '{$permission}' is not allowed for role '{$role}'.");
            }
        }
    }

    /**
     * Validate role change.
     */
    private function validateRoleChange($validator, $targetUser): void
    {
        $newRole = $this->input('role');
        $currentUser = $this->user();

        // Only admins can change roles to/from admin
        if (($newRole === 'admin' || $targetUser->role === 'admin') && !$currentUser->isAdmin()) {
            $validator->errors()->add('role', 'Only administrators can change admin roles.');
        }

        // Prevent self-role change from admin
        if ($targetUser->id === $currentUser->id && $targetUser->role === 'admin' && $newRole !== 'admin') {
            $validator->errors()->add('role', 'You cannot change your own admin role.');
        }
    }

    /**
     * Validate location change.
     */
    private function validateLocationChange($validator, $currentUser): void
    {
        // Only admins can change user locations
        if (!$currentUser->isAdmin()) {
            $validator->errors()->add('location_id', 'Only administrators can change user locations.');
        }
    }

    /**
     * Get allowed permissions for a role.
     */
    private function getAllowedPermissionsForRole(string $role): array
    {
        $permissions = [
            'admin' => [
                'manage_users', 'manage_products', 'manage_inventory', 'manage_sales',
                'manage_purchases', 'manage_suppliers', 'manage_customers', 'view_reports',
                'manage_locations', 'manage_categories', 'process_refunds', 'adjust_stock',
                'view_analytics', 'export_data', 'manage_settings'
            ],
            'pharmacist' => [
                'manage_products', 'manage_inventory', 'manage_sales', 'manage_purchases',
                'manage_suppliers', 'manage_customers', 'view_reports', 'process_refunds',
                'adjust_stock', 'view_analytics'
            ],
            'cashier' => [
                'manage_sales', 'manage_customers', 'view_reports'
            ],
        ];

        return $permissions[$role] ?? [];
    }
}
