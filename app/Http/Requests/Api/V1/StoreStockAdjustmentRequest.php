<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreStockAdjustmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'pharmacist']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'inventory_id' => 'required|exists:inventory,id',
            'adjustment_type' => [
                'required',
                'string',
                'in:increase,decrease,correction,damage,expiry,theft,return'
            ],
            'quantity_adjusted' => 'required|integer|not_in:0',
            'reason' => 'required|string|max:500',
            'reference_number' => 'nullable|string|max:100|unique:stock_adjustments,reference_number',
            'cost_impact' => 'nullable|numeric',
            'approved_by' => 'nullable|exists:users,id',
            'approved_at' => 'nullable|date|before_or_equal:now',
            'notes' => 'nullable|string|max:1000',
            'metadata' => 'nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'inventory_id.required' => 'Inventory item is required.',
            'inventory_id.exists' => 'Selected inventory item does not exist.',
            'adjustment_type.required' => 'Adjustment type is required.',
            'adjustment_type.in' => 'Adjustment type must be one of: increase, decrease, correction, damage, expiry, theft, return.',
            'quantity_adjusted.required' => 'Quantity adjusted is required.',
            'quantity_adjusted.not_in' => 'Quantity adjusted cannot be zero.',
            'reason.required' => 'Reason for adjustment is required.',
            'reason.max' => 'Reason may not be greater than 500 characters.',
            'reference_number.max' => 'Reference number may not be greater than 100 characters.',
            'reference_number.unique' => 'This reference number already exists.',
            'cost_impact.numeric' => 'Cost impact must be a number.',
            'approved_by.exists' => 'Selected approver does not exist.',
            'approved_at.date' => 'Approval date must be a valid date.',
            'approved_at.before_or_equal' => 'Approval date cannot be in the future.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'inventory_id' => 'inventory item',
            'adjustment_type' => 'adjustment type',
            'quantity_adjusted' => 'quantity adjusted',
            'reference_number' => 'reference number',
            'cost_impact' => 'cost impact',
            'approved_by' => 'approved by',
            'approved_at' => 'approval date',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        $this->merge([
            'reference_number' => $this->reference_number === '' ? null : $this->reference_number,
            'cost_impact' => $this->cost_impact === '' ? null : $this->cost_impact,
            'approved_by' => $this->approved_by === '' ? null : $this->approved_by,
            'approved_at' => $this->approved_at === '' ? null : $this->approved_at,
            'notes' => $this->notes === '' ? null : $this->notes,
            'metadata' => $this->metadata === '' ? null : $this->metadata,
        ]);

        // Auto-generate reference number if not provided
        if (!$this->reference_number) {
            $this->merge(['reference_number' => 'ADJ-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6))]);
        }

        // Set current user as adjuster
        $this->merge(['adjusted_by' => $this->user()->id]);

        // Convert quantity to negative for decrease adjustments
        if (in_array($this->adjustment_type, ['decrease', 'damage', 'expiry', 'theft']) && $this->quantity_adjusted > 0) {
            $this->merge(['quantity_adjusted' => -abs($this->quantity_adjusted)]);
        }

        // Ensure positive quantity for increase adjustments
        if (in_array($this->adjustment_type, ['increase', 'return']) && $this->quantity_adjusted < 0) {
            $this->merge(['quantity_adjusted' => abs($this->quantity_adjusted)]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate inventory exists and is active
            if ($this->inventory_id) {
                $inventory = \App\Models\Inventory::find($this->inventory_id);
                
                if (!$inventory) {
                    $validator->errors()->add('inventory_id', 'Selected inventory item does not exist.');
                    return;
                }

                if ($inventory->status !== 'active') {
                    $validator->errors()->add('inventory_id', 'Cannot adjust inactive inventory.');
                }

                // Validate quantity constraints for decrease adjustments
                if (in_array($this->adjustment_type, ['decrease', 'damage', 'expiry', 'theft'])) {
                    $adjustmentQuantity = abs($this->quantity_adjusted);
                    
                    if ($adjustmentQuantity > $inventory->quantity_available) {
                        $validator->errors()->add('quantity_adjusted', 
                            'Cannot adjust more than available quantity (' . $inventory->quantity_available . ').');
                    }
                }

                // Validate reasonable quantity limits
                $maxAdjustment = 999999;
                if (abs($this->quantity_adjusted) > $maxAdjustment) {
                    $validator->errors()->add('quantity_adjusted', 
                        'Adjustment quantity cannot exceed ' . number_format($maxAdjustment) . '.');
                }
            }

            // Validate approval logic
            if ($this->approved_by || $this->approved_at) {
                // Both approved_by and approved_at should be provided together
                if (!$this->approved_by || !$this->approved_at) {
                    $validator->errors()->add('approved_by', 'Both approver and approval date are required for approval.');
                }

                // Validate approver has permission
                if ($this->approved_by) {
                    $approver = \App\Models\User::find($this->approved_by);
                    if ($approver && !$approver->hasAnyRole(['admin', 'pharmacist'])) {
                        $validator->errors()->add('approved_by', 'Selected user does not have permission to approve adjustments.');
                    }
                }

                // Cannot approve own adjustment (unless admin)
                if ($this->approved_by === $this->user()->id && !$this->user()->hasRole('admin')) {
                    $validator->errors()->add('approved_by', 'Cannot approve your own adjustment.');
                }
            }

            // Validate cost impact calculation
            if ($this->cost_impact !== null && $this->inventory_id) {
                $inventory = \App\Models\Inventory::find($this->inventory_id);
                if ($inventory) {
                    $expectedCostImpact = abs($this->quantity_adjusted) * $inventory->cost_price;
                    $tolerance = 0.01; // 1 cent tolerance
                    
                    if (abs($this->cost_impact - $expectedCostImpact) > $tolerance) {
                        $validator->errors()->add('cost_impact', 
                            'Cost impact does not match expected value (' . number_format($expectedCostImpact, 2) . ').');
                    }
                }
            }

            // Validate adjustment type specific rules
            switch ($this->adjustment_type) {
                case 'expiry':
                    if ($this->inventory_id) {
                        $inventory = \App\Models\Inventory::find($this->inventory_id);
                        if ($inventory && !$inventory->isExpiringSoon(30)) {
                            $validator->errors()->add('adjustment_type', 
                                'Expiry adjustment should only be used for items expiring within 30 days.');
                        }
                    }
                    break;

                case 'return':
                    // Return adjustments should have a reference to original sale/issue
                    if (!$this->reference_number || !str_contains(strtolower($this->reference_number), 'ret')) {
                        $validator->errors()->add('reference_number', 
                            'Return adjustments should include return reference number.');
                    }
                    break;

                case 'correction':
                    // Correction adjustments should have detailed reason
                    if (strlen($this->reason) < 20) {
                        $validator->errors()->add('reason', 
                            'Correction adjustments require detailed reason (minimum 20 characters).');
                    }
                    break;
            }

            // Validate metadata structure
            if ($this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }

            // Check if adjustment requires approval based on amount
            $requiresApproval = false;
            if ($this->inventory_id && abs($this->quantity_adjusted) > 0) {
                $inventory = \App\Models\Inventory::find($this->inventory_id);
                if ($inventory) {
                    $adjustmentValue = abs($this->quantity_adjusted) * $inventory->cost_price;
                    
                    // Require approval for adjustments over $1000 or high-value items
                    if ($adjustmentValue > 1000 || in_array($this->adjustment_type, ['theft', 'damage'])) {
                        $requiresApproval = true;
                    }
                }
            }

            // Add warning for high-value adjustments without approval
            if ($requiresApproval && !$this->approved_by) {
                $validator->errors()->add('approved_by', 
                    'This adjustment requires approval due to high value or adjustment type.');
            }
        });
    }
}
