<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'pharmacist', 'cashier']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'customer_code' => [
                'nullable',
                'string',
                'max:50',
                'regex:/^[A-Z0-9-]+$/',
                'unique:customers,customer_code'
            ],
            'first_name' => 'required|string|max:100',
            'last_name' => 'required|string|max:100',
            'email' => 'nullable|email|max:255|unique:customers,email',
            'phone' => [
                'required',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/',
                'unique:customers,phone'
            ],
            'mobile' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/'
            ],
            'date_of_birth' => 'nullable|date|before:today|after:1900-01-01',
            'gender' => 'nullable|string|in:male,female,other',
            'address_line_1' => 'nullable|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[A-Z0-9\s\-]+$/i'
            ],
            'country' => 'nullable|string|max:100',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/'
            ],
            'insurance_provider' => 'nullable|string|max:255',
            'insurance_number' => 'nullable|string|max:100',
            'allergies' => 'nullable|string|max:1000',
            'medical_conditions' => 'nullable|string|max:1000',
            'preferred_doctor' => 'nullable|string|max:255',
            'loyalty_program_id' => 'nullable|exists:loyalty_programs,id',
            'loyalty_points' => 'nullable|integer|min:0|max:999999',
            'credit_limit' => 'nullable|numeric|min:0|max:99999.99',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'is_active' => 'boolean',
            'notes' => 'nullable|string|max:1000',
            'metadata' => 'nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'customer_code.regex' => 'Customer code must contain only uppercase letters, numbers, and hyphens.',
            'customer_code.unique' => 'This customer code already exists.',
            'first_name.required' => 'First name is required.',
            'first_name.max' => 'First name may not be greater than 100 characters.',
            'last_name.required' => 'Last name is required.',
            'last_name.max' => 'Last name may not be greater than 100 characters.',
            'email.email' => 'Please provide a valid email address.',
            'email.unique' => 'This email address already exists.',
            'phone.required' => 'Phone number is required.',
            'phone.regex' => 'Please provide a valid phone number.',
            'phone.unique' => 'This phone number already exists.',
            'mobile.regex' => 'Please provide a valid mobile number.',
            'date_of_birth.date' => 'Date of birth must be a valid date.',
            'date_of_birth.before' => 'Date of birth must be before today.',
            'date_of_birth.after' => 'Date of birth must be after 1900.',
            'gender.in' => 'Gender must be one of: male, female, other.',
            'address_line_1.max' => 'Address line 1 may not be greater than 255 characters.',
            'address_line_2.max' => 'Address line 2 may not be greater than 255 characters.',
            'city.max' => 'City may not be greater than 100 characters.',
            'state.max' => 'State may not be greater than 100 characters.',
            'postal_code.regex' => 'Please provide a valid postal code.',
            'country.max' => 'Country may not be greater than 100 characters.',
            'emergency_contact_name.max' => 'Emergency contact name may not be greater than 255 characters.',
            'emergency_contact_phone.regex' => 'Please provide a valid emergency contact phone number.',
            'insurance_provider.max' => 'Insurance provider may not be greater than 255 characters.',
            'insurance_number.max' => 'Insurance number may not be greater than 100 characters.',
            'allergies.max' => 'Allergies may not be greater than 1000 characters.',
            'medical_conditions.max' => 'Medical conditions may not be greater than 1000 characters.',
            'preferred_doctor.max' => 'Preferred doctor may not be greater than 255 characters.',
            'loyalty_program_id.exists' => 'Selected loyalty program does not exist.',
            'loyalty_points.min' => 'Loyalty points must be at least 0.',
            'loyalty_points.max' => 'Loyalty points may not be greater than 999,999.',
            'credit_limit.min' => 'Credit limit must be at least 0.',
            'credit_limit.max' => 'Credit limit may not be greater than 99,999.99.',
            'discount_percentage.min' => 'Discount percentage must be at least 0.',
            'discount_percentage.max' => 'Discount percentage may not be greater than 100.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'customer_code' => 'customer code',
            'first_name' => 'first name',
            'last_name' => 'last name',
            'date_of_birth' => 'date of birth',
            'address_line_1' => 'address line 1',
            'address_line_2' => 'address line 2',
            'postal_code' => 'postal code',
            'emergency_contact_name' => 'emergency contact name',
            'emergency_contact_phone' => 'emergency contact phone',
            'insurance_provider' => 'insurance provider',
            'insurance_number' => 'insurance number',
            'medical_conditions' => 'medical conditions',
            'preferred_doctor' => 'preferred doctor',
            'loyalty_program_id' => 'loyalty program',
            'loyalty_points' => 'loyalty points',
            'credit_limit' => 'credit limit',
            'discount_percentage' => 'discount percentage',
            'is_active' => 'active status',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        $this->merge([
            'customer_code' => $this->customer_code === '' ? null : $this->customer_code,
            'email' => $this->email === '' ? null : $this->email,
            'mobile' => $this->mobile === '' ? null : $this->mobile,
            'date_of_birth' => $this->date_of_birth === '' ? null : $this->date_of_birth,
            'gender' => $this->gender === '' ? null : $this->gender,
            'address_line_1' => $this->address_line_1 === '' ? null : $this->address_line_1,
            'address_line_2' => $this->address_line_2 === '' ? null : $this->address_line_2,
            'city' => $this->city === '' ? null : $this->city,
            'state' => $this->state === '' ? null : $this->state,
            'postal_code' => $this->postal_code === '' ? null : $this->postal_code,
            'country' => $this->country === '' ? null : $this->country,
            'emergency_contact_name' => $this->emergency_contact_name === '' ? null : $this->emergency_contact_name,
            'emergency_contact_phone' => $this->emergency_contact_phone === '' ? null : $this->emergency_contact_phone,
            'insurance_provider' => $this->insurance_provider === '' ? null : $this->insurance_provider,
            'insurance_number' => $this->insurance_number === '' ? null : $this->insurance_number,
            'allergies' => $this->allergies === '' ? null : $this->allergies,
            'medical_conditions' => $this->medical_conditions === '' ? null : $this->medical_conditions,
            'preferred_doctor' => $this->preferred_doctor === '' ? null : $this->preferred_doctor,
            'loyalty_program_id' => $this->loyalty_program_id === '' ? null : $this->loyalty_program_id,
            'loyalty_points' => $this->loyalty_points === '' ? null : $this->loyalty_points,
            'credit_limit' => $this->credit_limit === '' ? null : $this->credit_limit,
            'discount_percentage' => $this->discount_percentage === '' ? null : $this->discount_percentage,
            'notes' => $this->notes === '' ? null : $this->notes,
            'metadata' => $this->metadata === '' ? null : $this->metadata,
        ]);

        // Set default values
        if (!$this->has('is_active')) {
            $this->merge(['is_active' => true]);
        }

        if (!$this->has('loyalty_points')) {
            $this->merge(['loyalty_points' => 0]);
        }

        // Auto-generate customer code if not provided
        if (!$this->customer_code) {
            $this->merge(['customer_code' => 'CUST-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6))]);
        }

        // Normalize phone numbers
        if ($this->phone) {
            $this->merge(['phone' => preg_replace('/[^\+0-9]/', '', $this->phone)]);
        }

        if ($this->mobile) {
            $this->merge(['mobile' => preg_replace('/[^\+0-9]/', '', $this->mobile)]);
        }

        if ($this->emergency_contact_phone) {
            $this->merge(['emergency_contact_phone' => preg_replace('/[^\+0-9]/', '', $this->emergency_contact_phone)]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate age constraints
            if ($this->date_of_birth) {
                $age = \Carbon\Carbon::parse($this->date_of_birth)->age;
                
                if ($age < 0) {
                    $validator->errors()->add('date_of_birth', 'Invalid date of birth.');
                } elseif ($age > 150) {
                    $validator->errors()->add('date_of_birth', 'Age seems unrealistic. Please verify the date of birth.');
                }
            }

            // Validate phone number uniqueness across phone and mobile
            if ($this->phone && $this->mobile && $this->phone === $this->mobile) {
                $validator->errors()->add('mobile', 'Mobile number cannot be the same as phone number.');
            }

            // Validate emergency contact is different from customer
            if ($this->emergency_contact_phone && 
                ($this->emergency_contact_phone === $this->phone || $this->emergency_contact_phone === $this->mobile)) {
                $validator->errors()->add('emergency_contact_phone', 'Emergency contact phone must be different from customer phone numbers.');
            }

            // Validate loyalty program eligibility
            if ($this->loyalty_program_id) {
                $loyaltyProgram = \App\Models\LoyaltyProgram::find($this->loyalty_program_id);
                if ($loyaltyProgram && !$loyaltyProgram->is_active) {
                    $validator->errors()->add('loyalty_program_id', 'Cannot assign inactive loyalty program.');
                }

                // Check minimum age requirement for loyalty program
                if ($loyaltyProgram && $loyaltyProgram->minimum_age && $this->date_of_birth) {
                    $age = \Carbon\Carbon::parse($this->date_of_birth)->age;
                    if ($age < $loyaltyProgram->minimum_age) {
                        $validator->errors()->add('loyalty_program_id', 'Customer does not meet minimum age requirement for this loyalty program.');
                    }
                }
            }

            // Validate credit limit authorization
            if ($this->credit_limit && $this->credit_limit > 0) {
                if (!$this->user()->hasAnyRole(['admin', 'pharmacist'])) {
                    $validator->errors()->add('credit_limit', 'Only administrators and pharmacists can set credit limits.');
                }

                if ($this->credit_limit > 10000) {
                    $validator->errors()->add('credit_limit', 'Credit limit exceeds maximum allowed amount (10,000).');
                }
            }

            // Validate discount percentage authorization
            if ($this->discount_percentage && $this->discount_percentage > 0) {
                if (!$this->user()->hasAnyRole(['admin', 'pharmacist'])) {
                    $validator->errors()->add('discount_percentage', 'Only administrators and pharmacists can set discount percentages.');
                }

                if ($this->discount_percentage > 50) {
                    $validator->errors()->add('discount_percentage', 'Discount percentage cannot exceed 50%.');
                }
            }

            // Validate insurance information consistency
            if ($this->insurance_provider && !$this->insurance_number) {
                $validator->errors()->add('insurance_number', 'Insurance number is required when insurance provider is specified.');
            }

            if ($this->insurance_number && !$this->insurance_provider) {
                $validator->errors()->add('insurance_provider', 'Insurance provider is required when insurance number is specified.');
            }

            // Validate medical information format
            if ($this->allergies) {
                $allergyKeywords = ['penicillin', 'sulfa', 'aspirin', 'latex', 'shellfish', 'nuts', 'dairy', 'eggs'];
                $allergyText = strtolower($this->allergies);
                $foundAllergies = array_filter($allergyKeywords, function($keyword) use ($allergyText) {
                    return strpos($allergyText, $keyword) !== false;
                });

                if (empty($foundAllergies) && strlen($this->allergies) < 10) {
                    $validator->errors()->add('allergies', 'Please provide specific allergy information or common allergens.');
                }
            }

            // Validate metadata structure
            if ($this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }

            // Validate address completeness
            if ($this->address_line_1 || $this->city || $this->state || $this->postal_code) {
                if (!$this->city) {
                    $validator->errors()->add('city', 'City is required when address information is provided.');
                }
                if (!$this->state) {
                    $validator->errors()->add('state', 'State is required when address information is provided.');
                }
            }

            // Validate name format
            if ($this->first_name && !preg_match('/^[a-zA-Z\s\-\'\.]+$/', $this->first_name)) {
                $validator->errors()->add('first_name', 'First name contains invalid characters.');
            }

            if ($this->last_name && !preg_match('/^[a-zA-Z\s\-\'\.]+$/', $this->last_name)) {
                $validator->errors()->add('last_name', 'Last name contains invalid characters.');
            }
        });
    }
}
