<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class StoreInventoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'pharmacist']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'product_id' => 'required|exists:products,id',
            'product_variant_id' => 'nullable|exists:product_variants,id',
            'location_id' => 'required|exists:locations,id',
            'batch_number' => 'required|string|max:100',
            'quantity_received' => 'required|integer|min:1|max:999999',
            'quantity_available' => 'nullable|integer|min:0|max:999999',
            'cost_price' => 'required|numeric|min:0|max:99999.99',
            'selling_price' => 'required|numeric|min:0|max:99999.99',
            'mrp' => 'required|numeric|min:0|max:99999.99',
            'manufacturing_date' => [
                'required',
                'date',
                'before_or_equal:today',
                'after:1900-01-01'
            ],
            'expiry_date' => [
                'required',
                'date',
                'after:manufacturing_date',
                'after:today'
            ],
            'supplier_id' => 'required|exists:suppliers,id',
            'purchase_order_id' => 'nullable|exists:purchase_orders,id',
            'storage_conditions' => 'nullable|string|max:500',
            'notes' => 'nullable|string|max:1000',
            'status' => [
                'required',
                'string',
                'in:active,expired,damaged,recalled,quarantine'
            ],
            'metadata' => 'nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'product_id.required' => 'Product is required.',
            'product_id.exists' => 'Selected product does not exist.',
            'product_variant_id.exists' => 'Selected product variant does not exist.',
            'location_id.required' => 'Storage location is required.',
            'location_id.exists' => 'Selected location does not exist.',
            'batch_number.required' => 'Batch number is required.',
            'batch_number.max' => 'Batch number may not be greater than 100 characters.',
            'quantity_received.required' => 'Quantity received is required.',
            'quantity_received.min' => 'Quantity received must be at least 1.',
            'quantity_received.max' => 'Quantity received may not be greater than 999,999.',
            'quantity_available.min' => 'Quantity available must be at least 0.',
            'quantity_available.max' => 'Quantity available may not be greater than 999,999.',
            'cost_price.required' => 'Cost price is required.',
            'cost_price.min' => 'Cost price must be at least 0.',
            'cost_price.max' => 'Cost price may not be greater than 99,999.99.',
            'selling_price.required' => 'Selling price is required.',
            'selling_price.min' => 'Selling price must be at least 0.',
            'selling_price.max' => 'Selling price may not be greater than 99,999.99.',
            'mrp.required' => 'MRP is required.',
            'mrp.min' => 'MRP must be at least 0.',
            'mrp.max' => 'MRP may not be greater than 99,999.99.',
            'manufacturing_date.required' => 'Manufacturing date is required.',
            'manufacturing_date.date' => 'Manufacturing date must be a valid date.',
            'manufacturing_date.before_or_equal' => 'Manufacturing date cannot be in the future.',
            'manufacturing_date.after' => 'Manufacturing date must be after 1900.',
            'expiry_date.required' => 'Expiry date is required.',
            'expiry_date.date' => 'Expiry date must be a valid date.',
            'expiry_date.after' => 'Expiry date must be after manufacturing date and today.',
            'supplier_id.required' => 'Supplier is required.',
            'supplier_id.exists' => 'Selected supplier does not exist.',
            'purchase_order_id.exists' => 'Selected purchase order does not exist.',
            'storage_conditions.max' => 'Storage conditions may not be greater than 500 characters.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            'status.required' => 'Status is required.',
            'status.in' => 'Status must be one of: active, expired, damaged, recalled, quarantine.',
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'product_id' => 'product',
            'product_variant_id' => 'product variant',
            'location_id' => 'storage location',
            'batch_number' => 'batch number',
            'quantity_received' => 'quantity received',
            'quantity_available' => 'quantity available',
            'cost_price' => 'cost price',
            'selling_price' => 'selling price',
            'mrp' => 'MRP',
            'manufacturing_date' => 'manufacturing date',
            'expiry_date' => 'expiry date',
            'supplier_id' => 'supplier',
            'purchase_order_id' => 'purchase order',
            'storage_conditions' => 'storage conditions',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        $this->merge([
            'product_variant_id' => $this->product_variant_id === '' ? null : $this->product_variant_id,
            'purchase_order_id' => $this->purchase_order_id === '' ? null : $this->purchase_order_id,
            'storage_conditions' => $this->storage_conditions === '' ? null : $this->storage_conditions,
            'notes' => $this->notes === '' ? null : $this->notes,
            'metadata' => $this->metadata === '' ? null : $this->metadata,
        ]);

        // Set default values
        if (!$this->has('quantity_available') || $this->quantity_available === '') {
            $this->merge(['quantity_available' => $this->quantity_received]);
        }

        if (!$this->has('status')) {
            $this->merge(['status' => 'active']);
        }

        // Format dates
        if ($this->manufacturing_date) {
            try {
                $this->merge(['manufacturing_date' => Carbon::parse($this->manufacturing_date)->format('Y-m-d')]);
            } catch (\Exception $e) {
                // Let validation handle invalid dates
            }
        }

        if ($this->expiry_date) {
            try {
                $this->merge(['expiry_date' => Carbon::parse($this->expiry_date)->format('Y-m-d')]);
            } catch (\Exception $e) {
                // Let validation handle invalid dates
            }
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate product variant belongs to product
            if ($this->product_id && $this->product_variant_id) {
                $variant = \App\Models\ProductVariant::find($this->product_variant_id);
                if ($variant && $variant->product_id != $this->product_id) {
                    $validator->errors()->add('product_variant_id', 'Selected variant does not belong to the selected product.');
                }
            }

            // Validate batch number uniqueness for the same product and location
            $existingBatch = \App\Models\Inventory::where('product_id', $this->product_id)
                ->where('location_id', $this->location_id)
                ->where('batch_number', $this->batch_number)
                ->where('expiry_date', $this->expiry_date)
                ->exists();

            if ($existingBatch) {
                $validator->errors()->add('batch_number', 'This batch number already exists for this product at this location with the same expiry date.');
            }

            // Validate pricing logic
            if ($this->cost_price && $this->selling_price && $this->cost_price > $this->selling_price) {
                $validator->errors()->add('selling_price', 'Selling price should be greater than or equal to cost price.');
            }

            if ($this->selling_price && $this->mrp && $this->selling_price > $this->mrp) {
                $validator->errors()->add('selling_price', 'Selling price cannot be greater than MRP.');
            }

            // Validate expiry date is reasonable (not too far in future)
            if ($this->expiry_date) {
                try {
                    $expiryDate = Carbon::parse($this->expiry_date);
                    $maxExpiryDate = Carbon::now()->addYears(10);
                    
                    if ($expiryDate->gt($maxExpiryDate)) {
                        $validator->errors()->add('expiry_date', 'Expiry date seems too far in the future. Please verify the date.');
                    }
                } catch (\Exception $e) {
                    // Date validation will handle invalid dates
                }
            }

            // Validate quantity available is not greater than quantity received
            if ($this->quantity_available && $this->quantity_received && $this->quantity_available > $this->quantity_received) {
                $validator->errors()->add('quantity_available', 'Quantity available cannot be greater than quantity received.');
            }

            // Validate purchase order belongs to supplier
            if ($this->purchase_order_id && $this->supplier_id) {
                $purchaseOrder = \App\Models\PurchaseOrder::find($this->purchase_order_id);
                if ($purchaseOrder && $purchaseOrder->supplier_id != $this->supplier_id) {
                    $validator->errors()->add('purchase_order_id', 'Selected purchase order does not belong to the selected supplier.');
                }
            }

            // Validate metadata structure
            if ($this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }

            // Validate location is active
            if ($this->location_id) {
                $location = \App\Models\Location::find($this->location_id);
                if ($location && !$location->is_active) {
                    $validator->errors()->add('location_id', 'Cannot add inventory to inactive location.');
                }
            }

            // Validate supplier is active
            if ($this->supplier_id) {
                $supplier = \App\Models\Supplier::find($this->supplier_id);
                if ($supplier && !$supplier->is_active) {
                    $validator->errors()->add('supplier_id', 'Cannot add inventory from inactive supplier.');
                }
            }
        });
    }
}
