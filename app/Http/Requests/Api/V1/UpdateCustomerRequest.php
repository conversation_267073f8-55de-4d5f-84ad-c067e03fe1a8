<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'pharmacist', 'cashier']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $customerId = $this->route('customer')->id;

        return [
            'customer_code' => [
                'sometimes',
                'nullable',
                'string',
                'max:50',
                'regex:/^[A-Z0-9-]+$/',
                Rule::unique('customers')->ignore($customerId)
            ],
            'first_name' => 'sometimes|required|string|max:100',
            'last_name' => 'sometimes|required|string|max:100',
            'email' => [
                'sometimes',
                'nullable',
                'email',
                'max:255',
                Rule::unique('customers')->ignore($customerId)
            ],
            'phone' => [
                'sometimes',
                'required',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/',
                Rule::unique('customers')->ignore($customerId)
            ],
            'mobile' => [
                'sometimes',
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/'
            ],
            'date_of_birth' => 'sometimes|nullable|date|before:today|after:1900-01-01',
            'gender' => 'sometimes|nullable|string|in:male,female,other',
            'address_line_1' => 'sometimes|nullable|string|max:255',
            'address_line_2' => 'sometimes|nullable|string|max:255',
            'city' => 'sometimes|nullable|string|max:100',
            'state' => 'sometimes|nullable|string|max:100',
            'postal_code' => [
                'sometimes',
                'nullable',
                'string',
                'max:20',
                'regex:/^[A-Z0-9\s\-]+$/i'
            ],
            'country' => 'sometimes|nullable|string|max:100',
            'emergency_contact_name' => 'sometimes|nullable|string|max:255',
            'emergency_contact_phone' => [
                'sometimes',
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/'
            ],
            'insurance_provider' => 'sometimes|nullable|string|max:255',
            'insurance_number' => 'sometimes|nullable|string|max:100',
            'allergies' => 'sometimes|nullable|string|max:1000',
            'medical_conditions' => 'sometimes|nullable|string|max:1000',
            'preferred_doctor' => 'sometimes|nullable|string|max:255',
            'loyalty_program_id' => 'sometimes|nullable|exists:loyalty_programs,id',
            'loyalty_points' => 'sometimes|nullable|integer|min:0|max:999999',
            'credit_limit' => 'sometimes|nullable|numeric|min:0|max:99999.99',
            'discount_percentage' => 'sometimes|nullable|numeric|min:0|max:100',
            'is_active' => 'sometimes|boolean',
            'notes' => 'sometimes|nullable|string|max:1000',
            'metadata' => 'sometimes|nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'customer_code.regex' => 'Customer code must contain only uppercase letters, numbers, and hyphens.',
            'customer_code.unique' => 'This customer code already exists.',
            'first_name.required' => 'First name is required.',
            'first_name.max' => 'First name may not be greater than 100 characters.',
            'last_name.required' => 'Last name is required.',
            'last_name.max' => 'Last name may not be greater than 100 characters.',
            'email.email' => 'Please provide a valid email address.',
            'email.unique' => 'This email address already exists.',
            'phone.required' => 'Phone number is required.',
            'phone.regex' => 'Please provide a valid phone number.',
            'phone.unique' => 'This phone number already exists.',
            'mobile.regex' => 'Please provide a valid mobile number.',
            'date_of_birth.date' => 'Date of birth must be a valid date.',
            'date_of_birth.before' => 'Date of birth must be before today.',
            'date_of_birth.after' => 'Date of birth must be after 1900.',
            'gender.in' => 'Gender must be one of: male, female, other.',
            'address_line_1.max' => 'Address line 1 may not be greater than 255 characters.',
            'address_line_2.max' => 'Address line 2 may not be greater than 255 characters.',
            'city.max' => 'City may not be greater than 100 characters.',
            'state.max' => 'State may not be greater than 100 characters.',
            'postal_code.regex' => 'Please provide a valid postal code.',
            'country.max' => 'Country may not be greater than 100 characters.',
            'emergency_contact_name.max' => 'Emergency contact name may not be greater than 255 characters.',
            'emergency_contact_phone.regex' => 'Please provide a valid emergency contact phone number.',
            'insurance_provider.max' => 'Insurance provider may not be greater than 255 characters.',
            'insurance_number.max' => 'Insurance number may not be greater than 100 characters.',
            'allergies.max' => 'Allergies may not be greater than 1000 characters.',
            'medical_conditions.max' => 'Medical conditions may not be greater than 1000 characters.',
            'preferred_doctor.max' => 'Preferred doctor may not be greater than 255 characters.',
            'loyalty_program_id.exists' => 'Selected loyalty program does not exist.',
            'loyalty_points.min' => 'Loyalty points must be at least 0.',
            'loyalty_points.max' => 'Loyalty points may not be greater than 999,999.',
            'credit_limit.min' => 'Credit limit must be at least 0.',
            'credit_limit.max' => 'Credit limit may not be greater than 99,999.99.',
            'discount_percentage.min' => 'Discount percentage must be at least 0.',
            'discount_percentage.max' => 'Discount percentage may not be greater than 100.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'customer_code' => 'customer code',
            'first_name' => 'first name',
            'last_name' => 'last name',
            'date_of_birth' => 'date of birth',
            'address_line_1' => 'address line 1',
            'address_line_2' => 'address line 2',
            'postal_code' => 'postal code',
            'emergency_contact_name' => 'emergency contact name',
            'emergency_contact_phone' => 'emergency contact phone',
            'insurance_provider' => 'insurance provider',
            'insurance_number' => 'insurance number',
            'medical_conditions' => 'medical conditions',
            'preferred_doctor' => 'preferred doctor',
            'loyalty_program_id' => 'loyalty program',
            'loyalty_points' => 'loyalty points',
            'credit_limit' => 'credit limit',
            'discount_percentage' => 'discount percentage',
            'is_active' => 'active status',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        $nullableFields = [
            'customer_code', 'email', 'mobile', 'date_of_birth', 'gender',
            'address_line_1', 'address_line_2', 'city', 'state', 'postal_code', 'country',
            'emergency_contact_name', 'emergency_contact_phone', 'insurance_provider',
            'insurance_number', 'allergies', 'medical_conditions', 'preferred_doctor',
            'loyalty_program_id', 'loyalty_points', 'credit_limit', 'discount_percentage',
            'notes', 'metadata'
        ];

        foreach ($nullableFields as $field) {
            if ($this->has($field) && $this->$field === '') {
                $this->merge([$field => null]);
            }
        }

        // Normalize phone numbers
        if ($this->has('phone') && $this->phone) {
            $this->merge(['phone' => preg_replace('/[^\+0-9]/', '', $this->phone)]);
        }

        if ($this->has('mobile') && $this->mobile) {
            $this->merge(['mobile' => preg_replace('/[^\+0-9]/', '', $this->mobile)]);
        }

        if ($this->has('emergency_contact_phone') && $this->emergency_contact_phone) {
            $this->merge(['emergency_contact_phone' => preg_replace('/[^\+0-9]/', '', $this->emergency_contact_phone)]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $customer = $this->route('customer');
            
            // Validate age constraints
            if ($this->has('date_of_birth') && $this->date_of_birth) {
                $age = \Carbon\Carbon::parse($this->date_of_birth)->age;
                
                if ($age < 0) {
                    $validator->errors()->add('date_of_birth', 'Invalid date of birth.');
                } elseif ($age > 150) {
                    $validator->errors()->add('date_of_birth', 'Age seems unrealistic. Please verify the date of birth.');
                }
            }

            // Validate phone number uniqueness across phone and mobile
            $phone = $this->phone ?? $customer->phone;
            $mobile = $this->mobile ?? $customer->mobile;
            
            if ($phone && $mobile && $phone === $mobile) {
                $validator->errors()->add('mobile', 'Mobile number cannot be the same as phone number.');
            }

            // Validate emergency contact is different from customer
            $emergencyPhone = $this->emergency_contact_phone ?? $customer->emergency_contact_phone;
            
            if ($emergencyPhone && ($emergencyPhone === $phone || $emergencyPhone === $mobile)) {
                $validator->errors()->add('emergency_contact_phone', 'Emergency contact phone must be different from customer phone numbers.');
            }

            // Validate loyalty program changes
            if ($this->has('loyalty_program_id') && $this->loyalty_program_id) {
                $loyaltyProgram = \App\Models\LoyaltyProgram::find($this->loyalty_program_id);
                if ($loyaltyProgram && !$loyaltyProgram->is_active) {
                    $validator->errors()->add('loyalty_program_id', 'Cannot assign inactive loyalty program.');
                }

                // Check minimum age requirement for loyalty program
                $dateOfBirth = $this->date_of_birth ?? $customer->date_of_birth;
                if ($loyaltyProgram && $loyaltyProgram->minimum_age && $dateOfBirth) {
                    $age = \Carbon\Carbon::parse($dateOfBirth)->age;
                    if ($age < $loyaltyProgram->minimum_age) {
                        $validator->errors()->add('loyalty_program_id', 'Customer does not meet minimum age requirement for this loyalty program.');
                    }
                }

                // Validate loyalty points transfer when changing programs
                if ($customer->loyalty_program_id && $customer->loyalty_program_id !== $this->loyalty_program_id) {
                    if ($customer->loyalty_points > 0) {
                        $validator->errors()->add('loyalty_program_id', 'Cannot change loyalty program while customer has active points. Please redeem or transfer points first.');
                    }
                }
            }

            // Validate credit limit authorization and changes
            if ($this->has('credit_limit')) {
                if ($this->credit_limit && $this->credit_limit > 0) {
                    if (!$this->user()->hasAnyRole(['admin', 'pharmacist'])) {
                        $validator->errors()->add('credit_limit', 'Only administrators and pharmacists can set credit limits.');
                    }

                    if ($this->credit_limit > 10000) {
                        $validator->errors()->add('credit_limit', 'Credit limit exceeds maximum allowed amount (10,000).');
                    }
                }

                // Check outstanding balance when reducing credit limit
                if ($this->credit_limit < $customer->credit_limit) {
                    $outstandingBalance = $customer->outstanding_balance ?? 0;
                    if ($this->credit_limit < $outstandingBalance) {
                        $validator->errors()->add('credit_limit', 'Credit limit cannot be less than outstanding balance (' . number_format($outstandingBalance, 2) . ').');
                    }
                }
            }

            // Validate discount percentage authorization
            if ($this->has('discount_percentage') && $this->discount_percentage && $this->discount_percentage > 0) {
                if (!$this->user()->hasAnyRole(['admin', 'pharmacist'])) {
                    $validator->errors()->add('discount_percentage', 'Only administrators and pharmacists can set discount percentages.');
                }

                if ($this->discount_percentage > 50) {
                    $validator->errors()->add('discount_percentage', 'Discount percentage cannot exceed 50%.');
                }
            }

            // Validate deactivation constraints
            if ($this->has('is_active') && !$this->is_active) {
                // Check for pending orders
                $pendingOrders = \App\Models\Sale::where('customer_id', $customer->id)
                    ->whereIn('status', ['pending', 'processing'])
                    ->exists();
                
                if ($pendingOrders) {
                    $validator->errors()->add('is_active', 'Cannot deactivate customer with pending orders.');
                }

                // Check outstanding balance
                if ($customer->outstanding_balance > 0) {
                    $validator->errors()->add('is_active', 'Cannot deactivate customer with outstanding balance.');
                }

                // Check active prescriptions
                $activePrescriptions = \App\Models\Prescription::where('customer_id', $customer->id)
                    ->where('status', 'active')
                    ->exists();
                
                if ($activePrescriptions) {
                    $validator->errors()->add('is_active', 'Cannot deactivate customer with active prescriptions.');
                }
            }

            // Validate insurance information consistency
            $insuranceProvider = $this->insurance_provider ?? $customer->insurance_provider;
            $insuranceNumber = $this->insurance_number ?? $customer->insurance_number;
            
            if ($insuranceProvider && !$insuranceNumber) {
                $validator->errors()->add('insurance_number', 'Insurance number is required when insurance provider is specified.');
            }

            if ($insuranceNumber && !$insuranceProvider) {
                $validator->errors()->add('insurance_provider', 'Insurance provider is required when insurance number is specified.');
            }

            // Validate loyalty points changes
            if ($this->has('loyalty_points')) {
                $pointsDifference = $this->loyalty_points - $customer->loyalty_points;
                
                // Large point adjustments require authorization
                if (abs($pointsDifference) > 1000 && !$this->user()->hasAnyRole(['admin', 'pharmacist'])) {
                    $validator->errors()->add('loyalty_points', 'Large loyalty point adjustments require administrator or pharmacist authorization.');
                }

                // Cannot reduce points below zero
                if ($this->loyalty_points < 0) {
                    $validator->errors()->add('loyalty_points', 'Loyalty points cannot be negative.');
                }
            }

            // Validate metadata structure
            if ($this->has('metadata') && $this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }

            // Validate name format
            if ($this->has('first_name') && $this->first_name && !preg_match('/^[a-zA-Z\s\-\'\.]+$/', $this->first_name)) {
                $validator->errors()->add('first_name', 'First name contains invalid characters.');
            }

            if ($this->has('last_name') && $this->last_name && !preg_match('/^[a-zA-Z\s\-\'\.]+$/', $this->last_name)) {
                $validator->errors()->add('last_name', 'Last name contains invalid characters.');
            }
        });
    }
}
