<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'pharmacist']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $categoryId = $this->route('category')->id;

        return [
            'name' => 'sometimes|required|string|max:255',
            'slug' => [
                'sometimes',
                'string',
                'max:255',
                'regex:/^[a-z0-9]+(?:-[a-z0-9]+)*$/',
                Rule::unique('categories')->ignore($categoryId)
            ],
            'description' => 'sometimes|nullable|string|max:1000',
            'parent_id' => [
                'sometimes',
                'nullable',
                'exists:categories,id',
                Rule::notIn([$categoryId]), // Cannot be parent of itself
                function ($attribute, $value, $fail) use ($categoryId) {
                    if ($value && $this->wouldCreateCircularReference($value, $categoryId)) {
                        $fail('Cannot create circular reference in category hierarchy.');
                    }
                }
            ],
            'image' => 'sometimes|nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'is_active' => 'sometimes|boolean',
            'sort_order' => 'sometimes|integer|min:0|max:999',
            'metadata' => 'sometimes|nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Category name is required.',
            'name.max' => 'Category name may not be greater than 255 characters.',
            'slug.regex' => 'Slug must contain only lowercase letters, numbers, and hyphens.',
            'slug.unique' => 'This slug already exists.',
            'description.max' => 'Description may not be greater than 1000 characters.',
            'parent_id.exists' => 'Selected parent category does not exist.',
            'parent_id.not_in' => 'Category cannot be parent of itself.',
            'image.image' => 'File must be an image.',
            'image.mimes' => 'Image must be a file of type: jpeg, png, jpg, gif, svg.',
            'image.max' => 'Image may not be greater than 2MB.',
            'sort_order.min' => 'Sort order must be at least 0.',
            'sort_order.max' => 'Sort order may not be greater than 999.',
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'parent_id' => 'parent category',
            'is_active' => 'active status',
            'sort_order' => 'sort order',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        if ($this->has('parent_id') && $this->parent_id === '') {
            $this->merge(['parent_id' => null]);
        }
        
        if ($this->has('description') && $this->description === '') {
            $this->merge(['description' => null]);
        }
        
        if ($this->has('metadata') && $this->metadata === '') {
            $this->merge(['metadata' => null]);
        }
    }

    /**
     * Check if updating this category would create a circular reference.
     */
    private function wouldCreateCircularReference($parentId, $categoryId): bool
    {
        if (!$parentId) {
            return false;
        }

        // Check if the proposed parent is a descendant of this category
        $category = \App\Models\Category::find($categoryId);
        if (!$category) {
            return false;
        }

        $descendants = $this->getAllDescendantIds($categoryId);
        return in_array($parentId, $descendants);
    }

    /**
     * Get all descendant IDs of a category.
     */
    private function getAllDescendantIds($categoryId): array
    {
        $descendants = [];
        $children = \App\Models\Category::where('parent_id', $categoryId)->pluck('id')->toArray();
        
        foreach ($children as $childId) {
            $descendants[] = $childId;
            $descendants = array_merge($descendants, $this->getAllDescendantIds($childId));
        }
        
        return $descendants;
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $categoryId = $this->route('category')->id;
            
            // Additional business logic validation
            if ($this->has('parent_id') && $this->parent_id) {
                $parent = \App\Models\Category::find($this->parent_id);
                if ($parent && !$parent->is_active) {
                    $validator->errors()->add('parent_id', 'Cannot assign inactive parent category.');
                }

                // Check maximum depth (e.g., 5 levels)
                if ($parent && $parent->depth >= 4) {
                    $validator->errors()->add('parent_id', 'Maximum category depth (5 levels) would be exceeded.');
                }
            }

            // Check if deactivating category with active children
            if ($this->has('is_active') && !$this->is_active) {
                $activeChildren = \App\Models\Category::where('parent_id', $categoryId)
                    ->where('is_active', true)
                    ->exists();
                
                if ($activeChildren) {
                    $validator->errors()->add('is_active', 'Cannot deactivate category with active subcategories.');
                }

                // Check if category has active products
                $activeProducts = \App\Models\Product::where('category_id', $categoryId)
                    ->where('is_active', true)
                    ->exists();
                
                if ($activeProducts) {
                    $validator->errors()->add('is_active', 'Cannot deactivate category with active products.');
                }
            }

            // Validate metadata structure
            if ($this->has('metadata') && $this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }
        });
    }
}
