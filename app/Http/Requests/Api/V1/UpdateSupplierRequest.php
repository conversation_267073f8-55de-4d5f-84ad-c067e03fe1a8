<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateSupplierRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'pharmacist']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $supplierId = $this->route('supplier')->id;

        return [
            'name' => 'sometimes|required|string|max:255',
            'code' => [
                'sometimes',
                'nullable',
                'string',
                'max:50',
                'regex:/^[A-Z0-9-]+$/',
                Rule::unique('suppliers')->ignore($supplierId)
            ],
            'contact_person' => 'sometimes|required|string|max:255',
            'email' => [
                'sometimes',
                'required',
                'email',
                'max:255',
                Rule::unique('suppliers')->ignore($supplierId)
            ],
            'phone' => [
                'sometimes',
                'required',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/'
            ],
            'mobile' => [
                'sometimes',
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/'
            ],
            'fax' => [
                'sometimes',
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/'
            ],
            'website' => 'sometimes|nullable|url|max:255',
            'address_line_1' => 'sometimes|required|string|max:255',
            'address_line_2' => 'sometimes|nullable|string|max:255',
            'city' => 'sometimes|required|string|max:100',
            'state' => 'sometimes|required|string|max:100',
            'postal_code' => [
                'sometimes',
                'required',
                'string',
                'max:20',
                'regex:/^[A-Z0-9\s\-]+$/i'
            ],
            'country' => 'sometimes|required|string|max:100',
            'tax_number' => [
                'sometimes',
                'nullable',
                'string',
                'max:50',
                Rule::unique('suppliers')->ignore($supplierId)
            ],
            'license_number' => [
                'sometimes',
                'nullable',
                'string',
                'max:100',
                Rule::unique('suppliers')->ignore($supplierId)
            ],
            'payment_terms' => [
                'sometimes',
                'required',
                'integer',
                'in:0,7,15,30,45,60,90'
            ],
            'credit_limit' => 'sometimes|required|numeric|min:0|max:9999999.99',
            'discount_percentage' => 'sometimes|nullable|numeric|min:0|max:100',
            'is_active' => 'sometimes|boolean',
            'notes' => 'sometimes|nullable|string|max:1000',
            'metadata' => 'sometimes|nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Supplier name is required.',
            'name.max' => 'Supplier name may not be greater than 255 characters.',
            'code.regex' => 'Supplier code must contain only uppercase letters, numbers, and hyphens.',
            'code.unique' => 'This supplier code already exists.',
            'contact_person.required' => 'Contact person is required.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please provide a valid email address.',
            'email.unique' => 'This email address already exists.',
            'phone.required' => 'Phone number is required.',
            'phone.regex' => 'Please provide a valid phone number.',
            'mobile.regex' => 'Please provide a valid mobile number.',
            'fax.regex' => 'Please provide a valid fax number.',
            'website.url' => 'Please provide a valid website URL.',
            'address_line_1.required' => 'Address line 1 is required.',
            'city.required' => 'City is required.',
            'state.required' => 'State is required.',
            'postal_code.required' => 'Postal code is required.',
            'postal_code.regex' => 'Please provide a valid postal code.',
            'country.required' => 'Country is required.',
            'tax_number.unique' => 'This tax number already exists.',
            'license_number.unique' => 'This license number already exists.',
            'payment_terms.required' => 'Payment terms are required.',
            'payment_terms.in' => 'Payment terms must be one of: 0, 7, 15, 30, 45, 60, or 90 days.',
            'credit_limit.required' => 'Credit limit is required.',
            'credit_limit.min' => 'Credit limit must be at least 0.',
            'credit_limit.max' => 'Credit limit may not be greater than 9,999,999.99.',
            'discount_percentage.min' => 'Discount percentage must be at least 0.',
            'discount_percentage.max' => 'Discount percentage may not be greater than 100.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'contact_person' => 'contact person',
            'address_line_1' => 'address line 1',
            'address_line_2' => 'address line 2',
            'postal_code' => 'postal code',
            'tax_number' => 'tax number',
            'license_number' => 'license number',
            'payment_terms' => 'payment terms',
            'credit_limit' => 'credit limit',
            'discount_percentage' => 'discount percentage',
            'is_active' => 'active status',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        if ($this->has('code') && $this->code === '') {
            $this->merge(['code' => null]);
        }
        
        if ($this->has('mobile') && $this->mobile === '') {
            $this->merge(['mobile' => null]);
        }
        
        if ($this->has('fax') && $this->fax === '') {
            $this->merge(['fax' => null]);
        }
        
        if ($this->has('website') && $this->website === '') {
            $this->merge(['website' => null]);
        }
        
        if ($this->has('address_line_2') && $this->address_line_2 === '') {
            $this->merge(['address_line_2' => null]);
        }
        
        if ($this->has('tax_number') && $this->tax_number === '') {
            $this->merge(['tax_number' => null]);
        }
        
        if ($this->has('license_number') && $this->license_number === '') {
            $this->merge(['license_number' => null]);
        }
        
        if ($this->has('discount_percentage') && $this->discount_percentage === '') {
            $this->merge(['discount_percentage' => null]);
        }
        
        if ($this->has('notes') && $this->notes === '') {
            $this->merge(['notes' => null]);
        }
        
        if ($this->has('metadata') && $this->metadata === '') {
            $this->merge(['metadata' => null]);
        }

        // Normalize phone numbers (remove spaces and special characters for validation)
        if ($this->has('phone') && $this->phone) {
            $this->merge(['phone' => preg_replace('/[^\+0-9]/', '', $this->phone)]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $supplierId = $this->route('supplier')->id;
            
            // Validate email domain for business emails
            if ($this->has('email') && $this->email && !$this->isValidBusinessEmail($this->email)) {
                $validator->errors()->add('email', 'Please provide a business email address.');
            }

            // Check if deactivating supplier with active products or pending orders
            if ($this->has('is_active') && !$this->is_active) {
                $activeProducts = \App\Models\Product::where('supplier_id', $supplierId)
                    ->where('is_active', true)
                    ->exists();
                
                if ($activeProducts) {
                    $validator->errors()->add('is_active', 'Cannot deactivate supplier with active products.');
                }

                $pendingOrders = \App\Models\PurchaseOrder::where('supplier_id', $supplierId)
                    ->whereIn('status', ['pending', 'approved', 'partial'])
                    ->exists();
                
                if ($pendingOrders) {
                    $validator->errors()->add('is_active', 'Cannot deactivate supplier with pending purchase orders.');
                }
            }

            // Validate metadata structure
            if ($this->has('metadata') && $this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }

            // Validate credit limit changes
            if ($this->has('credit_limit')) {
                $supplier = $this->route('supplier');
                $outstandingBalance = $supplier->outstanding_balance ?? 0;
                
                if ($this->credit_limit < $outstandingBalance) {
                    $validator->errors()->add('credit_limit', 'Credit limit cannot be less than outstanding balance (' . number_format($outstandingBalance, 2) . ').');
                }

                if ($this->credit_limit > 1000000) {
                    $validator->errors()->add('credit_limit', 'Credit limit seems unusually high. Please verify the amount.');
                }
            }
        });
    }

    /**
     * Check if email is a valid business email.
     */
    private function isValidBusinessEmail($email): bool
    {
        $personalDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com'];
        $domain = substr(strrchr($email, "@"), 1);
        
        return !in_array(strtolower($domain), $personalDomains);
    }
}
