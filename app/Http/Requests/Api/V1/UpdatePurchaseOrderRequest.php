<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdatePurchaseOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'pharmacist']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $purchaseOrderId = $this->route('purchaseOrder')->id;

        return [
            'purchase_order_number' => [
                'sometimes',
                'nullable',
                'string',
                'max:100',
                Rule::unique('purchase_orders')->ignore($purchaseOrderId)
            ],
            'supplier_id' => 'sometimes|required|exists:suppliers,id',
            'order_date' => 'sometimes|required|date|before_or_equal:today',
            'expected_delivery_date' => 'sometimes|nullable|date|after_or_equal:order_date',
            'status' => [
                'sometimes',
                'required',
                'string',
                'in:draft,pending,approved,ordered,partial,received,cancelled'
            ],
            'priority' => [
                'sometimes',
                'required',
                'string',
                'in:low,normal,high,urgent'
            ],
            'payment_terms' => [
                'sometimes',
                'required',
                'integer',
                'in:0,7,15,30,45,60,90'
            ],
            'discount_percentage' => 'sometimes|nullable|numeric|min:0|max:100',
            'tax_percentage' => 'sometimes|required|numeric|min:0|max:100',
            'shipping_cost' => 'sometimes|nullable|numeric|min:0|max:99999.99',
            'notes' => 'sometimes|nullable|string|max:1000',
            'approved_by' => 'sometimes|nullable|exists:users,id',
            'approved_at' => 'sometimes|nullable|date|before_or_equal:now',
            
            // Purchase order items (for complete replacement)
            'items' => 'sometimes|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.product_variant_id' => 'nullable|exists:product_variants,id',
            'items.*.quantity_ordered' => 'required|integer|min:1|max:999999',
            'items.*.unit_cost' => 'required|numeric|min:0|max:99999.99',
            'items.*.total_cost' => 'required|numeric|min:0|max:9999999.99',
            'items.*.notes' => 'nullable|string|max:500',
            
            'metadata' => 'sometimes|nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'purchase_order_number.unique' => 'This purchase order number already exists.',
            'supplier_id.required' => 'Supplier is required.',
            'supplier_id.exists' => 'Selected supplier does not exist.',
            'order_date.required' => 'Order date is required.',
            'order_date.date' => 'Order date must be a valid date.',
            'order_date.before_or_equal' => 'Order date cannot be in the future.',
            'expected_delivery_date.date' => 'Expected delivery date must be a valid date.',
            'expected_delivery_date.after_or_equal' => 'Expected delivery date must be on or after order date.',
            'status.required' => 'Status is required.',
            'status.in' => 'Status must be one of: draft, pending, approved, ordered, partial, received, cancelled.',
            'priority.required' => 'Priority is required.',
            'priority.in' => 'Priority must be one of: low, normal, high, urgent.',
            'payment_terms.required' => 'Payment terms are required.',
            'payment_terms.in' => 'Payment terms must be one of: 0, 7, 15, 30, 45, 60, or 90 days.',
            'discount_percentage.min' => 'Discount percentage must be at least 0.',
            'discount_percentage.max' => 'Discount percentage may not be greater than 100.',
            'tax_percentage.required' => 'Tax percentage is required.',
            'tax_percentage.min' => 'Tax percentage must be at least 0.',
            'tax_percentage.max' => 'Tax percentage may not be greater than 100.',
            'shipping_cost.min' => 'Shipping cost must be at least 0.',
            'shipping_cost.max' => 'Shipping cost may not be greater than 99,999.99.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            'approved_by.exists' => 'Selected approver does not exist.',
            'approved_at.date' => 'Approval date must be a valid date.',
            'approved_at.before_or_equal' => 'Approval date cannot be in the future.',
            
            'items.min' => 'At least one item is required.',
            'items.*.product_id.required' => 'Product is required for each item.',
            'items.*.product_id.exists' => 'Selected product does not exist.',
            'items.*.product_variant_id.exists' => 'Selected product variant does not exist.',
            'items.*.quantity_ordered.required' => 'Quantity is required for each item.',
            'items.*.quantity_ordered.min' => 'Quantity must be at least 1.',
            'items.*.quantity_ordered.max' => 'Quantity may not be greater than 999,999.',
            'items.*.unit_cost.required' => 'Unit cost is required for each item.',
            'items.*.unit_cost.min' => 'Unit cost must be at least 0.',
            'items.*.unit_cost.max' => 'Unit cost may not be greater than 99,999.99.',
            'items.*.total_cost.required' => 'Total cost is required for each item.',
            'items.*.total_cost.min' => 'Total cost must be at least 0.',
            'items.*.total_cost.max' => 'Total cost may not be greater than 9,999,999.99.',
            'items.*.notes.max' => 'Item notes may not be greater than 500 characters.',
            
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'purchase_order_number' => 'purchase order number',
            'supplier_id' => 'supplier',
            'order_date' => 'order date',
            'expected_delivery_date' => 'expected delivery date',
            'payment_terms' => 'payment terms',
            'discount_percentage' => 'discount percentage',
            'tax_percentage' => 'tax percentage',
            'shipping_cost' => 'shipping cost',
            'approved_by' => 'approved by',
            'approved_at' => 'approval date',
            'items.*.product_id' => 'product',
            'items.*.product_variant_id' => 'product variant',
            'items.*.quantity_ordered' => 'quantity',
            'items.*.unit_cost' => 'unit cost',
            'items.*.total_cost' => 'total cost',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        $nullableFields = [
            'purchase_order_number', 'expected_delivery_date', 'discount_percentage',
            'shipping_cost', 'notes', 'approved_by', 'approved_at', 'metadata'
        ];

        foreach ($nullableFields as $field) {
            if ($this->has($field) && $this->$field === '') {
                $this->merge([$field => null]);
            }
        }

        // Calculate totals for items if not provided
        if ($this->has('items') && $this->items) {
            $items = $this->items;
            foreach ($items as $index => $item) {
                if (!isset($item['total_cost']) && isset($item['quantity_ordered']) && isset($item['unit_cost'])) {
                    $items[$index]['total_cost'] = $item['quantity_ordered'] * $item['unit_cost'];
                }
            }
            $this->merge(['items' => $items]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $purchaseOrder = $this->route('purchaseOrder');
            
            // Validate supplier is active
            if ($this->has('supplier_id')) {
                $supplier = \App\Models\Supplier::find($this->supplier_id);
                if ($supplier && !$supplier->is_active) {
                    $validator->errors()->add('supplier_id', 'Cannot change to inactive supplier.');
                }
            }

            // Validate status transitions
            if ($this->has('status')) {
                $currentStatus = $purchaseOrder->status;
                $newStatus = $this->status;
                
                $validTransitions = [
                    'draft' => ['pending', 'cancelled'],
                    'pending' => ['approved', 'cancelled'],
                    'approved' => ['ordered', 'cancelled'],
                    'ordered' => ['partial', 'received', 'cancelled'],
                    'partial' => ['received', 'cancelled'],
                    'received' => [],
                    'cancelled' => []
                ];

                if (!in_array($newStatus, $validTransitions[$currentStatus] ?? [])) {
                    $validator->errors()->add('status', "Cannot change status from {$currentStatus} to {$newStatus}.");
                }

                // Cannot modify received or cancelled orders
                if (in_array($currentStatus, ['received', 'cancelled']) && $currentStatus !== $newStatus) {
                    $validator->errors()->add('status', "Cannot modify {$currentStatus} purchase order.");
                }
            }

            // Validate modifications based on current status
            $currentStatus = $purchaseOrder->status;
            
            if (in_array($currentStatus, ['received', 'cancelled'])) {
                $modifiableFields = ['notes', 'metadata'];
                $requestFields = array_keys($this->all());
                $nonModifiableFields = array_diff($requestFields, $modifiableFields);
                
                if (!empty($nonModifiableFields)) {
                    $validator->errors()->add('status', "Cannot modify {$currentStatus} purchase order except for notes and metadata.");
                }
            }

            // Validate approval logic
            if ($this->has('approved_by') || $this->has('approved_at')) {
                $approvedBy = $this->approved_by ?? $purchaseOrder->approved_by;
                $approvedAt = $this->approved_at ?? $purchaseOrder->approved_at;
                
                if (($approvedBy && !$approvedAt) || (!$approvedBy && $approvedAt)) {
                    $validator->errors()->add('approved_by', 'Both approver and approval date are required for approval.');
                }

                if ($this->has('approved_by') && $this->approved_by) {
                    $approver = \App\Models\User::find($this->approved_by);
                    if ($approver && !$approver->hasAnyRole(['admin', 'pharmacist'])) {
                        $validator->errors()->add('approved_by', 'Selected user does not have permission to approve purchase orders.');
                    }

                    // Cannot approve own purchase order (unless admin)
                    if ($this->approved_by === $this->user()->id && !$this->user()->hasRole('admin')) {
                        $validator->errors()->add('approved_by', 'Cannot approve your own purchase order.');
                    }
                }
            }

            // Validate items modifications
            if ($this->has('items')) {
                // Cannot modify items after ordering
                if (in_array($currentStatus, ['ordered', 'partial', 'received'])) {
                    $validator->errors()->add('items', 'Cannot modify items after order has been placed.');
                }

                $totalAmount = 0;
                $productIds = [];

                foreach ($this->items as $index => $item) {
                    // Check for duplicate products
                    $productKey = $item['product_id'] . '-' . ($item['product_variant_id'] ?? '');
                    if (in_array($productKey, $productIds)) {
                        $validator->errors()->add("items.{$index}.product_id", 'Duplicate product in order items.');
                    }
                    $productIds[] = $productKey;

                    // Validate product variant belongs to product
                    if (isset($item['product_variant_id']) && $item['product_variant_id']) {
                        $variant = \App\Models\ProductVariant::find($item['product_variant_id']);
                        if ($variant && $variant->product_id != $item['product_id']) {
                            $validator->errors()->add("items.{$index}.product_variant_id", 'Selected variant does not belong to the selected product.');
                        }
                    }

                    // Validate product is active
                    if (isset($item['product_id'])) {
                        $product = \App\Models\Product::find($item['product_id']);
                        if ($product && !$product->is_active) {
                            $validator->errors()->add("items.{$index}.product_id", 'Cannot order inactive product.');
                        }
                    }

                    // Validate total cost calculation
                    if (isset($item['quantity_ordered']) && isset($item['unit_cost']) && isset($item['total_cost'])) {
                        $expectedTotal = $item['quantity_ordered'] * $item['unit_cost'];
                        $tolerance = 0.01; // 1 cent tolerance
                        
                        if (abs($item['total_cost'] - $expectedTotal) > $tolerance) {
                            $validator->errors()->add("items.{$index}.total_cost", 'Total cost does not match quantity × unit cost.');
                        }
                    }

                    // Add to total amount
                    if (isset($item['total_cost'])) {
                        $totalAmount += $item['total_cost'];
                    }
                }

                // Validate reasonable order total
                if ($totalAmount > 1000000) {
                    $validator->errors()->add('items', 'Order total exceeds maximum allowed amount (1,000,000).');
                }

                if ($totalAmount < 1) {
                    $validator->errors()->add('items', 'Order total must be at least 1.');
                }
            }

            // Validate metadata structure
            if ($this->has('metadata') && $this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }
        });
    }
}
