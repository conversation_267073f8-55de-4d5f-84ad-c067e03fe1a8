<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

class StoreProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'pharmacist']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'generic_name' => 'required|string|max:255',
            'brand' => 'nullable|string|max:255',
            'sku' => 'required|string|max:100|unique:products,sku',
            'barcode' => 'nullable|string|max:50|unique:products,barcode',
            'category_id' => 'required|exists:categories,id',
            'supplier_id' => 'required|exists:suppliers,id',
            'strength' => 'nullable|string|max:100',
            'dosage_form' => 'nullable|string|max:100',
            'unit_price' => 'required|numeric|min:0|max:99999.99',
            'selling_price' => 'required|numeric|min:0|max:99999.99',
            'mrp' => 'required|numeric|min:0|max:99999.99',
            'reorder_level' => 'required|integer|min:0',
            'max_stock_level' => 'required|integer|min:0|gte:reorder_level',
            'is_prescription_required' => 'required|boolean',
            'is_controlled_substance' => 'required|boolean',
            'storage_conditions' => 'nullable|string|max:500',
            'manufacturer' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Product name is required.',
            'generic_name.required' => 'Generic name is required.',
            'sku.required' => 'SKU is required.',
            'sku.unique' => 'This SKU already exists.',
            'barcode.unique' => 'This barcode already exists.',
            'category_id.required' => 'Category is required.',
            'category_id.exists' => 'Selected category does not exist.',
            'supplier_id.required' => 'Supplier is required.',
            'supplier_id.exists' => 'Selected supplier does not exist.',
            'unit_price.required' => 'Unit price is required.',
            'selling_price.required' => 'Selling price is required.',
            'mrp.required' => 'MRP is required.',
            'reorder_level.required' => 'Reorder level is required.',
            'max_stock_level.required' => 'Maximum stock level is required.',
            'max_stock_level.gte' => 'Maximum stock level must be greater than or equal to reorder level.',
            'is_prescription_required.required' => 'Prescription requirement status is required.',
            'is_controlled_substance.required' => 'Controlled substance status is required.',
            'image.image' => 'File must be an image.',
            'image.mimes' => 'Image must be a file of type: jpeg, png, jpg, gif.',
            'image.max' => 'Image may not be greater than 2MB.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'sku' => 'SKU',
            'mrp' => 'MRP',
            'is_prescription_required' => 'prescription requirement',
            'is_controlled_substance' => 'controlled substance status',
        ];
    }
}
