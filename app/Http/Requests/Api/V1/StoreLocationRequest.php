<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreLocationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'pharmacist']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'location_code' => [
                'nullable',
                'string',
                'max:50',
                'regex:/^[A-Z0-9-]+$/',
                'unique:locations,location_code'
            ],
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'location_type' => [
                'required',
                'string',
                'in:warehouse,shelf,bin,refrigerator,freezer,controlled,quarantine,receiving,shipping'
            ],
            'parent_location_id' => 'nullable|exists:locations,id',
            'aisle' => 'nullable|string|max:10',
            'shelf' => 'nullable|string|max:10',
            'bin' => 'nullable|string|max:10',
            'level' => 'nullable|integer|min:0|max:10',
            'capacity' => 'nullable|integer|min:1|max:999999',
            'current_stock_count' => 'nullable|integer|min:0|max:999999',
            'temperature_min' => 'nullable|numeric|min:-50|max:50',
            'temperature_max' => 'nullable|numeric|min:-50|max:50',
            'humidity_min' => 'nullable|numeric|min:0|max:100',
            'humidity_max' => 'nullable|numeric|min:0|max:100',
            'requires_prescription_access' => 'boolean',
            'requires_controlled_access' => 'boolean',
            'is_active' => 'boolean',
            'is_pickable' => 'boolean',
            'is_receivable' => 'boolean',
            'barcode' => [
                'nullable',
                'string',
                'max:50',
                'regex:/^[0-9A-Z\-]+$/',
                'unique:locations,barcode'
            ],
            'sort_order' => 'integer|min:0|max:999',
            'notes' => 'nullable|string|max:1000',
            'metadata' => 'nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'location_code.regex' => 'Location code must contain only uppercase letters, numbers, and hyphens.',
            'location_code.unique' => 'This location code already exists.',
            'name.required' => 'Location name is required.',
            'name.max' => 'Location name may not be greater than 255 characters.',
            'description.max' => 'Description may not be greater than 1000 characters.',
            'location_type.required' => 'Location type is required.',
            'location_type.in' => 'Location type must be one of: warehouse, shelf, bin, refrigerator, freezer, controlled, quarantine, receiving, shipping.',
            'parent_location_id.exists' => 'Selected parent location does not exist.',
            'aisle.max' => 'Aisle may not be greater than 10 characters.',
            'shelf.max' => 'Shelf may not be greater than 10 characters.',
            'bin.max' => 'Bin may not be greater than 10 characters.',
            'level.min' => 'Level must be at least 0.',
            'level.max' => 'Level may not be greater than 10.',
            'capacity.min' => 'Capacity must be at least 1.',
            'capacity.max' => 'Capacity may not be greater than 999,999.',
            'current_stock_count.min' => 'Current stock count must be at least 0.',
            'current_stock_count.max' => 'Current stock count may not be greater than 999,999.',
            'temperature_min.min' => 'Minimum temperature must be at least -50°C.',
            'temperature_min.max' => 'Minimum temperature may not be greater than 50°C.',
            'temperature_max.min' => 'Maximum temperature must be at least -50°C.',
            'temperature_max.max' => 'Maximum temperature may not be greater than 50°C.',
            'humidity_min.min' => 'Minimum humidity must be at least 0%.',
            'humidity_min.max' => 'Minimum humidity may not be greater than 100%.',
            'humidity_max.min' => 'Maximum humidity must be at least 0%.',
            'humidity_max.max' => 'Maximum humidity may not be greater than 100%.',
            'barcode.regex' => 'Barcode must contain only numbers, uppercase letters, and hyphens.',
            'barcode.unique' => 'This barcode already exists.',
            'sort_order.min' => 'Sort order must be at least 0.',
            'sort_order.max' => 'Sort order may not be greater than 999.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'location_code' => 'location code',
            'location_type' => 'location type',
            'parent_location_id' => 'parent location',
            'current_stock_count' => 'current stock count',
            'temperature_min' => 'minimum temperature',
            'temperature_max' => 'maximum temperature',
            'humidity_min' => 'minimum humidity',
            'humidity_max' => 'maximum humidity',
            'requires_prescription_access' => 'requires prescription access',
            'requires_controlled_access' => 'requires controlled access',
            'is_active' => 'active status',
            'is_pickable' => 'pickable',
            'is_receivable' => 'receivable',
            'sort_order' => 'sort order',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        $this->merge([
            'location_code' => $this->location_code === '' ? null : $this->location_code,
            'description' => $this->description === '' ? null : $this->description,
            'parent_location_id' => $this->parent_location_id === '' ? null : $this->parent_location_id,
            'aisle' => $this->aisle === '' ? null : $this->aisle,
            'shelf' => $this->shelf === '' ? null : $this->shelf,
            'bin' => $this->bin === '' ? null : $this->bin,
            'level' => $this->level === '' ? null : $this->level,
            'capacity' => $this->capacity === '' ? null : $this->capacity,
            'current_stock_count' => $this->current_stock_count === '' ? null : $this->current_stock_count,
            'temperature_min' => $this->temperature_min === '' ? null : $this->temperature_min,
            'temperature_max' => $this->temperature_max === '' ? null : $this->temperature_max,
            'humidity_min' => $this->humidity_min === '' ? null : $this->humidity_min,
            'humidity_max' => $this->humidity_max === '' ? null : $this->humidity_max,
            'barcode' => $this->barcode === '' ? null : $this->barcode,
            'notes' => $this->notes === '' ? null : $this->notes,
            'metadata' => $this->metadata === '' ? null : $this->metadata,
        ]);

        // Auto-generate location code if not provided
        if (!$this->location_code) {
            $typePrefix = strtoupper(substr($this->location_type ?? 'LOC', 0, 3));
            $this->merge(['location_code' => $typePrefix . '-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -4))]);
        }

        // Set default values
        if (!$this->has('is_active')) {
            $this->merge(['is_active' => true]);
        }

        if (!$this->has('is_pickable')) {
            $this->merge(['is_pickable' => true]);
        }

        if (!$this->has('is_receivable')) {
            $this->merge(['is_receivable' => true]);
        }

        if (!$this->has('requires_prescription_access')) {
            $this->merge(['requires_prescription_access' => false]);
        }

        if (!$this->has('requires_controlled_access')) {
            $this->merge(['requires_controlled_access' => false]);
        }

        if (!$this->has('sort_order')) {
            $this->merge(['sort_order' => 0]);
        }

        if (!$this->has('current_stock_count')) {
            $this->merge(['current_stock_count' => 0]);
        }

        // Set default temperature and humidity based on location type
        if ($this->location_type) {
            switch ($this->location_type) {
                case 'refrigerator':
                    if (!$this->temperature_min) $this->merge(['temperature_min' => 2]);
                    if (!$this->temperature_max) $this->merge(['temperature_max' => 8]);
                    break;
                case 'freezer':
                    if (!$this->temperature_min) $this->merge(['temperature_min' => -25]);
                    if (!$this->temperature_max) $this->merge(['temperature_max' => -15]);
                    break;
                case 'controlled':
                    if (!$this->requires_controlled_access) $this->merge(['requires_controlled_access' => true]);
                    break;
            }
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate parent location hierarchy
            if ($this->parent_location_id) {
                $parentLocation = \App\Models\Location::find($this->parent_location_id);
                
                if ($parentLocation) {
                    // Parent must be active
                    if (!$parentLocation->is_active) {
                        $validator->errors()->add('parent_location_id', 'Cannot assign inactive parent location.');
                    }

                    // Validate hierarchy depth (max 5 levels)
                    $depth = $this->calculateLocationDepth($parentLocation);
                    if ($depth >= 5) {
                        $validator->errors()->add('parent_location_id', 'Location hierarchy cannot exceed 5 levels.');
                    }

                    // Validate location type hierarchy rules
                    $validChildTypes = [
                        'warehouse' => ['shelf', 'refrigerator', 'freezer', 'controlled', 'quarantine', 'receiving', 'shipping'],
                        'shelf' => ['bin'],
                        'bin' => [],
                        'refrigerator' => ['shelf', 'bin'],
                        'freezer' => ['shelf', 'bin'],
                        'controlled' => ['shelf', 'bin'],
                        'quarantine' => ['shelf', 'bin'],
                        'receiving' => [],
                        'shipping' => []
                    ];

                    if (!in_array($this->location_type, $validChildTypes[$parentLocation->location_type] ?? [])) {
                        $validator->errors()->add('location_type', "Location type '{$this->location_type}' cannot be a child of '{$parentLocation->location_type}'.");
                    }
                }
            }

            // Validate temperature range
            if ($this->temperature_min && $this->temperature_max && $this->temperature_min >= $this->temperature_max) {
                $validator->errors()->add('temperature_max', 'Maximum temperature must be greater than minimum temperature.');
            }

            // Validate humidity range
            if ($this->humidity_min && $this->humidity_max && $this->humidity_min >= $this->humidity_max) {
                $validator->errors()->add('humidity_max', 'Maximum humidity must be greater than minimum humidity.');
            }

            // Validate capacity vs current stock
            if ($this->capacity && $this->current_stock_count && $this->current_stock_count > $this->capacity) {
                $validator->errors()->add('current_stock_count', 'Current stock count cannot exceed capacity.');
            }

            // Validate location type specific requirements
            switch ($this->location_type) {
                case 'refrigerator':
                case 'freezer':
                    if (!$this->temperature_min || !$this->temperature_max) {
                        $validator->errors()->add('temperature_min', 'Temperature range is required for refrigerated/frozen locations.');
                    }
                    break;

                case 'controlled':
                    if (!$this->requires_controlled_access) {
                        $validator->errors()->add('requires_controlled_access', 'Controlled locations must require controlled access.');
                    }
                    break;

                case 'quarantine':
                    if ($this->is_pickable) {
                        $validator->errors()->add('is_pickable', 'Quarantine locations should not be pickable.');
                    }
                    break;

                case 'receiving':
                    if (!$this->is_receivable) {
                        $validator->errors()->add('is_receivable', 'Receiving locations must be receivable.');
                    }
                    if ($this->is_pickable) {
                        $validator->errors()->add('is_pickable', 'Receiving locations should not be pickable.');
                    }
                    break;

                case 'shipping':
                    if ($this->is_receivable) {
                        $validator->errors()->add('is_receivable', 'Shipping locations should not be receivable.');
                    }
                    break;
            }

            // Validate address components consistency
            if ($this->aisle || $this->shelf || $this->bin) {
                if ($this->location_type === 'warehouse') {
                    $validator->errors()->add('aisle', 'Warehouse locations should not have aisle/shelf/bin details.');
                }
            }

            // Validate bin location requires shelf
            if ($this->location_type === 'bin' && !$this->parent_location_id) {
                $validator->errors()->add('parent_location_id', 'Bin locations must have a parent location.');
            }

            // Validate shelf location addressing
            if ($this->location_type === 'shelf') {
                if (!$this->aisle) {
                    $validator->errors()->add('aisle', 'Shelf locations should specify an aisle.');
                }
            }

            // Validate bin location addressing
            if ($this->location_type === 'bin') {
                if (!$this->bin) {
                    $validator->errors()->add('bin', 'Bin locations should specify a bin identifier.');
                }
            }

            // Validate metadata structure
            if ($this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }

            // Validate barcode format
            if ($this->barcode) {
                // Check if barcode follows location barcode pattern
                if (!preg_match('/^LOC-[A-Z0-9\-]+$/', $this->barcode)) {
                    $validator->errors()->add('barcode', 'Location barcode should follow format: LOC-XXXXXXXX');
                }
            }

            // Validate name uniqueness within same parent
            if ($this->parent_location_id) {
                $existingLocation = \App\Models\Location::where('parent_location_id', $this->parent_location_id)
                    ->where('name', $this->name)
                    ->exists();

                if ($existingLocation) {
                    $validator->errors()->add('name', 'Location name must be unique within the same parent location.');
                }
            } else {
                // Root level locations should have unique names
                $existingLocation = \App\Models\Location::whereNull('parent_location_id')
                    ->where('name', $this->name)
                    ->exists();

                if ($existingLocation) {
                    $validator->errors()->add('name', 'Root level location name must be unique.');
                }
            }

            // Validate level consistency with hierarchy
            if ($this->level !== null && $this->parent_location_id) {
                $parentLocation = \App\Models\Location::find($this->parent_location_id);
                if ($parentLocation && $parentLocation->level !== null) {
                    if ($this->level <= $parentLocation->level) {
                        $validator->errors()->add('level', 'Location level must be greater than parent location level.');
                    }
                }
            }
        });
    }

    /**
     * Calculate the depth of a location in the hierarchy.
     */
    private function calculateLocationDepth($location, $depth = 0): int
    {
        if (!$location->parent_location_id || $depth >= 10) { // Prevent infinite recursion
            return $depth;
        }

        $parent = \App\Models\Location::find($location->parent_location_id);
        if (!$parent) {
            return $depth;
        }

        return $this->calculateLocationDepth($parent, $depth + 1);
    }
}
