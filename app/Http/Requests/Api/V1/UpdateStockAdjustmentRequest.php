<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateStockAdjustmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $adjustment = $this->route('stockAdjustment');
        
        // Only allow updates if not yet approved or user is admin
        return $this->user()->hasAnyRole(['admin', 'pharmacist']) && 
               (!$adjustment->approved_at || $this->user()->hasRole('admin'));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $adjustmentId = $this->route('stockAdjustment')->id;

        return [
            'inventory_id' => 'sometimes|required|exists:inventory,id',
            'adjustment_type' => [
                'sometimes',
                'required',
                'string',
                'in:increase,decrease,correction,damage,expiry,theft,return'
            ],
            'quantity_adjusted' => 'sometimes|required|integer|not_in:0',
            'reason' => 'sometimes|required|string|max:500',
            'reference_number' => [
                'sometimes',
                'nullable',
                'string',
                'max:100',
                Rule::unique('stock_adjustments')->ignore($adjustmentId)
            ],
            'cost_impact' => 'sometimes|nullable|numeric',
            'approved_by' => 'sometimes|nullable|exists:users,id',
            'approved_at' => 'sometimes|nullable|date|before_or_equal:now',
            'notes' => 'sometimes|nullable|string|max:1000',
            'metadata' => 'sometimes|nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'inventory_id.required' => 'Inventory item is required.',
            'inventory_id.exists' => 'Selected inventory item does not exist.',
            'adjustment_type.required' => 'Adjustment type is required.',
            'adjustment_type.in' => 'Adjustment type must be one of: increase, decrease, correction, damage, expiry, theft, return.',
            'quantity_adjusted.required' => 'Quantity adjusted is required.',
            'quantity_adjusted.not_in' => 'Quantity adjusted cannot be zero.',
            'reason.required' => 'Reason for adjustment is required.',
            'reason.max' => 'Reason may not be greater than 500 characters.',
            'reference_number.max' => 'Reference number may not be greater than 100 characters.',
            'reference_number.unique' => 'This reference number already exists.',
            'cost_impact.numeric' => 'Cost impact must be a number.',
            'approved_by.exists' => 'Selected approver does not exist.',
            'approved_at.date' => 'Approval date must be a valid date.',
            'approved_at.before_or_equal' => 'Approval date cannot be in the future.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'inventory_id' => 'inventory item',
            'adjustment_type' => 'adjustment type',
            'quantity_adjusted' => 'quantity adjusted',
            'reference_number' => 'reference number',
            'cost_impact' => 'cost impact',
            'approved_by' => 'approved by',
            'approved_at' => 'approval date',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        if ($this->has('reference_number') && $this->reference_number === '') {
            $this->merge(['reference_number' => null]);
        }
        
        if ($this->has('cost_impact') && $this->cost_impact === '') {
            $this->merge(['cost_impact' => null]);
        }
        
        if ($this->has('approved_by') && $this->approved_by === '') {
            $this->merge(['approved_by' => null]);
        }
        
        if ($this->has('approved_at') && $this->approved_at === '') {
            $this->merge(['approved_at' => null]);
        }
        
        if ($this->has('notes') && $this->notes === '') {
            $this->merge(['notes' => null]);
        }
        
        if ($this->has('metadata') && $this->metadata === '') {
            $this->merge(['metadata' => null]);
        }

        // Convert quantity to negative for decrease adjustments
        if ($this->has('adjustment_type') && $this->has('quantity_adjusted')) {
            if (in_array($this->adjustment_type, ['decrease', 'damage', 'expiry', 'theft']) && $this->quantity_adjusted > 0) {
                $this->merge(['quantity_adjusted' => -abs($this->quantity_adjusted)]);
            }

            // Ensure positive quantity for increase adjustments
            if (in_array($this->adjustment_type, ['increase', 'return']) && $this->quantity_adjusted < 0) {
                $this->merge(['quantity_adjusted' => abs($this->quantity_adjusted)]);
            }
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $adjustment = $this->route('stockAdjustment');
            
            // Prevent updates to approved adjustments (except by admin)
            if ($adjustment->approved_at && !$this->user()->hasRole('admin')) {
                $validator->errors()->add('approved_at', 'Cannot modify approved adjustments.');
                return;
            }

            // Validate inventory exists and is active
            if ($this->has('inventory_id')) {
                $inventory = \App\Models\Inventory::find($this->inventory_id);
                
                if (!$inventory) {
                    $validator->errors()->add('inventory_id', 'Selected inventory item does not exist.');
                    return;
                }

                if ($inventory->status !== 'active') {
                    $validator->errors()->add('inventory_id', 'Cannot adjust inactive inventory.');
                }
            }

            // Validate quantity constraints for decrease adjustments
            if ($this->has('adjustment_type') && $this->has('quantity_adjusted')) {
                $inventoryId = $this->inventory_id ?? $adjustment->inventory_id;
                $inventory = \App\Models\Inventory::find($inventoryId);
                
                if ($inventory && in_array($this->adjustment_type, ['decrease', 'damage', 'expiry', 'theft'])) {
                    $adjustmentQuantity = abs($this->quantity_adjusted);
                    
                    // Calculate available quantity considering the original adjustment
                    $availableQuantity = $inventory->quantity_available;
                    if ($adjustment->quantity_adjusted < 0) {
                        $availableQuantity += abs($adjustment->quantity_adjusted); // Add back original decrease
                    }
                    
                    if ($adjustmentQuantity > $availableQuantity) {
                        $validator->errors()->add('quantity_adjusted', 
                            'Cannot adjust more than available quantity (' . $availableQuantity . ').');
                    }
                }
            }

            // Validate approval logic
            if ($this->has('approved_by') || $this->has('approved_at')) {
                // Both approved_by and approved_at should be provided together
                if (($this->has('approved_by') && !$this->approved_by) || 
                    ($this->has('approved_at') && !$this->approved_at)) {
                    if ($this->approved_by || $this->approved_at) {
                        $validator->errors()->add('approved_by', 'Both approver and approval date are required for approval.');
                    }
                }

                // Validate approver has permission
                if ($this->has('approved_by') && $this->approved_by) {
                    $approver = \App\Models\User::find($this->approved_by);
                    if ($approver && !$approver->hasAnyRole(['admin', 'pharmacist'])) {
                        $validator->errors()->add('approved_by', 'Selected user does not have permission to approve adjustments.');
                    }

                    // Cannot approve own adjustment (unless admin)
                    if ($this->approved_by === $adjustment->adjusted_by && !$this->user()->hasRole('admin')) {
                        $validator->errors()->add('approved_by', 'Cannot approve your own adjustment.');
                    }
                }
            }

            // Validate cost impact calculation
            if ($this->has('cost_impact') && $this->cost_impact !== null) {
                $inventoryId = $this->inventory_id ?? $adjustment->inventory_id;
                $quantityAdjusted = $this->quantity_adjusted ?? $adjustment->quantity_adjusted;
                
                $inventory = \App\Models\Inventory::find($inventoryId);
                if ($inventory) {
                    $expectedCostImpact = abs($quantityAdjusted) * $inventory->cost_price;
                    $tolerance = 0.01; // 1 cent tolerance
                    
                    if (abs($this->cost_impact - $expectedCostImpact) > $tolerance) {
                        $validator->errors()->add('cost_impact', 
                            'Cost impact does not match expected value (' . number_format($expectedCostImpact, 2) . ').');
                    }
                }
            }

            // Validate metadata structure
            if ($this->has('metadata') && $this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }

            // Validate adjustment type specific rules
            if ($this->has('adjustment_type')) {
                switch ($this->adjustment_type) {
                    case 'expiry':
                        $inventoryId = $this->inventory_id ?? $adjustment->inventory_id;
                        $inventory = \App\Models\Inventory::find($inventoryId);
                        if ($inventory && !$inventory->isExpiringSoon(30)) {
                            $validator->errors()->add('adjustment_type', 
                                'Expiry adjustment should only be used for items expiring within 30 days.');
                        }
                        break;

                    case 'return':
                        $referenceNumber = $this->reference_number ?? $adjustment->reference_number;
                        if (!$referenceNumber || !str_contains(strtolower($referenceNumber), 'ret')) {
                            $validator->errors()->add('reference_number', 
                                'Return adjustments should include return reference number.');
                        }
                        break;

                    case 'correction':
                        $reason = $this->reason ?? $adjustment->reason;
                        if (strlen($reason) < 20) {
                            $validator->errors()->add('reason', 
                                'Correction adjustments require detailed reason (minimum 20 characters).');
                        }
                        break;
                }
            }

            // Prevent changing critical fields after approval
            if ($adjustment->approved_at) {
                $criticalFields = ['inventory_id', 'adjustment_type', 'quantity_adjusted'];
                foreach ($criticalFields as $field) {
                    if ($this->has($field) && $this->$field != $adjustment->$field) {
                        $validator->errors()->add($field, 'Cannot change ' . str_replace('_', ' ', $field) . ' after approval.');
                    }
                }
            }
        });
    }
}
