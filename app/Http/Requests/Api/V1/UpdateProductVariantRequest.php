<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateProductVariantRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'pharmacist']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $variantId = $this->route('productVariant')->id;

        return [
            'product_id' => 'sometimes|required|exists:products,id',
            'variant_type' => [
                'sometimes',
                'required',
                'string',
                'in:size,strength,packaging,form,flavor,color'
            ],
            'variant_value' => 'sometimes|required|string|max:255',
            'sku' => [
                'sometimes',
                'nullable',
                'string',
                'max:100',
                'regex:/^[A-Z0-9-]+$/',
                Rule::unique('product_variants')->ignore($variantId)
            ],
            'barcode' => [
                'sometimes',
                'nullable',
                'string',
                'max:50',
                'regex:/^[0-9]{8,13}$/',
                Rule::unique('product_variants')->ignore($variantId)
            ],
            'unit_size' => 'sometimes|required|numeric|min:0.001|max:999999.999',
            'unit_type' => [
                'sometimes',
                'required',
                'string',
                'in:tablet,capsule,ml,mg,g,kg,piece,bottle,box,strip,vial,tube,sachet'
            ],
            'conversion_factor' => 'sometimes|required|numeric|min:0.001|max:999999.999',
            'cost_price' => 'sometimes|required|numeric|min:0|max:99999.99',
            'selling_price' => 'sometimes|required|numeric|min:0|max:99999.99',
            'mrp' => 'sometimes|required|numeric|min:0|max:99999.99',
            'minimum_stock_level' => 'sometimes|nullable|integer|min:0|max:999999',
            'maximum_stock_level' => 'sometimes|nullable|integer|min:0|max:999999',
            'reorder_point' => 'sometimes|nullable|integer|min:0|max:999999',
            'is_active' => 'sometimes|boolean',
            'is_default' => 'sometimes|boolean',
            'sort_order' => 'sometimes|integer|min:0|max:999',
            'image' => 'sometimes|nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'notes' => 'sometimes|nullable|string|max:1000',
            'metadata' => 'sometimes|nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'product_id.required' => 'Product is required.',
            'product_id.exists' => 'Selected product does not exist.',
            'variant_type.required' => 'Variant type is required.',
            'variant_type.in' => 'Variant type must be one of: size, strength, packaging, form, flavor, color.',
            'variant_value.required' => 'Variant value is required.',
            'variant_value.max' => 'Variant value may not be greater than 255 characters.',
            'sku.regex' => 'SKU must contain only uppercase letters, numbers, and hyphens.',
            'sku.unique' => 'This SKU already exists.',
            'barcode.regex' => 'Barcode must be 8-13 digits.',
            'barcode.unique' => 'This barcode already exists.',
            'unit_size.required' => 'Unit size is required.',
            'unit_size.min' => 'Unit size must be at least 0.001.',
            'unit_size.max' => 'Unit size may not be greater than 999,999.999.',
            'unit_type.required' => 'Unit type is required.',
            'unit_type.in' => 'Unit type must be one of: tablet, capsule, ml, mg, g, kg, piece, bottle, box, strip, vial, tube, sachet.',
            'conversion_factor.required' => 'Conversion factor is required.',
            'conversion_factor.min' => 'Conversion factor must be at least 0.001.',
            'conversion_factor.max' => 'Conversion factor may not be greater than 999,999.999.',
            'cost_price.required' => 'Cost price is required.',
            'cost_price.min' => 'Cost price must be at least 0.',
            'cost_price.max' => 'Cost price may not be greater than 99,999.99.',
            'selling_price.required' => 'Selling price is required.',
            'selling_price.min' => 'Selling price must be at least 0.',
            'selling_price.max' => 'Selling price may not be greater than 99,999.99.',
            'mrp.required' => 'MRP is required.',
            'mrp.min' => 'MRP must be at least 0.',
            'mrp.max' => 'MRP may not be greater than 99,999.99.',
            'minimum_stock_level.min' => 'Minimum stock level must be at least 0.',
            'minimum_stock_level.max' => 'Minimum stock level may not be greater than 999,999.',
            'maximum_stock_level.min' => 'Maximum stock level must be at least 0.',
            'maximum_stock_level.max' => 'Maximum stock level may not be greater than 999,999.',
            'reorder_point.min' => 'Reorder point must be at least 0.',
            'reorder_point.max' => 'Reorder point may not be greater than 999,999.',
            'sort_order.min' => 'Sort order must be at least 0.',
            'sort_order.max' => 'Sort order may not be greater than 999.',
            'image.image' => 'File must be an image.',
            'image.mimes' => 'Image must be a file of type: jpeg, png, jpg, gif, svg.',
            'image.max' => 'Image may not be greater than 2MB.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'product_id' => 'product',
            'variant_type' => 'variant type',
            'variant_value' => 'variant value',
            'unit_size' => 'unit size',
            'unit_type' => 'unit type',
            'conversion_factor' => 'conversion factor',
            'cost_price' => 'cost price',
            'selling_price' => 'selling price',
            'mrp' => 'MRP',
            'minimum_stock_level' => 'minimum stock level',
            'maximum_stock_level' => 'maximum stock level',
            'reorder_point' => 'reorder point',
            'is_active' => 'active status',
            'is_default' => 'default variant',
            'sort_order' => 'sort order',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        if ($this->has('sku') && $this->sku === '') {
            $this->merge(['sku' => null]);
        }
        
        if ($this->has('barcode') && $this->barcode === '') {
            $this->merge(['barcode' => null]);
        }
        
        if ($this->has('minimum_stock_level') && $this->minimum_stock_level === '') {
            $this->merge(['minimum_stock_level' => null]);
        }
        
        if ($this->has('maximum_stock_level') && $this->maximum_stock_level === '') {
            $this->merge(['maximum_stock_level' => null]);
        }
        
        if ($this->has('reorder_point') && $this->reorder_point === '') {
            $this->merge(['reorder_point' => null]);
        }
        
        if ($this->has('notes') && $this->notes === '') {
            $this->merge(['notes' => null]);
        }
        
        if ($this->has('metadata') && $this->metadata === '') {
            $this->merge(['metadata' => null]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $variant = $this->route('productVariant');
            
            // Validate product exists and is active
            if ($this->has('product_id')) {
                $product = \App\Models\Product::find($this->product_id);
                if ($product && !$product->is_active) {
                    $validator->errors()->add('product_id', 'Cannot move variant to inactive product.');
                }
            }

            // Validate variant combination uniqueness (excluding current record)
            if ($this->has('variant_type') || $this->has('variant_value') || $this->has('product_id')) {
                $productId = $this->product_id ?? $variant->product_id;
                $variantType = $this->variant_type ?? $variant->variant_type;
                $variantValue = $this->variant_value ?? $variant->variant_value;

                $existingVariant = \App\Models\ProductVariant::where('product_id', $productId)
                    ->where('variant_type', $variantType)
                    ->where('variant_value', $variantValue)
                    ->where('id', '!=', $variant->id)
                    ->exists();

                if ($existingVariant) {
                    $validator->errors()->add('variant_value', 'This variant combination already exists for this product.');
                }
            }

            // Validate pricing logic
            $costPrice = $this->cost_price ?? $variant->cost_price;
            $sellingPrice = $this->selling_price ?? $variant->selling_price;
            $mrp = $this->mrp ?? $variant->mrp;

            if ($costPrice && $sellingPrice && $costPrice > $sellingPrice) {
                $validator->errors()->add('selling_price', 'Selling price should be greater than or equal to cost price.');
            }

            if ($sellingPrice && $mrp && $sellingPrice > $mrp) {
                $validator->errors()->add('selling_price', 'Selling price cannot be greater than MRP.');
            }

            // Validate stock level logic
            $minStock = $this->minimum_stock_level ?? $variant->minimum_stock_level;
            $maxStock = $this->maximum_stock_level ?? $variant->maximum_stock_level;
            $reorderPoint = $this->reorder_point ?? $variant->reorder_point;

            if ($minStock && $maxStock && $minStock > $maxStock) {
                $validator->errors()->add('maximum_stock_level', 'Maximum stock level must be greater than minimum stock level.');
            }

            if ($reorderPoint && $minStock && $reorderPoint < $minStock) {
                $validator->errors()->add('reorder_point', 'Reorder point should be greater than or equal to minimum stock level.');
            }

            if ($reorderPoint && $maxStock && $reorderPoint > $maxStock) {
                $validator->errors()->add('reorder_point', 'Reorder point should be less than or equal to maximum stock level.');
            }

            // Validate default variant logic
            if ($this->has('is_default') && $this->is_default) {
                $productId = $this->product_id ?? $variant->product_id;
                
                $existingDefault = \App\Models\ProductVariant::where('product_id', $productId)
                    ->where('is_default', true)
                    ->where('id', '!=', $variant->id)
                    ->exists();

                if ($existingDefault) {
                    $validator->errors()->add('is_default', 'Product already has a default variant. Please unset the current default first.');
                }
            }

            // Prevent deactivating if it's the only active variant
            if ($this->has('is_active') && !$this->is_active) {
                $productId = $this->product_id ?? $variant->product_id;
                
                $otherActiveVariants = \App\Models\ProductVariant::where('product_id', $productId)
                    ->where('is_active', true)
                    ->where('id', '!=', $variant->id)
                    ->exists();

                if (!$otherActiveVariants) {
                    $validator->errors()->add('is_active', 'Cannot deactivate the only active variant for this product.');
                }

                // Check if variant has active inventory
                $hasActiveInventory = \App\Models\Inventory::where('product_variant_id', $variant->id)
                    ->where('quantity_available', '>', 0)
                    ->exists();

                if ($hasActiveInventory) {
                    $validator->errors()->add('is_active', 'Cannot deactivate variant with available inventory.');
                }
            }

            // Prevent deactivating default variant
            if ($this->has('is_active') && !$this->is_active && $variant->is_default) {
                $validator->errors()->add('is_active', 'Cannot deactivate default variant. Please set another variant as default first.');
            }

            // Validate barcode format (EAN-13 check digit)
            if ($this->has('barcode') && $this->barcode && strlen($this->barcode) === 13) {
                if (!$this->isValidEAN13($this->barcode)) {
                    $validator->errors()->add('barcode', 'Invalid EAN-13 barcode check digit.');
                }
            }

            // Validate metadata structure
            if ($this->has('metadata') && $this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }

            // Validate variant value format based on type
            if ($this->has('variant_type') && $this->has('variant_value')) {
                switch ($this->variant_type) {
                    case 'strength':
                        if (!preg_match('/^\d+(\.\d+)?\s*(mg|g|ml|%|IU|mcg)$/i', $this->variant_value)) {
                            $validator->errors()->add('variant_value', 'Strength must include numeric value and unit (e.g., "500mg", "2.5ml").');
                        }
                        break;

                    case 'size':
                        if (!preg_match('/^\d+(\.\d+)?\s*(ml|g|kg|oz|lb|tablet|capsule)s?$/i', $this->variant_value)) {
                            $validator->errors()->add('variant_value', 'Size must include numeric value and unit (e.g., "100ml", "30 tablets").');
                        }
                        break;
                }
            }

            // Validate price changes don't affect existing inventory significantly
            if ($this->has('cost_price') || $this->has('selling_price')) {
                $newCostPrice = $this->cost_price ?? $variant->cost_price;
                $newSellingPrice = $this->selling_price ?? $variant->selling_price;
                
                $priceChangeThreshold = 0.2; // 20% change threshold
                
                if ($this->has('cost_price') && $variant->cost_price > 0) {
                    $costPriceChange = abs($newCostPrice - $variant->cost_price) / $variant->cost_price;
                    if ($costPriceChange > $priceChangeThreshold) {
                        $validator->errors()->add('cost_price', 'Cost price change exceeds 20%. This may significantly impact inventory valuation.');
                    }
                }
                
                if ($this->has('selling_price') && $variant->selling_price > 0) {
                    $sellingPriceChange = abs($newSellingPrice - $variant->selling_price) / $variant->selling_price;
                    if ($sellingPriceChange > $priceChangeThreshold) {
                        $validator->errors()->add('selling_price', 'Selling price change exceeds 20%. This may affect existing orders.');
                    }
                }
            }
        });
    }

    /**
     * Validate EAN-13 barcode check digit.
     */
    private function isValidEAN13($barcode): bool
    {
        if (strlen($barcode) !== 13 || !ctype_digit($barcode)) {
            return false;
        }

        $sum = 0;
        for ($i = 0; $i < 12; $i++) {
            $sum += (int)$barcode[$i] * (($i % 2 === 0) ? 1 : 3);
        }

        $checkDigit = (10 - ($sum % 10)) % 10;
        return $checkDigit === (int)$barcode[12];
    }
}
