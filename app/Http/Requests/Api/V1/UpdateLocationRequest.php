<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateLocationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'pharmacist']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $locationId = $this->route('location')->id;

        return [
            'location_code' => [
                'sometimes',
                'nullable',
                'string',
                'max:50',
                'regex:/^[A-Z0-9-]+$/',
                Rule::unique('locations')->ignore($locationId)
            ],
            'name' => 'sometimes|required|string|max:255',
            'description' => 'sometimes|nullable|string|max:1000',
            'location_type' => [
                'sometimes',
                'required',
                'string',
                'in:warehouse,shelf,bin,refrigerator,freezer,controlled,quarantine,receiving,shipping'
            ],
            'parent_location_id' => 'sometimes|nullable|exists:locations,id',
            'aisle' => 'sometimes|nullable|string|max:10',
            'shelf' => 'sometimes|nullable|string|max:10',
            'bin' => 'sometimes|nullable|string|max:10',
            'level' => 'sometimes|nullable|integer|min:0|max:10',
            'capacity' => 'sometimes|nullable|integer|min:1|max:999999',
            'current_stock_count' => 'sometimes|nullable|integer|min:0|max:999999',
            'temperature_min' => 'sometimes|nullable|numeric|min:-50|max:50',
            'temperature_max' => 'sometimes|nullable|numeric|min:-50|max:50',
            'humidity_min' => 'sometimes|nullable|numeric|min:0|max:100',
            'humidity_max' => 'sometimes|nullable|numeric|min:0|max:100',
            'requires_prescription_access' => 'sometimes|boolean',
            'requires_controlled_access' => 'sometimes|boolean',
            'is_active' => 'sometimes|boolean',
            'is_pickable' => 'sometimes|boolean',
            'is_receivable' => 'sometimes|boolean',
            'barcode' => [
                'sometimes',
                'nullable',
                'string',
                'max:50',
                'regex:/^[0-9A-Z\-]+$/',
                Rule::unique('locations')->ignore($locationId)
            ],
            'sort_order' => 'sometimes|integer|min:0|max:999',
            'notes' => 'sometimes|nullable|string|max:1000',
            'metadata' => 'sometimes|nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'location_code.regex' => 'Location code must contain only uppercase letters, numbers, and hyphens.',
            'location_code.unique' => 'This location code already exists.',
            'name.required' => 'Location name is required.',
            'name.max' => 'Location name may not be greater than 255 characters.',
            'description.max' => 'Description may not be greater than 1000 characters.',
            'location_type.required' => 'Location type is required.',
            'location_type.in' => 'Location type must be one of: warehouse, shelf, bin, refrigerator, freezer, controlled, quarantine, receiving, shipping.',
            'parent_location_id.exists' => 'Selected parent location does not exist.',
            'aisle.max' => 'Aisle may not be greater than 10 characters.',
            'shelf.max' => 'Shelf may not be greater than 10 characters.',
            'bin.max' => 'Bin may not be greater than 10 characters.',
            'level.min' => 'Level must be at least 0.',
            'level.max' => 'Level may not be greater than 10.',
            'capacity.min' => 'Capacity must be at least 1.',
            'capacity.max' => 'Capacity may not be greater than 999,999.',
            'current_stock_count.min' => 'Current stock count must be at least 0.',
            'current_stock_count.max' => 'Current stock count may not be greater than 999,999.',
            'temperature_min.min' => 'Minimum temperature must be at least -50°C.',
            'temperature_min.max' => 'Minimum temperature may not be greater than 50°C.',
            'temperature_max.min' => 'Maximum temperature must be at least -50°C.',
            'temperature_max.max' => 'Maximum temperature may not be greater than 50°C.',
            'humidity_min.min' => 'Minimum humidity must be at least 0%.',
            'humidity_min.max' => 'Minimum humidity may not be greater than 100%.',
            'humidity_max.min' => 'Maximum humidity must be at least 0%.',
            'humidity_max.max' => 'Maximum humidity may not be greater than 100%.',
            'barcode.regex' => 'Barcode must contain only numbers, uppercase letters, and hyphens.',
            'barcode.unique' => 'This barcode already exists.',
            'sort_order.min' => 'Sort order must be at least 0.',
            'sort_order.max' => 'Sort order may not be greater than 999.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'location_code' => 'location code',
            'location_type' => 'location type',
            'parent_location_id' => 'parent location',
            'current_stock_count' => 'current stock count',
            'temperature_min' => 'minimum temperature',
            'temperature_max' => 'maximum temperature',
            'humidity_min' => 'minimum humidity',
            'humidity_max' => 'maximum humidity',
            'requires_prescription_access' => 'requires prescription access',
            'requires_controlled_access' => 'requires controlled access',
            'is_active' => 'active status',
            'is_pickable' => 'pickable',
            'is_receivable' => 'receivable',
            'sort_order' => 'sort order',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        $nullableFields = [
            'location_code', 'description', 'parent_location_id', 'aisle', 'shelf',
            'bin', 'level', 'capacity', 'current_stock_count', 'temperature_min',
            'temperature_max', 'humidity_min', 'humidity_max', 'barcode', 'notes', 'metadata'
        ];

        foreach ($nullableFields as $field) {
            if ($this->has($field) && $this->$field === '') {
                $this->merge([$field => null]);
            }
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $location = $this->route('location');
            
            // Validate parent location hierarchy
            if ($this->has('parent_location_id')) {
                $parentLocationId = $this->parent_location_id;
                
                if ($parentLocationId) {
                    // Cannot set self as parent
                    if ($parentLocationId === $location->id) {
                        $validator->errors()->add('parent_location_id', 'Location cannot be its own parent.');
                        return;
                    }

                    // Cannot create circular reference
                    if ($this->wouldCreateCircularReference($location->id, $parentLocationId)) {
                        $validator->errors()->add('parent_location_id', 'This would create a circular reference in the location hierarchy.');
                        return;
                    }

                    $parentLocation = \App\Models\Location::find($parentLocationId);
                    
                    if ($parentLocation) {
                        // Parent must be active
                        if (!$parentLocation->is_active) {
                            $validator->errors()->add('parent_location_id', 'Cannot assign inactive parent location.');
                        }

                        // Validate hierarchy depth (max 5 levels)
                        $depth = $this->calculateLocationDepth($parentLocation);
                        if ($depth >= 5) {
                            $validator->errors()->add('parent_location_id', 'Location hierarchy cannot exceed 5 levels.');
                        }

                        // Validate location type hierarchy rules
                        $locationType = $this->location_type ?? $location->location_type;
                        $validChildTypes = [
                            'warehouse' => ['shelf', 'refrigerator', 'freezer', 'controlled', 'quarantine', 'receiving', 'shipping'],
                            'shelf' => ['bin'],
                            'bin' => [],
                            'refrigerator' => ['shelf', 'bin'],
                            'freezer' => ['shelf', 'bin'],
                            'controlled' => ['shelf', 'bin'],
                            'quarantine' => ['shelf', 'bin'],
                            'receiving' => [],
                            'shipping' => []
                        ];

                        if (!in_array($locationType, $validChildTypes[$parentLocation->location_type] ?? [])) {
                            $validator->errors()->add('location_type', "Location type '{$locationType}' cannot be a child of '{$parentLocation->location_type}'.");
                        }
                    }
                }
            }

            // Validate temperature range
            $tempMin = $this->temperature_min ?? $location->temperature_min;
            $tempMax = $this->temperature_max ?? $location->temperature_max;
            
            if ($tempMin && $tempMax && $tempMin >= $tempMax) {
                $validator->errors()->add('temperature_max', 'Maximum temperature must be greater than minimum temperature.');
            }

            // Validate humidity range
            $humidityMin = $this->humidity_min ?? $location->humidity_min;
            $humidityMax = $this->humidity_max ?? $location->humidity_max;
            
            if ($humidityMin && $humidityMax && $humidityMin >= $humidityMax) {
                $validator->errors()->add('humidity_max', 'Maximum humidity must be greater than minimum humidity.');
            }

            // Validate capacity vs current stock
            $capacity = $this->capacity ?? $location->capacity;
            $currentStock = $this->current_stock_count ?? $location->current_stock_count;
            
            if ($capacity && $currentStock && $currentStock > $capacity) {
                $validator->errors()->add('current_stock_count', 'Current stock count cannot exceed capacity.');
            }

            // Validate deactivation constraints
            if ($this->has('is_active') && !$this->is_active && $location->is_active) {
                // Cannot deactivate if has active child locations
                $hasActiveChildren = \App\Models\Location::where('parent_location_id', $location->id)
                    ->where('is_active', true)
                    ->exists();

                if ($hasActiveChildren) {
                    $validator->errors()->add('is_active', 'Cannot deactivate location with active child locations.');
                }

                // Cannot deactivate if has active inventory
                $hasActiveInventory = \App\Models\Inventory::where('location_id', $location->id)
                    ->where('quantity_available', '>', 0)
                    ->exists();

                if ($hasActiveInventory) {
                    $validator->errors()->add('is_active', 'Cannot deactivate location with active inventory.');
                }
            }

            // Validate location type specific requirements
            $locationType = $this->location_type ?? $location->location_type;
            
            switch ($locationType) {
                case 'refrigerator':
                case 'freezer':
                    if (!$tempMin || !$tempMax) {
                        $validator->errors()->add('temperature_min', 'Temperature range is required for refrigerated/frozen locations.');
                    }
                    break;

                case 'controlled':
                    $requiresControlled = $this->requires_controlled_access ?? $location->requires_controlled_access;
                    if (!$requiresControlled) {
                        $validator->errors()->add('requires_controlled_access', 'Controlled locations must require controlled access.');
                    }
                    break;

                case 'quarantine':
                    $isPickable = $this->is_pickable ?? $location->is_pickable;
                    if ($isPickable) {
                        $validator->errors()->add('is_pickable', 'Quarantine locations should not be pickable.');
                    }
                    break;

                case 'receiving':
                    $isReceivable = $this->is_receivable ?? $location->is_receivable;
                    $isPickable = $this->is_pickable ?? $location->is_pickable;
                    
                    if (!$isReceivable) {
                        $validator->errors()->add('is_receivable', 'Receiving locations must be receivable.');
                    }
                    if ($isPickable) {
                        $validator->errors()->add('is_pickable', 'Receiving locations should not be pickable.');
                    }
                    break;

                case 'shipping':
                    $isReceivable = $this->is_receivable ?? $location->is_receivable;
                    if ($isReceivable) {
                        $validator->errors()->add('is_receivable', 'Shipping locations should not be receivable.');
                    }
                    break;
            }

            // Validate name uniqueness within same parent
            if ($this->has('name')) {
                $parentLocationId = $this->parent_location_id ?? $location->parent_location_id;
                
                if ($parentLocationId) {
                    $existingLocation = \App\Models\Location::where('parent_location_id', $parentLocationId)
                        ->where('name', $this->name)
                        ->where('id', '!=', $location->id)
                        ->exists();

                    if ($existingLocation) {
                        $validator->errors()->add('name', 'Location name must be unique within the same parent location.');
                    }
                } else {
                    // Root level locations should have unique names
                    $existingLocation = \App\Models\Location::whereNull('parent_location_id')
                        ->where('name', $this->name)
                        ->where('id', '!=', $location->id)
                        ->exists();

                    if ($existingLocation) {
                        $validator->errors()->add('name', 'Root level location name must be unique.');
                    }
                }
            }

            // Validate metadata structure
            if ($this->has('metadata') && $this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }

            // Validate level consistency with hierarchy
            if ($this->has('level') && $this->level !== null) {
                $parentLocationId = $this->parent_location_id ?? $location->parent_location_id;
                
                if ($parentLocationId) {
                    $parentLocation = \App\Models\Location::find($parentLocationId);
                    if ($parentLocation && $parentLocation->level !== null) {
                        if ($this->level <= $parentLocation->level) {
                            $validator->errors()->add('level', 'Location level must be greater than parent location level.');
                        }
                    }
                }
            }
        });
    }

    /**
     * Check if changing parent would create a circular reference.
     */
    private function wouldCreateCircularReference($locationId, $newParentId, $depth = 0): bool
    {
        if ($depth > 10) { // Prevent infinite recursion
            return true;
        }

        if ($newParentId === $locationId) {
            return true;
        }

        $parent = \App\Models\Location::find($newParentId);
        if (!$parent || !$parent->parent_location_id) {
            return false;
        }

        return $this->wouldCreateCircularReference($locationId, $parent->parent_location_id, $depth + 1);
    }

    /**
     * Calculate the depth of a location in the hierarchy.
     */
    private function calculateLocationDepth($location, $depth = 0): int
    {
        if (!$location->parent_location_id || $depth >= 10) { // Prevent infinite recursion
            return $depth;
        }

        $parent = \App\Models\Location::find($location->parent_location_id);
        if (!$parent) {
            return $depth;
        }

        return $this->calculateLocationDepth($parent, $depth + 1);
    }
}
