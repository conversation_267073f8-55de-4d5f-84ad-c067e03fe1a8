<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreProductVariantRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'pharmacist']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'product_id' => 'required|exists:products,id',
            'variant_type' => [
                'required',
                'string',
                'in:size,strength,packaging,form,flavor,color'
            ],
            'variant_value' => 'required|string|max:255',
            'sku' => [
                'nullable',
                'string',
                'max:100',
                'regex:/^[A-Z0-9-]+$/',
                'unique:product_variants,sku'
            ],
            'barcode' => [
                'nullable',
                'string',
                'max:50',
                'regex:/^[0-9]{8,13}$/',
                'unique:product_variants,barcode'
            ],
            'unit_size' => 'required|numeric|min:0.001|max:999999.999',
            'unit_type' => [
                'required',
                'string',
                'in:tablet,capsule,ml,mg,g,kg,piece,bottle,box,strip,vial,tube,sachet'
            ],
            'conversion_factor' => 'required|numeric|min:0.001|max:999999.999',
            'cost_price' => 'required|numeric|min:0|max:99999.99',
            'selling_price' => 'required|numeric|min:0|max:99999.99',
            'mrp' => 'required|numeric|min:0|max:99999.99',
            'minimum_stock_level' => 'nullable|integer|min:0|max:999999',
            'maximum_stock_level' => 'nullable|integer|min:0|max:999999',
            'reorder_point' => 'nullable|integer|min:0|max:999999',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'sort_order' => 'integer|min:0|max:999',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'notes' => 'nullable|string|max:1000',
            'metadata' => 'nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'product_id.required' => 'Product is required.',
            'product_id.exists' => 'Selected product does not exist.',
            'variant_type.required' => 'Variant type is required.',
            'variant_type.in' => 'Variant type must be one of: size, strength, packaging, form, flavor, color.',
            'variant_value.required' => 'Variant value is required.',
            'variant_value.max' => 'Variant value may not be greater than 255 characters.',
            'sku.regex' => 'SKU must contain only uppercase letters, numbers, and hyphens.',
            'sku.unique' => 'This SKU already exists.',
            'barcode.regex' => 'Barcode must be 8-13 digits.',
            'barcode.unique' => 'This barcode already exists.',
            'unit_size.required' => 'Unit size is required.',
            'unit_size.min' => 'Unit size must be at least 0.001.',
            'unit_size.max' => 'Unit size may not be greater than 999,999.999.',
            'unit_type.required' => 'Unit type is required.',
            'unit_type.in' => 'Unit type must be one of: tablet, capsule, ml, mg, g, kg, piece, bottle, box, strip, vial, tube, sachet.',
            'conversion_factor.required' => 'Conversion factor is required.',
            'conversion_factor.min' => 'Conversion factor must be at least 0.001.',
            'conversion_factor.max' => 'Conversion factor may not be greater than 999,999.999.',
            'cost_price.required' => 'Cost price is required.',
            'cost_price.min' => 'Cost price must be at least 0.',
            'cost_price.max' => 'Cost price may not be greater than 99,999.99.',
            'selling_price.required' => 'Selling price is required.',
            'selling_price.min' => 'Selling price must be at least 0.',
            'selling_price.max' => 'Selling price may not be greater than 99,999.99.',
            'mrp.required' => 'MRP is required.',
            'mrp.min' => 'MRP must be at least 0.',
            'mrp.max' => 'MRP may not be greater than 99,999.99.',
            'minimum_stock_level.min' => 'Minimum stock level must be at least 0.',
            'minimum_stock_level.max' => 'Minimum stock level may not be greater than 999,999.',
            'maximum_stock_level.min' => 'Maximum stock level must be at least 0.',
            'maximum_stock_level.max' => 'Maximum stock level may not be greater than 999,999.',
            'reorder_point.min' => 'Reorder point must be at least 0.',
            'reorder_point.max' => 'Reorder point may not be greater than 999,999.',
            'sort_order.min' => 'Sort order must be at least 0.',
            'sort_order.max' => 'Sort order may not be greater than 999.',
            'image.image' => 'File must be an image.',
            'image.mimes' => 'Image must be a file of type: jpeg, png, jpg, gif, svg.',
            'image.max' => 'Image may not be greater than 2MB.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'product_id' => 'product',
            'variant_type' => 'variant type',
            'variant_value' => 'variant value',
            'unit_size' => 'unit size',
            'unit_type' => 'unit type',
            'conversion_factor' => 'conversion factor',
            'cost_price' => 'cost price',
            'selling_price' => 'selling price',
            'mrp' => 'MRP',
            'minimum_stock_level' => 'minimum stock level',
            'maximum_stock_level' => 'maximum stock level',
            'reorder_point' => 'reorder point',
            'is_active' => 'active status',
            'is_default' => 'default variant',
            'sort_order' => 'sort order',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        $this->merge([
            'sku' => $this->sku === '' ? null : $this->sku,
            'barcode' => $this->barcode === '' ? null : $this->barcode,
            'minimum_stock_level' => $this->minimum_stock_level === '' ? null : $this->minimum_stock_level,
            'maximum_stock_level' => $this->maximum_stock_level === '' ? null : $this->maximum_stock_level,
            'reorder_point' => $this->reorder_point === '' ? null : $this->reorder_point,
            'image' => $this->image === '' ? null : $this->image,
            'notes' => $this->notes === '' ? null : $this->notes,
            'metadata' => $this->metadata === '' ? null : $this->metadata,
        ]);

        // Set default values
        if (!$this->has('is_active')) {
            $this->merge(['is_active' => true]);
        }

        if (!$this->has('is_default')) {
            $this->merge(['is_default' => false]);
        }

        if (!$this->has('sort_order')) {
            $this->merge(['sort_order' => 0]);
        }

        // Auto-generate SKU if not provided
        if (!$this->sku && $this->product_id && $this->variant_value) {
            $product = \App\Models\Product::find($this->product_id);
            if ($product) {
                $baseSku = $product->sku ?? 'PROD-' . $this->product_id;
                $variantSuffix = strtoupper(substr(preg_replace('/[^A-Z0-9]/', '', $this->variant_value), 0, 6));
                $this->merge(['sku' => $baseSku . '-' . $variantSuffix]);
            }
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate product exists and is active
            if ($this->product_id) {
                $product = \App\Models\Product::find($this->product_id);
                if ($product && !$product->is_active) {
                    $validator->errors()->add('product_id', 'Cannot create variant for inactive product.');
                }
            }

            // Validate variant combination uniqueness
            if ($this->product_id && $this->variant_type && $this->variant_value) {
                $existingVariant = \App\Models\ProductVariant::where('product_id', $this->product_id)
                    ->where('variant_type', $this->variant_type)
                    ->where('variant_value', $this->variant_value)
                    ->exists();

                if ($existingVariant) {
                    $validator->errors()->add('variant_value', 'This variant combination already exists for this product.');
                }
            }

            // Validate pricing logic
            if ($this->cost_price && $this->selling_price && $this->cost_price > $this->selling_price) {
                $validator->errors()->add('selling_price', 'Selling price should be greater than or equal to cost price.');
            }

            if ($this->selling_price && $this->mrp && $this->selling_price > $this->mrp) {
                $validator->errors()->add('selling_price', 'Selling price cannot be greater than MRP.');
            }

            // Validate stock level logic
            if ($this->minimum_stock_level && $this->maximum_stock_level && 
                $this->minimum_stock_level > $this->maximum_stock_level) {
                $validator->errors()->add('maximum_stock_level', 'Maximum stock level must be greater than minimum stock level.');
            }

            if ($this->reorder_point && $this->minimum_stock_level && 
                $this->reorder_point < $this->minimum_stock_level) {
                $validator->errors()->add('reorder_point', 'Reorder point should be greater than or equal to minimum stock level.');
            }

            if ($this->reorder_point && $this->maximum_stock_level && 
                $this->reorder_point > $this->maximum_stock_level) {
                $validator->errors()->add('reorder_point', 'Reorder point should be less than or equal to maximum stock level.');
            }

            // Validate conversion factor logic
            if ($this->conversion_factor && $this->unit_size) {
                // Conversion factor should make sense with unit size
                if ($this->conversion_factor > 1000 && $this->unit_size < 1) {
                    $validator->errors()->add('conversion_factor', 'Conversion factor seems unusually high for this unit size.');
                }
            }

            // Validate unit type compatibility with variant type
            if ($this->variant_type && $this->unit_type) {
                $incompatibleCombinations = [
                    'strength' => ['bottle', 'box', 'piece'],
                    'packaging' => ['mg', 'g', 'kg'],
                    'form' => ['ml'],
                ];

                if (isset($incompatibleCombinations[$this->variant_type]) && 
                    in_array($this->unit_type, $incompatibleCombinations[$this->variant_type])) {
                    $validator->errors()->add('unit_type', 'Unit type is not compatible with variant type.');
                }
            }

            // Validate default variant logic
            if ($this->is_default && $this->product_id) {
                $existingDefault = \App\Models\ProductVariant::where('product_id', $this->product_id)
                    ->where('is_default', true)
                    ->exists();

                if ($existingDefault) {
                    $validator->errors()->add('is_default', 'Product already has a default variant. Please unset the current default first.');
                }
            }

            // Validate barcode format (EAN-13 check digit)
            if ($this->barcode && strlen($this->barcode) === 13) {
                if (!$this->isValidEAN13($this->barcode)) {
                    $validator->errors()->add('barcode', 'Invalid EAN-13 barcode check digit.');
                }
            }

            // Validate metadata structure
            if ($this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }

            // Validate variant value format based on type
            switch ($this->variant_type) {
                case 'strength':
                    if (!preg_match('/^\d+(\.\d+)?\s*(mg|g|ml|%|IU|mcg)$/i', $this->variant_value)) {
                        $validator->errors()->add('variant_value', 'Strength must include numeric value and unit (e.g., "500mg", "2.5ml").');
                    }
                    break;

                case 'size':
                    if (!preg_match('/^\d+(\.\d+)?\s*(ml|g|kg|oz|lb|tablet|capsule)s?$/i', $this->variant_value)) {
                        $validator->errors()->add('variant_value', 'Size must include numeric value and unit (e.g., "100ml", "30 tablets").');
                    }
                    break;
            }
        });
    }

    /**
     * Validate EAN-13 barcode check digit.
     */
    private function isValidEAN13($barcode): bool
    {
        if (strlen($barcode) !== 13 || !ctype_digit($barcode)) {
            return false;
        }

        $sum = 0;
        for ($i = 0; $i < 12; $i++) {
            $sum += (int)$barcode[$i] * (($i % 2 === 0) ? 1 : 3);
        }

        $checkDigit = (10 - ($sum % 10)) % 10;
        return $checkDigit === (int)$barcode[12];
    }
}
