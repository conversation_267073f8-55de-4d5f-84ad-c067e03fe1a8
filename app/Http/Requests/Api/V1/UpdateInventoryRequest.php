<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class UpdateInventoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'pharmacist']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $inventoryId = $this->route('inventory')->id;

        return [
            'product_id' => 'sometimes|required|exists:products,id',
            'product_variant_id' => 'sometimes|nullable|exists:product_variants,id',
            'location_id' => 'sometimes|required|exists:locations,id',
            'batch_number' => 'sometimes|required|string|max:100',
            'quantity_received' => 'sometimes|required|integer|min:1|max:999999',
            'quantity_available' => 'sometimes|nullable|integer|min:0|max:999999',
            'quantity_reserved' => 'sometimes|nullable|integer|min:0|max:999999',
            'cost_price' => 'sometimes|required|numeric|min:0|max:99999.99',
            'selling_price' => 'sometimes|required|numeric|min:0|max:99999.99',
            'mrp' => 'sometimes|required|numeric|min:0|max:99999.99',
            'manufacturing_date' => [
                'sometimes',
                'required',
                'date',
                'before_or_equal:today',
                'after:1900-01-01'
            ],
            'expiry_date' => [
                'sometimes',
                'required',
                'date',
                'after:manufacturing_date',
                'after:today'
            ],
            'supplier_id' => 'sometimes|required|exists:suppliers,id',
            'purchase_order_id' => 'sometimes|nullable|exists:purchase_orders,id',
            'storage_conditions' => 'sometimes|nullable|string|max:500',
            'notes' => 'sometimes|nullable|string|max:1000',
            'status' => [
                'sometimes',
                'required',
                'string',
                'in:active,expired,damaged,recalled,quarantine'
            ],
            'metadata' => 'sometimes|nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'product_id.required' => 'Product is required.',
            'product_id.exists' => 'Selected product does not exist.',
            'product_variant_id.exists' => 'Selected product variant does not exist.',
            'location_id.required' => 'Storage location is required.',
            'location_id.exists' => 'Selected location does not exist.',
            'batch_number.required' => 'Batch number is required.',
            'batch_number.max' => 'Batch number may not be greater than 100 characters.',
            'quantity_received.required' => 'Quantity received is required.',
            'quantity_received.min' => 'Quantity received must be at least 1.',
            'quantity_received.max' => 'Quantity received may not be greater than 999,999.',
            'quantity_available.min' => 'Quantity available must be at least 0.',
            'quantity_available.max' => 'Quantity available may not be greater than 999,999.',
            'quantity_reserved.min' => 'Quantity reserved must be at least 0.',
            'quantity_reserved.max' => 'Quantity reserved may not be greater than 999,999.',
            'cost_price.required' => 'Cost price is required.',
            'cost_price.min' => 'Cost price must be at least 0.',
            'cost_price.max' => 'Cost price may not be greater than 99,999.99.',
            'selling_price.required' => 'Selling price is required.',
            'selling_price.min' => 'Selling price must be at least 0.',
            'selling_price.max' => 'Selling price may not be greater than 99,999.99.',
            'mrp.required' => 'MRP is required.',
            'mrp.min' => 'MRP must be at least 0.',
            'mrp.max' => 'MRP may not be greater than 99,999.99.',
            'manufacturing_date.required' => 'Manufacturing date is required.',
            'manufacturing_date.date' => 'Manufacturing date must be a valid date.',
            'manufacturing_date.before_or_equal' => 'Manufacturing date cannot be in the future.',
            'manufacturing_date.after' => 'Manufacturing date must be after 1900.',
            'expiry_date.required' => 'Expiry date is required.',
            'expiry_date.date' => 'Expiry date must be a valid date.',
            'expiry_date.after' => 'Expiry date must be after manufacturing date and today.',
            'supplier_id.required' => 'Supplier is required.',
            'supplier_id.exists' => 'Selected supplier does not exist.',
            'purchase_order_id.exists' => 'Selected purchase order does not exist.',
            'storage_conditions.max' => 'Storage conditions may not be greater than 500 characters.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            'status.required' => 'Status is required.',
            'status.in' => 'Status must be one of: active, expired, damaged, recalled, quarantine.',
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'product_id' => 'product',
            'product_variant_id' => 'product variant',
            'location_id' => 'storage location',
            'batch_number' => 'batch number',
            'quantity_received' => 'quantity received',
            'quantity_available' => 'quantity available',
            'quantity_reserved' => 'quantity reserved',
            'cost_price' => 'cost price',
            'selling_price' => 'selling price',
            'mrp' => 'MRP',
            'manufacturing_date' => 'manufacturing date',
            'expiry_date' => 'expiry date',
            'supplier_id' => 'supplier',
            'purchase_order_id' => 'purchase order',
            'storage_conditions' => 'storage conditions',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        if ($this->has('product_variant_id') && $this->product_variant_id === '') {
            $this->merge(['product_variant_id' => null]);
        }
        
        if ($this->has('purchase_order_id') && $this->purchase_order_id === '') {
            $this->merge(['purchase_order_id' => null]);
        }
        
        if ($this->has('storage_conditions') && $this->storage_conditions === '') {
            $this->merge(['storage_conditions' => null]);
        }
        
        if ($this->has('notes') && $this->notes === '') {
            $this->merge(['notes' => null]);
        }
        
        if ($this->has('metadata') && $this->metadata === '') {
            $this->merge(['metadata' => null]);
        }

        // Format dates
        if ($this->has('manufacturing_date') && $this->manufacturing_date) {
            try {
                $this->merge(['manufacturing_date' => Carbon::parse($this->manufacturing_date)->format('Y-m-d')]);
            } catch (\Exception $e) {
                // Let validation handle invalid dates
            }
        }

        if ($this->has('expiry_date') && $this->expiry_date) {
            try {
                $this->merge(['expiry_date' => Carbon::parse($this->expiry_date)->format('Y-m-d')]);
            } catch (\Exception $e) {
                // Let validation handle invalid dates
            }
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $inventoryId = $this->route('inventory')->id;
            $inventory = $this->route('inventory');
            
            // Validate product variant belongs to product
            if ($this->has('product_id') && $this->has('product_variant_id') && $this->product_variant_id) {
                $variant = \App\Models\ProductVariant::find($this->product_variant_id);
                if ($variant && $variant->product_id != $this->product_id) {
                    $validator->errors()->add('product_variant_id', 'Selected variant does not belong to the selected product.');
                }
            }

            // Validate batch number uniqueness for the same product and location (excluding current record)
            if ($this->has('batch_number') || $this->has('product_id') || $this->has('location_id') || $this->has('expiry_date')) {
                $productId = $this->product_id ?? $inventory->product_id;
                $locationId = $this->location_id ?? $inventory->location_id;
                $batchNumber = $this->batch_number ?? $inventory->batch_number;
                $expiryDate = $this->expiry_date ?? $inventory->expiry_date;

                $existingBatch = \App\Models\Inventory::where('product_id', $productId)
                    ->where('location_id', $locationId)
                    ->where('batch_number', $batchNumber)
                    ->where('expiry_date', $expiryDate)
                    ->where('id', '!=', $inventoryId)
                    ->exists();

                if ($existingBatch) {
                    $validator->errors()->add('batch_number', 'This batch number already exists for this product at this location with the same expiry date.');
                }
            }

            // Validate pricing logic
            $costPrice = $this->cost_price ?? $inventory->cost_price;
            $sellingPrice = $this->selling_price ?? $inventory->selling_price;
            $mrp = $this->mrp ?? $inventory->mrp;

            if ($costPrice && $sellingPrice && $costPrice > $sellingPrice) {
                $validator->errors()->add('selling_price', 'Selling price should be greater than or equal to cost price.');
            }

            if ($sellingPrice && $mrp && $sellingPrice > $mrp) {
                $validator->errors()->add('selling_price', 'Selling price cannot be greater than MRP.');
            }

            // Validate quantity constraints
            $quantityReceived = $this->quantity_received ?? $inventory->quantity_received;
            $quantityAvailable = $this->quantity_available ?? $inventory->quantity_available;
            $quantityReserved = $this->quantity_reserved ?? $inventory->quantity_reserved;

            if ($quantityAvailable && $quantityReceived && $quantityAvailable > $quantityReceived) {
                $validator->errors()->add('quantity_available', 'Quantity available cannot be greater than quantity received.');
            }

            if ($quantityReserved && $quantityAvailable && $quantityReserved > $quantityAvailable) {
                $validator->errors()->add('quantity_reserved', 'Quantity reserved cannot be greater than quantity available.');
            }

            // Validate that we're not reducing quantity below what's already sold/reserved
            if ($this->has('quantity_available')) {
                $soldQuantity = $inventory->quantity_received - $inventory->quantity_available - $inventory->quantity_reserved;
                $minAvailable = $quantityReceived - $soldQuantity - $quantityReserved;
                
                if ($this->quantity_available < $minAvailable) {
                    $validator->errors()->add('quantity_available', 'Cannot reduce quantity below already sold/reserved amount.');
                }
            }

            // Validate purchase order belongs to supplier
            if ($this->has('purchase_order_id') && $this->purchase_order_id && $this->has('supplier_id')) {
                $purchaseOrder = \App\Models\PurchaseOrder::find($this->purchase_order_id);
                if ($purchaseOrder && $purchaseOrder->supplier_id != $this->supplier_id) {
                    $validator->errors()->add('purchase_order_id', 'Selected purchase order does not belong to the selected supplier.');
                }
            }

            // Validate metadata structure
            if ($this->has('metadata') && $this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }

            // Validate status changes
            if ($this->has('status') && $this->status !== $inventory->status) {
                // Cannot change from expired back to active
                if ($inventory->status === 'expired' && $this->status === 'active') {
                    $validator->errors()->add('status', 'Cannot change expired inventory back to active.');
                }

                // Cannot change to active if already expired by date
                if ($this->status === 'active' && $inventory->isExpired()) {
                    $validator->errors()->add('status', 'Cannot set status to active for expired inventory.');
                }
            }
        });
    }
}
