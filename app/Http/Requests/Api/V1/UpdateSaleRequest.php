<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSaleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $saleId = $this->route('sale')->id;

        return [
            'sale_number' => [
                'sometimes',
                'nullable',
                'string',
                'max:100',
                "unique:sales,sale_number,{$saleId}"
            ],
            'customer_id' => 'sometimes|nullable|exists:customers,id',
            'prescription_id' => 'sometimes|nullable|exists:prescriptions,id',
            'sale_date' => 'sometimes|required|date|before_or_equal:now',
            'sale_type' => [
                'sometimes',
                'required',
                'string',
                'in:retail,prescription,insurance,wholesale'
            ],
            'payment_method' => [
                'sometimes',
                'required',
                'string',
                'in:cash,card,insurance,credit,mobile_payment,bank_transfer'
            ],
            'payment_status' => [
                'sometimes',
                'required',
                'string',
                'in:pending,paid,partial,refunded,cancelled'
            ],
            'subtotal' => 'sometimes|required|numeric|min:0|max:9999999.99',
            'discount_amount' => 'sometimes|nullable|numeric|min:0|max:9999999.99',
            'tax_amount' => 'sometimes|required|numeric|min:0|max:999999.99',
            'total_amount' => 'sometimes|required|numeric|min:0.01|max:9999999.99',
            'amount_paid' => 'sometimes|nullable|numeric|min:0|max:9999999.99',
            'change_amount' => 'sometimes|nullable|numeric|min:0|max:999999.99',
            'insurance_claim_number' => 'sometimes|nullable|string|max:100',
            'insurance_amount' => 'sometimes|nullable|numeric|min:0|max:9999999.99',
            'notes' => 'sometimes|nullable|string|max:1000',
            
            // Sale items (for updates, items are typically handled separately)
            'items' => 'sometimes|array|min:1',
            'items.*.id' => 'sometimes|exists:sale_items,id',
            'items.*.product_id' => 'required_with:items|exists:products,id',
            'items.*.product_variant_id' => 'sometimes|nullable|exists:product_variants,id',
            'items.*.inventory_id' => 'required_with:items|exists:inventories,id',
            'items.*.quantity_sold' => 'required_with:items|integer|min:1|max:999',
            'items.*.unit_price' => 'required_with:items|numeric|min:0|max:99999.99',
            'items.*.discount_amount' => 'sometimes|nullable|numeric|min:0|max:99999.99',
            'items.*.tax_amount' => 'required_with:items|numeric|min:0|max:9999.99',
            'items.*.total_amount' => 'required_with:items|numeric|min:0|max:999999.99',
            'items.*.prescription_item_id' => 'sometimes|nullable|exists:prescription_items,id',
            'items.*.notes' => 'sometimes|nullable|string|max:500',
            
            'metadata' => 'sometimes|nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'sale_number.unique' => 'This sale number already exists.',
            'customer_id.exists' => 'Selected customer does not exist.',
            'prescription_id.exists' => 'Selected prescription does not exist.',
            'sale_date.required' => 'Sale date is required.',
            'sale_date.date' => 'Sale date must be a valid date.',
            'sale_date.before_or_equal' => 'Sale date cannot be in the future.',
            'sale_type.required' => 'Sale type is required.',
            'sale_type.in' => 'Sale type must be one of: retail, prescription, insurance, wholesale.',
            'payment_method.required' => 'Payment method is required.',
            'payment_method.in' => 'Payment method must be one of: cash, card, insurance, credit, mobile_payment, bank_transfer.',
            'payment_status.required' => 'Payment status is required.',
            'payment_status.in' => 'Payment status must be one of: pending, paid, partial, refunded, cancelled.',
            'subtotal.required' => 'Subtotal is required.',
            'subtotal.min' => 'Subtotal must be at least 0.',
            'subtotal.max' => 'Subtotal may not be greater than 9,999,999.99.',
            'discount_amount.min' => 'Discount amount must be at least 0.',
            'discount_amount.max' => 'Discount amount may not be greater than 9,999,999.99.',
            'tax_amount.required' => 'Tax amount is required.',
            'tax_amount.min' => 'Tax amount must be at least 0.',
            'tax_amount.max' => 'Tax amount may not be greater than 999,999.99.',
            'total_amount.required' => 'Total amount is required.',
            'total_amount.min' => 'Total amount must be at least 0.01.',
            'total_amount.max' => 'Total amount may not be greater than 9,999,999.99.',
            'amount_paid.min' => 'Amount paid must be at least 0.',
            'amount_paid.max' => 'Amount paid may not be greater than 9,999,999.99.',
            'change_amount.min' => 'Change amount must be at least 0.',
            'change_amount.max' => 'Change amount may not be greater than 999,999.99.',
            'insurance_amount.min' => 'Insurance amount must be at least 0.',
            'insurance_amount.max' => 'Insurance amount may not be greater than 9,999,999.99.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            
            'items.min' => 'At least one item is required.',
            'items.*.id.exists' => 'Selected sale item does not exist.',
            'items.*.product_id.required_with' => 'Product is required for each item.',
            'items.*.product_id.exists' => 'Selected product does not exist.',
            'items.*.product_variant_id.exists' => 'Selected product variant does not exist.',
            'items.*.inventory_id.required_with' => 'Inventory is required for each item.',
            'items.*.inventory_id.exists' => 'Selected inventory does not exist.',
            'items.*.quantity_sold.required_with' => 'Quantity is required for each item.',
            'items.*.quantity_sold.min' => 'Quantity must be at least 1.',
            'items.*.quantity_sold.max' => 'Quantity may not be greater than 999.',
            'items.*.unit_price.required_with' => 'Unit price is required for each item.',
            'items.*.unit_price.min' => 'Unit price must be at least 0.',
            'items.*.unit_price.max' => 'Unit price may not be greater than 99,999.99.',
            'items.*.discount_amount.min' => 'Discount amount must be at least 0.',
            'items.*.discount_amount.max' => 'Discount amount may not be greater than 99,999.99.',
            'items.*.tax_amount.required_with' => 'Tax amount is required for each item.',
            'items.*.tax_amount.min' => 'Tax amount must be at least 0.',
            'items.*.tax_amount.max' => 'Tax amount may not be greater than 9,999.99.',
            'items.*.total_amount.required_with' => 'Total amount is required for each item.',
            'items.*.total_amount.min' => 'Total amount must be at least 0.',
            'items.*.total_amount.max' => 'Total amount may not be greater than 999,999.99.',
            'items.*.prescription_item_id.exists' => 'Selected prescription item does not exist.',
            'items.*.notes.max' => 'Item notes may not be greater than 500 characters.',
            
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'sale_number' => 'sale number',
            'customer_id' => 'customer',
            'prescription_id' => 'prescription',
            'sale_date' => 'sale date',
            'sale_type' => 'sale type',
            'payment_method' => 'payment method',
            'payment_status' => 'payment status',
            'discount_amount' => 'discount amount',
            'tax_amount' => 'tax amount',
            'total_amount' => 'total amount',
            'amount_paid' => 'amount paid',
            'change_amount' => 'change amount',
            'insurance_claim_number' => 'insurance claim number',
            'insurance_amount' => 'insurance amount',
            'items.*.product_id' => 'product',
            'items.*.product_variant_id' => 'product variant',
            'items.*.inventory_id' => 'inventory',
            'items.*.quantity_sold' => 'quantity',
            'items.*.unit_price' => 'unit price',
            'items.*.discount_amount' => 'discount amount',
            'items.*.tax_amount' => 'tax amount',
            'items.*.total_amount' => 'total amount',
            'items.*.prescription_item_id' => 'prescription item',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        $nullableFields = [
            'sale_number', 'customer_id', 'prescription_id', 'discount_amount',
            'amount_paid', 'change_amount', 'insurance_claim_number', 
            'insurance_amount', 'notes', 'metadata'
        ];

        foreach ($nullableFields as $field) {
            if ($this->has($field) && $this->$field === '') {
                $this->merge([$field => null]);
            }
        }

        // Calculate totals if items are provided
        if ($this->has('items') && $this->items) {
            $calculatedSubtotal = 0;
            $calculatedTaxAmount = 0;
            $calculatedDiscountAmount = $this->discount_amount ?? 0;

            $items = $this->items;
            foreach ($items as $index => $item) {
                // Calculate item total if not provided
                if (!isset($item['total_amount']) && isset($item['quantity_sold']) && isset($item['unit_price'])) {
                    $itemSubtotal = $item['quantity_sold'] * $item['unit_price'];
                    $itemDiscount = $item['discount_amount'] ?? 0;
                    $itemTax = $item['tax_amount'] ?? 0;
                    $items[$index]['total_amount'] = $itemSubtotal - $itemDiscount + $itemTax;
                }

                $calculatedSubtotal += ($item['quantity_sold'] ?? 0) * ($item['unit_price'] ?? 0);
                $calculatedTaxAmount += $item['tax_amount'] ?? 0;
            }

            $this->merge(['items' => $items]);

            // Set calculated totals if not provided
            if (!$this->has('subtotal')) {
                $this->merge(['subtotal' => $calculatedSubtotal]);
            }

            if (!$this->has('tax_amount')) {
                $this->merge(['tax_amount' => $calculatedTaxAmount]);
            }

            if (!$this->has('total_amount')) {
                $calculatedTotal = $calculatedSubtotal - $calculatedDiscountAmount + $calculatedTaxAmount;
                $this->merge(['total_amount' => $calculatedTotal]);
            }
        }

        // Calculate change amount
        if ($this->has('amount_paid') && $this->has('total_amount') && !$this->has('change_amount')) {
            $change = max(0, ($this->amount_paid ?? 0) - ($this->total_amount ?? 0));
            $this->merge(['change_amount' => $change]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $sale = $this->route('sale');

            // Prevent modification of completed/cancelled sales
            if (in_array($sale->payment_status, ['refunded', 'cancelled'])) {
                $validator->errors()->add('payment_status', 'Cannot modify refunded or cancelled sales.');
                return;
            }

            // Validate customer is active
            if ($this->has('customer_id') && $this->customer_id) {
                $customer = \App\Models\Customer::find($this->customer_id);
                if ($customer && !$customer->is_active) {
                    $validator->errors()->add('customer_id', 'Cannot update sale with inactive customer.');
                }
            }

            // Validate prescription requirements
            $saleType = $this->sale_type ?? $sale->sale_type;
            if ($saleType === 'prescription') {
                $prescriptionId = $this->prescription_id ?? $sale->prescription_id;
                if (!$prescriptionId) {
                    $validator->errors()->add('prescription_id', 'Prescription is required for prescription sales.');
                }

                if ($prescriptionId) {
                    $prescription = \App\Models\Prescription::find($prescriptionId);
                    if ($prescription) {
                        if ($prescription->status !== 'active') {
                            $validator->errors()->add('prescription_id', 'Prescription must be active for sales.');
                        }

                        $customerId = $this->customer_id ?? $sale->customer_id;
                        if ($prescription->customer_id !== $customerId) {
                            $validator->errors()->add('customer_id', 'Customer must match prescription customer.');
                        }

                        // Check prescription expiry
                        if ($prescription->expiry_date && $prescription->expiry_date < now()) {
                            $validator->errors()->add('prescription_id', 'Prescription has expired.');
                        }
                    }
                }
            }

            // Validate insurance requirements
            $paymentMethod = $this->payment_method ?? $sale->payment_method;
            if ($saleType === 'insurance' || $paymentMethod === 'insurance') {
                $insuranceClaimNumber = $this->insurance_claim_number ?? $sale->insurance_claim_number;
                $insuranceAmount = $this->insurance_amount ?? $sale->insurance_amount;

                if (!$insuranceClaimNumber) {
                    $validator->errors()->add('insurance_claim_number', 'Insurance claim number is required for insurance sales.');
                }

                if (!$insuranceAmount) {
                    $validator->errors()->add('insurance_amount', 'Insurance amount is required for insurance sales.');
                }
            }

            // Validate payment logic
            $paymentStatus = $this->payment_status ?? $sale->payment_status;
            $amountPaid = $this->amount_paid ?? $sale->amount_paid;
            $totalAmount = $this->total_amount ?? $sale->total_amount;

            if ($paymentStatus === 'paid') {
                if (!$amountPaid) {
                    $validator->errors()->add('amount_paid', 'Amount paid is required when payment status is paid.');
                }

                if ($amountPaid < $totalAmount) {
                    $validator->errors()->add('amount_paid', 'Amount paid must be at least the total amount for paid status.');
                }
            }

            if ($paymentStatus === 'partial') {
                if (!$amountPaid) {
                    $validator->errors()->add('amount_paid', 'Amount paid is required when payment status is partial.');
                }

                if ($amountPaid >= $totalAmount) {
                    $validator->errors()->add('payment_status', 'Payment status should be "paid" when amount paid equals or exceeds total.');
                }
            }

            // Validate change amount
            $changeAmount = $this->change_amount ?? $sale->change_amount;
            if ($changeAmount && $amountPaid && $totalAmount) {
                $expectedChange = max(0, $amountPaid - $totalAmount);
                $tolerance = 0.01; // 1 cent tolerance

                if (abs($changeAmount - $expectedChange) > $tolerance) {
                    $validator->errors()->add('change_amount', 'Change amount does not match amount paid minus total amount.');
                }
            }

            // Validate total calculations if items are provided
            if ($this->has('items') && $this->items) {
                $calculatedSubtotal = 0;
                $calculatedTaxAmount = 0;

                foreach ($this->items as $item) {
                    $calculatedSubtotal += ($item['quantity_sold'] ?? 0) * ($item['unit_price'] ?? 0);
                    $calculatedTaxAmount += $item['tax_amount'] ?? 0;
                }

                $tolerance = 0.01; // 1 cent tolerance
                $subtotal = $this->subtotal ?? $sale->subtotal;
                $taxAmount = $this->tax_amount ?? $sale->tax_amount;
                $discountAmount = $this->discount_amount ?? $sale->discount_amount ?? 0;

                if (abs($subtotal - $calculatedSubtotal) > $tolerance) {
                    $validator->errors()->add('subtotal', 'Subtotal does not match sum of item subtotals.');
                }

                if (abs($taxAmount - $calculatedTaxAmount) > $tolerance) {
                    $validator->errors()->add('tax_amount', 'Tax amount does not match sum of item tax amounts.');
                }

                $expectedTotal = $calculatedSubtotal - $discountAmount + $calculatedTaxAmount;
                if (abs($totalAmount - $expectedTotal) > $tolerance) {
                    $validator->errors()->add('total_amount', 'Total amount does not match subtotal minus discount plus tax.');
                }
            }

            // Validate metadata structure
            if ($this->has('metadata') && $this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }
        });
    }
}
