<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StorePrescriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'pharmacist']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'prescription_number' => [
                'nullable',
                'string',
                'max:100',
                'unique:prescriptions,prescription_number'
            ],
            'customer_id' => 'required|exists:customers,id',
            'doctor_name' => 'required|string|max:255',
            'doctor_license' => 'required|string|max:100',
            'doctor_phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/'
            ],
            'prescription_date' => 'required|date|before_or_equal:today|after:' . now()->subYears(2)->format('Y-m-d'),
            'expiry_date' => 'nullable|date|after:prescription_date|before:' . now()->addYears(2)->format('Y-m-d'),
            'status' => [
                'required',
                'string',
                'in:active,filled,expired,cancelled'
            ],
            'priority' => [
                'required',
                'string',
                'in:normal,urgent,emergency'
            ],
            'patient_name' => 'required|string|max:255',
            'patient_age' => 'nullable|integer|min:0|max:150',
            'patient_weight' => 'nullable|numeric|min:0.5|max:500',
            'patient_allergies' => 'nullable|string|max:1000',
            'diagnosis' => 'nullable|string|max:1000',
            'special_instructions' => 'nullable|string|max:1000',
            'refills_allowed' => 'nullable|integer|min:0|max:12',
            'refills_used' => 'nullable|integer|min:0|max:12',
            'is_controlled_substance' => 'boolean',
            'controlled_substance_schedule' => [
                'nullable',
                'string',
                'in:I,II,III,IV,V',
                'required_if:is_controlled_substance,true'
            ],
            'notes' => 'nullable|string|max:1000',
            
            // Prescription items
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.product_variant_id' => 'nullable|exists:product_variants,id',
            'items.*.quantity_prescribed' => 'required|integer|min:1|max:999',
            'items.*.dosage' => 'required|string|max:255',
            'items.*.frequency' => 'required|string|max:255',
            'items.*.duration' => 'required|string|max:255',
            'items.*.instructions' => 'nullable|string|max:500',
            'items.*.substitution_allowed' => 'boolean',
            
            'metadata' => 'nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'prescription_number.unique' => 'This prescription number already exists.',
            'customer_id.required' => 'Customer is required.',
            'customer_id.exists' => 'Selected customer does not exist.',
            'doctor_name.required' => 'Doctor name is required.',
            'doctor_name.max' => 'Doctor name may not be greater than 255 characters.',
            'doctor_license.required' => 'Doctor license is required.',
            'doctor_license.max' => 'Doctor license may not be greater than 100 characters.',
            'doctor_phone.regex' => 'Please provide a valid doctor phone number.',
            'prescription_date.required' => 'Prescription date is required.',
            'prescription_date.date' => 'Prescription date must be a valid date.',
            'prescription_date.before_or_equal' => 'Prescription date cannot be in the future.',
            'prescription_date.after' => 'Prescription date cannot be more than 2 years old.',
            'expiry_date.date' => 'Expiry date must be a valid date.',
            'expiry_date.after' => 'Expiry date must be after prescription date.',
            'expiry_date.before' => 'Expiry date cannot be more than 2 years in the future.',
            'status.required' => 'Status is required.',
            'status.in' => 'Status must be one of: active, filled, expired, cancelled.',
            'priority.required' => 'Priority is required.',
            'priority.in' => 'Priority must be one of: normal, urgent, emergency.',
            'patient_name.required' => 'Patient name is required.',
            'patient_name.max' => 'Patient name may not be greater than 255 characters.',
            'patient_age.min' => 'Patient age must be at least 0.',
            'patient_age.max' => 'Patient age may not be greater than 150.',
            'patient_weight.min' => 'Patient weight must be at least 0.5 kg.',
            'patient_weight.max' => 'Patient weight may not be greater than 500 kg.',
            'patient_allergies.max' => 'Patient allergies may not be greater than 1000 characters.',
            'diagnosis.max' => 'Diagnosis may not be greater than 1000 characters.',
            'special_instructions.max' => 'Special instructions may not be greater than 1000 characters.',
            'refills_allowed.min' => 'Refills allowed must be at least 0.',
            'refills_allowed.max' => 'Refills allowed may not be greater than 12.',
            'refills_used.min' => 'Refills used must be at least 0.',
            'refills_used.max' => 'Refills used may not be greater than 12.',
            'controlled_substance_schedule.in' => 'Controlled substance schedule must be one of: I, II, III, IV, V.',
            'controlled_substance_schedule.required_if' => 'Controlled substance schedule is required for controlled substances.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            
            'items.required' => 'At least one medication is required.',
            'items.min' => 'At least one medication is required.',
            'items.*.product_id.required' => 'Product is required for each medication.',
            'items.*.product_id.exists' => 'Selected product does not exist.',
            'items.*.product_variant_id.exists' => 'Selected product variant does not exist.',
            'items.*.quantity_prescribed.required' => 'Quantity is required for each medication.',
            'items.*.quantity_prescribed.min' => 'Quantity must be at least 1.',
            'items.*.quantity_prescribed.max' => 'Quantity may not be greater than 999.',
            'items.*.dosage.required' => 'Dosage is required for each medication.',
            'items.*.dosage.max' => 'Dosage may not be greater than 255 characters.',
            'items.*.frequency.required' => 'Frequency is required for each medication.',
            'items.*.frequency.max' => 'Frequency may not be greater than 255 characters.',
            'items.*.duration.required' => 'Duration is required for each medication.',
            'items.*.duration.max' => 'Duration may not be greater than 255 characters.',
            'items.*.instructions.max' => 'Instructions may not be greater than 500 characters.',
            
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'prescription_number' => 'prescription number',
            'customer_id' => 'customer',
            'doctor_name' => 'doctor name',
            'doctor_license' => 'doctor license',
            'doctor_phone' => 'doctor phone',
            'prescription_date' => 'prescription date',
            'expiry_date' => 'expiry date',
            'patient_name' => 'patient name',
            'patient_age' => 'patient age',
            'patient_weight' => 'patient weight',
            'patient_allergies' => 'patient allergies',
            'special_instructions' => 'special instructions',
            'refills_allowed' => 'refills allowed',
            'refills_used' => 'refills used',
            'is_controlled_substance' => 'controlled substance',
            'controlled_substance_schedule' => 'controlled substance schedule',
            'items.*.product_id' => 'product',
            'items.*.product_variant_id' => 'product variant',
            'items.*.quantity_prescribed' => 'quantity',
            'items.*.substitution_allowed' => 'substitution allowed',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        $this->merge([
            'prescription_number' => $this->prescription_number === '' ? null : $this->prescription_number,
            'doctor_phone' => $this->doctor_phone === '' ? null : $this->doctor_phone,
            'expiry_date' => $this->expiry_date === '' ? null : $this->expiry_date,
            'patient_age' => $this->patient_age === '' ? null : $this->patient_age,
            'patient_weight' => $this->patient_weight === '' ? null : $this->patient_weight,
            'patient_allergies' => $this->patient_allergies === '' ? null : $this->patient_allergies,
            'diagnosis' => $this->diagnosis === '' ? null : $this->diagnosis,
            'special_instructions' => $this->special_instructions === '' ? null : $this->special_instructions,
            'refills_allowed' => $this->refills_allowed === '' ? null : $this->refills_allowed,
            'refills_used' => $this->refills_used === '' ? null : $this->refills_used,
            'controlled_substance_schedule' => $this->controlled_substance_schedule === '' ? null : $this->controlled_substance_schedule,
            'notes' => $this->notes === '' ? null : $this->notes,
            'metadata' => $this->metadata === '' ? null : $this->metadata,
        ]);

        // Auto-generate prescription number if not provided
        if (!$this->prescription_number) {
            $this->merge(['prescription_number' => 'RX-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6))]);
        }

        // Set default values
        if (!$this->has('status')) {
            $this->merge(['status' => 'active']);
        }

        if (!$this->has('priority')) {
            $this->merge(['priority' => 'normal']);
        }

        if (!$this->has('is_controlled_substance')) {
            $this->merge(['is_controlled_substance' => false]);
        }

        if (!$this->has('refills_used')) {
            $this->merge(['refills_used' => 0]);
        }

        // Set created_by to current user
        $this->merge(['created_by' => $this->user()->id]);

        // Normalize phone number
        if ($this->doctor_phone) {
            $this->merge(['doctor_phone' => preg_replace('/[^\+0-9]/', '', $this->doctor_phone)]);
        }

        // Set default expiry date if not provided (1 year from prescription date)
        if (!$this->expiry_date && $this->prescription_date) {
            $prescriptionDate = \Carbon\Carbon::parse($this->prescription_date);
            $this->merge(['expiry_date' => $prescriptionDate->addYear()->format('Y-m-d')]);
        }

        // Set substitution_allowed default for items
        if ($this->items) {
            $items = $this->items;
            foreach ($items as $index => $item) {
                if (!isset($item['substitution_allowed'])) {
                    $items[$index]['substitution_allowed'] = true;
                }
            }
            $this->merge(['items' => $items]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate customer is active
            if ($this->customer_id) {
                $customer = \App\Models\Customer::find($this->customer_id);
                if ($customer && !$customer->is_active) {
                    $validator->errors()->add('customer_id', 'Cannot create prescription for inactive customer.');
                }
            }

            // Validate doctor license format (basic validation)
            if ($this->doctor_license && !preg_match('/^[A-Z0-9\-]+$/i', $this->doctor_license)) {
                $validator->errors()->add('doctor_license', 'Doctor license format is invalid.');
            }

            // Validate refills logic
            if ($this->refills_allowed && $this->refills_used && $this->refills_used > $this->refills_allowed) {
                $validator->errors()->add('refills_used', 'Refills used cannot exceed refills allowed.');
            }

            // Validate controlled substance requirements
            if ($this->is_controlled_substance) {
                if (!$this->controlled_substance_schedule) {
                    $validator->errors()->add('controlled_substance_schedule', 'Controlled substance schedule is required.');
                }

                // Controlled substances have stricter refill limits
                if ($this->refills_allowed > 5) {
                    $validator->errors()->add('refills_allowed', 'Controlled substances cannot have more than 5 refills.');
                }

                // Schedule II substances cannot have refills
                if ($this->controlled_substance_schedule === 'II' && $this->refills_allowed > 0) {
                    $validator->errors()->add('refills_allowed', 'Schedule II controlled substances cannot have refills.');
                }
            }

            // Validate prescription items
            if ($this->items) {
                $productIds = [];

                foreach ($this->items as $index => $item) {
                    // Check for duplicate products
                    $productKey = $item['product_id'] . '-' . ($item['product_variant_id'] ?? '');
                    if (in_array($productKey, $productIds)) {
                        $validator->errors()->add("items.{$index}.product_id", 'Duplicate product in prescription items.');
                    }
                    $productIds[] = $productKey;

                    // Validate product variant belongs to product
                    if (isset($item['product_variant_id']) && $item['product_variant_id']) {
                        $variant = \App\Models\ProductVariant::find($item['product_variant_id']);
                        if ($variant && $variant->product_id != $item['product_id']) {
                            $validator->errors()->add("items.{$index}.product_variant_id", 'Selected variant does not belong to the selected product.');
                        }
                    }

                    // Validate product is active and requires prescription
                    if (isset($item['product_id'])) {
                        $product = \App\Models\Product::find($item['product_id']);
                        if ($product) {
                            if (!$product->is_active) {
                                $validator->errors()->add("items.{$index}.product_id", 'Cannot prescribe inactive product.');
                            }

                            if (!$product->requires_prescription) {
                                $validator->errors()->add("items.{$index}.product_id", 'Selected product does not require a prescription.');
                            }

                            // Check if product is controlled substance
                            if ($product->is_controlled_substance && !$this->is_controlled_substance) {
                                $validator->errors()->add("items.{$index}.product_id", 'Controlled substance products require prescription to be marked as controlled substance.');
                            }
                        }
                    }

                    // Validate dosage format
                    if (isset($item['dosage'])) {
                        if (!preg_match('/^\d+(\.\d+)?\s*(mg|g|ml|tablet|capsule|drop|spray|patch|unit)/i', $item['dosage'])) {
                            $validator->errors()->add("items.{$index}.dosage", 'Dosage must include numeric value and unit (e.g., "500mg", "2 tablets").');
                        }
                    }

                    // Validate frequency format
                    if (isset($item['frequency'])) {
                        $validFrequencies = ['once daily', 'twice daily', 'three times daily', 'four times daily', 'every 4 hours', 'every 6 hours', 'every 8 hours', 'every 12 hours', 'as needed', 'before meals', 'after meals', 'at bedtime'];
                        $frequency = strtolower($item['frequency']);
                        
                        $isValidFrequency = false;
                        foreach ($validFrequencies as $validFreq) {
                            if (strpos($frequency, $validFreq) !== false) {
                                $isValidFrequency = true;
                                break;
                            }
                        }

                        if (!$isValidFrequency && !preg_match('/\d+\s*(time|times)\s*(per|a)\s*(day|week)/i', $frequency)) {
                            $validator->errors()->add("items.{$index}.frequency", 'Frequency format is not recognized. Use standard medical frequency terms.');
                        }
                    }

                    // Validate duration format
                    if (isset($item['duration'])) {
                        if (!preg_match('/^\d+\s*(day|days|week|weeks|month|months)$/i', $item['duration'])) {
                            $validator->errors()->add("items.{$index}.duration", 'Duration must be in format like "7 days", "2 weeks", "1 month".');
                        }
                    }
                }
            }

            // Validate patient information consistency
            if ($this->customer_id && $this->patient_name) {
                $customer = \App\Models\Customer::find($this->customer_id);
                if ($customer) {
                    $customerFullName = trim($customer->first_name . ' ' . $customer->last_name);
                    if (strtolower($customerFullName) !== strtolower($this->patient_name)) {
                        $validator->errors()->add('patient_name', 'Patient name does not match customer name. Please verify.');
                    }
                }
            }

            // Validate patient age consistency
            if ($this->customer_id && $this->patient_age) {
                $customer = \App\Models\Customer::find($this->customer_id);
                if ($customer && $customer->date_of_birth) {
                    $calculatedAge = \Carbon\Carbon::parse($customer->date_of_birth)->age;
                    if (abs($calculatedAge - $this->patient_age) > 1) {
                        $validator->errors()->add('patient_age', 'Patient age does not match customer\'s calculated age. Please verify.');
                    }
                }
            }

            // Validate metadata structure
            if ($this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }

            // Validate emergency prescriptions
            if ($this->priority === 'emergency') {
                if (!$this->special_instructions) {
                    $validator->errors()->add('special_instructions', 'Emergency prescriptions require special instructions.');
                }
            }
        });
    }
}
