<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdatePrescriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'pharmacist']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $prescriptionId = $this->route('prescription')->id;

        return [
            'prescription_number' => [
                'sometimes',
                'nullable',
                'string',
                'max:100',
                Rule::unique('prescriptions')->ignore($prescriptionId)
            ],
            'customer_id' => 'sometimes|required|exists:customers,id',
            'doctor_name' => 'sometimes|required|string|max:255',
            'doctor_license' => 'sometimes|required|string|max:100',
            'doctor_phone' => [
                'sometimes',
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/'
            ],
            'prescription_date' => 'sometimes|required|date|before_or_equal:today|after:' . now()->subYears(2)->format('Y-m-d'),
            'expiry_date' => 'sometimes|nullable|date|after:prescription_date|before:' . now()->addYears(2)->format('Y-m-d'),
            'status' => [
                'sometimes',
                'required',
                'string',
                'in:active,filled,expired,cancelled'
            ],
            'priority' => [
                'sometimes',
                'required',
                'string',
                'in:normal,urgent,emergency'
            ],
            'patient_name' => 'sometimes|required|string|max:255',
            'patient_age' => 'sometimes|nullable|integer|min:0|max:150',
            'patient_weight' => 'sometimes|nullable|numeric|min:0.5|max:500',
            'patient_allergies' => 'sometimes|nullable|string|max:1000',
            'diagnosis' => 'sometimes|nullable|string|max:1000',
            'special_instructions' => 'sometimes|nullable|string|max:1000',
            'refills_allowed' => 'sometimes|nullable|integer|min:0|max:12',
            'refills_used' => 'sometimes|nullable|integer|min:0|max:12',
            'is_controlled_substance' => 'sometimes|boolean',
            'controlled_substance_schedule' => [
                'sometimes',
                'nullable',
                'string',
                'in:I,II,III,IV,V',
                'required_if:is_controlled_substance,true'
            ],
            'notes' => 'sometimes|nullable|string|max:1000',
            
            // Prescription items (for complete replacement)
            'items' => 'sometimes|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.product_variant_id' => 'nullable|exists:product_variants,id',
            'items.*.quantity_prescribed' => 'required|integer|min:1|max:999',
            'items.*.dosage' => 'required|string|max:255',
            'items.*.frequency' => 'required|string|max:255',
            'items.*.duration' => 'required|string|max:255',
            'items.*.instructions' => 'nullable|string|max:500',
            'items.*.substitution_allowed' => 'boolean',
            
            'metadata' => 'sometimes|nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'prescription_number.unique' => 'This prescription number already exists.',
            'customer_id.required' => 'Customer is required.',
            'customer_id.exists' => 'Selected customer does not exist.',
            'doctor_name.required' => 'Doctor name is required.',
            'doctor_name.max' => 'Doctor name may not be greater than 255 characters.',
            'doctor_license.required' => 'Doctor license is required.',
            'doctor_license.max' => 'Doctor license may not be greater than 100 characters.',
            'doctor_phone.regex' => 'Please provide a valid doctor phone number.',
            'prescription_date.required' => 'Prescription date is required.',
            'prescription_date.date' => 'Prescription date must be a valid date.',
            'prescription_date.before_or_equal' => 'Prescription date cannot be in the future.',
            'prescription_date.after' => 'Prescription date cannot be more than 2 years old.',
            'expiry_date.date' => 'Expiry date must be a valid date.',
            'expiry_date.after' => 'Expiry date must be after prescription date.',
            'expiry_date.before' => 'Expiry date cannot be more than 2 years in the future.',
            'status.required' => 'Status is required.',
            'status.in' => 'Status must be one of: active, filled, expired, cancelled.',
            'priority.required' => 'Priority is required.',
            'priority.in' => 'Priority must be one of: normal, urgent, emergency.',
            'patient_name.required' => 'Patient name is required.',
            'patient_name.max' => 'Patient name may not be greater than 255 characters.',
            'patient_age.min' => 'Patient age must be at least 0.',
            'patient_age.max' => 'Patient age may not be greater than 150.',
            'patient_weight.min' => 'Patient weight must be at least 0.5 kg.',
            'patient_weight.max' => 'Patient weight may not be greater than 500 kg.',
            'patient_allergies.max' => 'Patient allergies may not be greater than 1000 characters.',
            'diagnosis.max' => 'Diagnosis may not be greater than 1000 characters.',
            'special_instructions.max' => 'Special instructions may not be greater than 1000 characters.',
            'refills_allowed.min' => 'Refills allowed must be at least 0.',
            'refills_allowed.max' => 'Refills allowed may not be greater than 12.',
            'refills_used.min' => 'Refills used must be at least 0.',
            'refills_used.max' => 'Refills used may not be greater than 12.',
            'controlled_substance_schedule.in' => 'Controlled substance schedule must be one of: I, II, III, IV, V.',
            'controlled_substance_schedule.required_if' => 'Controlled substance schedule is required for controlled substances.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            
            'items.min' => 'At least one medication is required.',
            'items.*.product_id.required' => 'Product is required for each medication.',
            'items.*.product_id.exists' => 'Selected product does not exist.',
            'items.*.product_variant_id.exists' => 'Selected product variant does not exist.',
            'items.*.quantity_prescribed.required' => 'Quantity is required for each medication.',
            'items.*.quantity_prescribed.min' => 'Quantity must be at least 1.',
            'items.*.quantity_prescribed.max' => 'Quantity may not be greater than 999.',
            'items.*.dosage.required' => 'Dosage is required for each medication.',
            'items.*.dosage.max' => 'Dosage may not be greater than 255 characters.',
            'items.*.frequency.required' => 'Frequency is required for each medication.',
            'items.*.frequency.max' => 'Frequency may not be greater than 255 characters.',
            'items.*.duration.required' => 'Duration is required for each medication.',
            'items.*.duration.max' => 'Duration may not be greater than 255 characters.',
            'items.*.instructions.max' => 'Instructions may not be greater than 500 characters.',
            
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'prescription_number' => 'prescription number',
            'customer_id' => 'customer',
            'doctor_name' => 'doctor name',
            'doctor_license' => 'doctor license',
            'doctor_phone' => 'doctor phone',
            'prescription_date' => 'prescription date',
            'expiry_date' => 'expiry date',
            'patient_name' => 'patient name',
            'patient_age' => 'patient age',
            'patient_weight' => 'patient weight',
            'patient_allergies' => 'patient allergies',
            'special_instructions' => 'special instructions',
            'refills_allowed' => 'refills allowed',
            'refills_used' => 'refills used',
            'is_controlled_substance' => 'controlled substance',
            'controlled_substance_schedule' => 'controlled substance schedule',
            'items.*.product_id' => 'product',
            'items.*.product_variant_id' => 'product variant',
            'items.*.quantity_prescribed' => 'quantity',
            'items.*.substitution_allowed' => 'substitution allowed',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        $nullableFields = [
            'prescription_number', 'doctor_phone', 'expiry_date', 'patient_age',
            'patient_weight', 'patient_allergies', 'diagnosis', 'special_instructions',
            'refills_allowed', 'refills_used', 'controlled_substance_schedule',
            'notes', 'metadata'
        ];

        foreach ($nullableFields as $field) {
            if ($this->has($field) && $this->$field === '') {
                $this->merge([$field => null]);
            }
        }

        // Normalize phone number
        if ($this->has('doctor_phone') && $this->doctor_phone) {
            $this->merge(['doctor_phone' => preg_replace('/[^\+0-9]/', '', $this->doctor_phone)]);
        }

        // Set substitution_allowed default for items
        if ($this->has('items') && $this->items) {
            $items = $this->items;
            foreach ($items as $index => $item) {
                if (!isset($item['substitution_allowed'])) {
                    $items[$index]['substitution_allowed'] = true;
                }
            }
            $this->merge(['items' => $items]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $prescription = $this->route('prescription');
            
            // Validate customer is active
            if ($this->has('customer_id')) {
                $customer = \App\Models\Customer::find($this->customer_id);
                if ($customer && !$customer->is_active) {
                    $validator->errors()->add('customer_id', 'Cannot change to inactive customer.');
                }
            }

            // Validate status transitions
            if ($this->has('status')) {
                $currentStatus = $prescription->status;
                $newStatus = $this->status;
                
                $validTransitions = [
                    'active' => ['filled', 'expired', 'cancelled'],
                    'filled' => ['expired'],
                    'expired' => [],
                    'cancelled' => []
                ];

                if (!in_array($newStatus, $validTransitions[$currentStatus] ?? [])) {
                    $validator->errors()->add('status', "Cannot change status from {$currentStatus} to {$newStatus}.");
                }

                // Cannot modify filled, expired, or cancelled prescriptions (except notes)
                if (in_array($currentStatus, ['filled', 'expired', 'cancelled']) && $currentStatus !== $newStatus) {
                    $validator->errors()->add('status', "Cannot modify {$currentStatus} prescription.");
                }
            }

            // Validate modifications based on current status
            $currentStatus = $prescription->status;
            
            if (in_array($currentStatus, ['filled', 'expired', 'cancelled'])) {
                $modifiableFields = ['notes', 'metadata', 'refills_used'];
                $requestFields = array_keys($this->all());
                $nonModifiableFields = array_diff($requestFields, $modifiableFields);
                
                if (!empty($nonModifiableFields)) {
                    $validator->errors()->add('status', "Cannot modify {$currentStatus} prescription except for notes, metadata, and refills used.");
                }
            }

            // Validate refills logic
            $refillsAllowed = $this->refills_allowed ?? $prescription->refills_allowed;
            $refillsUsed = $this->refills_used ?? $prescription->refills_used;
            
            if ($refillsAllowed && $refillsUsed && $refillsUsed > $refillsAllowed) {
                $validator->errors()->add('refills_used', 'Refills used cannot exceed refills allowed.');
            }

            // Validate controlled substance requirements
            $isControlledSubstance = $this->is_controlled_substance ?? $prescription->is_controlled_substance;
            $controlledSchedule = $this->controlled_substance_schedule ?? $prescription->controlled_substance_schedule;
            
            if ($isControlledSubstance) {
                if (!$controlledSchedule) {
                    $validator->errors()->add('controlled_substance_schedule', 'Controlled substance schedule is required.');
                }

                // Controlled substances have stricter refill limits
                if ($refillsAllowed > 5) {
                    $validator->errors()->add('refills_allowed', 'Controlled substances cannot have more than 5 refills.');
                }

                // Schedule II substances cannot have refills
                if ($controlledSchedule === 'II' && $refillsAllowed > 0) {
                    $validator->errors()->add('refills_allowed', 'Schedule II controlled substances cannot have refills.');
                }
            }

            // Validate prescription items modifications
            if ($this->has('items')) {
                // Cannot modify items after filling
                if (in_array($currentStatus, ['filled', 'expired'])) {
                    $validator->errors()->add('items', 'Cannot modify items after prescription has been filled.');
                }

                $productIds = [];

                foreach ($this->items as $index => $item) {
                    // Check for duplicate products
                    $productKey = $item['product_id'] . '-' . ($item['product_variant_id'] ?? '');
                    if (in_array($productKey, $productIds)) {
                        $validator->errors()->add("items.{$index}.product_id", 'Duplicate product in prescription items.');
                    }
                    $productIds[] = $productKey;

                    // Validate product variant belongs to product
                    if (isset($item['product_variant_id']) && $item['product_variant_id']) {
                        $variant = \App\Models\ProductVariant::find($item['product_variant_id']);
                        if ($variant && $variant->product_id != $item['product_id']) {
                            $validator->errors()->add("items.{$index}.product_variant_id", 'Selected variant does not belong to the selected product.');
                        }
                    }

                    // Validate product is active and requires prescription
                    if (isset($item['product_id'])) {
                        $product = \App\Models\Product::find($item['product_id']);
                        if ($product) {
                            if (!$product->is_active) {
                                $validator->errors()->add("items.{$index}.product_id", 'Cannot prescribe inactive product.');
                            }

                            if (!$product->requires_prescription) {
                                $validator->errors()->add("items.{$index}.product_id", 'Selected product does not require a prescription.');
                            }

                            // Check if product is controlled substance
                            if ($product->is_controlled_substance && !$isControlledSubstance) {
                                $validator->errors()->add("items.{$index}.product_id", 'Controlled substance products require prescription to be marked as controlled substance.');
                            }
                        }
                    }

                    // Validate dosage format
                    if (isset($item['dosage'])) {
                        if (!preg_match('/^\d+(\.\d+)?\s*(mg|g|ml|tablet|capsule|drop|spray|patch|unit)/i', $item['dosage'])) {
                            $validator->errors()->add("items.{$index}.dosage", 'Dosage must include numeric value and unit (e.g., "500mg", "2 tablets").');
                        }
                    }

                    // Validate frequency format
                    if (isset($item['frequency'])) {
                        $validFrequencies = ['once daily', 'twice daily', 'three times daily', 'four times daily', 'every 4 hours', 'every 6 hours', 'every 8 hours', 'every 12 hours', 'as needed', 'before meals', 'after meals', 'at bedtime'];
                        $frequency = strtolower($item['frequency']);
                        
                        $isValidFrequency = false;
                        foreach ($validFrequencies as $validFreq) {
                            if (strpos($frequency, $validFreq) !== false) {
                                $isValidFrequency = true;
                                break;
                            }
                        }

                        if (!$isValidFrequency && !preg_match('/\d+\s*(time|times)\s*(per|a)\s*(day|week)/i', $frequency)) {
                            $validator->errors()->add("items.{$index}.frequency", 'Frequency format is not recognized. Use standard medical frequency terms.');
                        }
                    }

                    // Validate duration format
                    if (isset($item['duration'])) {
                        if (!preg_match('/^\d+\s*(day|days|week|weeks|month|months)$/i', $item['duration'])) {
                            $validator->errors()->add("items.{$index}.duration", 'Duration must be in format like "7 days", "2 weeks", "1 month".');
                        }
                    }
                }
            }

            // Validate metadata structure
            if ($this->has('metadata') && $this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }

            // Validate refills used increment
            if ($this->has('refills_used') && $this->refills_used > $prescription->refills_used) {
                $increment = $this->refills_used - $prescription->refills_used;
                if ($increment > 1) {
                    $validator->errors()->add('refills_used', 'Can only increment refills used by 1 at a time.');
                }
            }
        });
    }
}
