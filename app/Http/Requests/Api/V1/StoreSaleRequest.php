<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

class StoreSaleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'customer_id' => 'nullable|exists:customers,id',
            'location_id' => 'required|exists:locations,id',
            'prescription_id' => 'nullable|exists:prescriptions,id',
            'payment_method' => 'required|in:cash,card,upi,bank_transfer,credit',
            'payment_status' => 'required|in:pending,paid,partial,refunded',
            'discount_type' => 'nullable|in:percentage,fixed',
            'discount_value' => 'nullable|numeric|min:0',
            'tax_amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string|max:1000',

            // Sale items validation
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.batch_number' => 'required|string|max:100',
            'items.*.expiry_date' => 'required|date|after:today',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.selling_price' => 'required|numeric|min:0',
            'items.*.discount_amount' => 'nullable|numeric|min:0',
            'items.*.tax_amount' => 'required|numeric|min:0',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'customer_id.exists' => 'Selected customer does not exist.',
            'location_id.required' => 'Location is required.',
            'location_id.exists' => 'Selected location does not exist.',
            'prescription_id.exists' => 'Selected prescription does not exist.',
            'payment_method.required' => 'Payment method is required.',
            'payment_method.in' => 'Invalid payment method selected.',
            'payment_status.required' => 'Payment status is required.',
            'payment_status.in' => 'Invalid payment status selected.',
            'discount_type.in' => 'Invalid discount type selected.',
            'discount_value.numeric' => 'Discount value must be a number.',
            'tax_amount.required' => 'Tax amount is required.',
            'tax_amount.numeric' => 'Tax amount must be a number.',

            'items.required' => 'At least one item is required.',
            'items.array' => 'Items must be an array.',
            'items.min' => 'At least one item is required.',
            'items.*.product_id.required' => 'Product is required for each item.',
            'items.*.product_id.exists' => 'Selected product does not exist.',
            'items.*.batch_number.required' => 'Batch number is required for each item.',
            'items.*.expiry_date.required' => 'Expiry date is required for each item.',
            'items.*.expiry_date.after' => 'Expiry date must be in the future.',
            'items.*.quantity.required' => 'Quantity is required for each item.',
            'items.*.quantity.min' => 'Quantity must be at least 1.',
            'items.*.unit_price.required' => 'Unit price is required for each item.',
            'items.*.selling_price.required' => 'Selling price is required for each item.',
            'items.*.tax_amount.required' => 'Tax amount is required for each item.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Calculate totals if not provided
        if ($this->has('items')) {
            $subtotal = 0;
            $totalTax = 0;
            $totalDiscount = 0;

            foreach ($this->items as $item) {
                $itemTotal = ($item['selling_price'] ?? 0) * ($item['quantity'] ?? 0);
                $itemDiscount = $item['discount_amount'] ?? 0;
                $itemTax = $item['tax_amount'] ?? 0;

                $subtotal += $itemTotal - $itemDiscount;
                $totalTax += $itemTax;
                $totalDiscount += $itemDiscount;
            }

            $this->merge([
                'subtotal' => $subtotal,
                'total_amount' => $subtotal + $totalTax,
                'total_discount' => $totalDiscount,
            ]);
        }
    }
}
