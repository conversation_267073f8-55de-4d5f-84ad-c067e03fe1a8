<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreSupplierRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'pharmacist']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'code' => [
                'nullable',
                'string',
                'max:50',
                'regex:/^[A-Z0-9-]+$/',
                'unique:suppliers,code'
            ],
            'contact_person' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:suppliers,email',
            'phone' => [
                'required',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/'
            ],
            'mobile' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/'
            ],
            'fax' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/'
            ],
            'website' => 'nullable|url|max:255',
            'address_line_1' => 'required|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'postal_code' => [
                'required',
                'string',
                'max:20',
                'regex:/^[A-Z0-9\s\-]+$/i'
            ],
            'country' => 'required|string|max:100',
            'tax_number' => [
                'nullable',
                'string',
                'max:50',
                'unique:suppliers,tax_number'
            ],
            'license_number' => [
                'nullable',
                'string',
                'max:100',
                'unique:suppliers,license_number'
            ],
            'payment_terms' => [
                'required',
                'integer',
                'in:0,7,15,30,45,60,90'
            ],
            'credit_limit' => 'required|numeric|min:0|max:9999999.99',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'is_active' => 'boolean',
            'notes' => 'nullable|string|max:1000',
            'metadata' => 'nullable|array',
            'metadata.*.key' => 'required_with:metadata|string|max:100',
            'metadata.*.value' => 'required_with:metadata|string|max:500',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Supplier name is required.',
            'name.max' => 'Supplier name may not be greater than 255 characters.',
            'code.regex' => 'Supplier code must contain only uppercase letters, numbers, and hyphens.',
            'code.unique' => 'This supplier code already exists.',
            'contact_person.required' => 'Contact person is required.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please provide a valid email address.',
            'email.unique' => 'This email address already exists.',
            'phone.required' => 'Phone number is required.',
            'phone.regex' => 'Please provide a valid phone number.',
            'mobile.regex' => 'Please provide a valid mobile number.',
            'fax.regex' => 'Please provide a valid fax number.',
            'website.url' => 'Please provide a valid website URL.',
            'address_line_1.required' => 'Address line 1 is required.',
            'city.required' => 'City is required.',
            'state.required' => 'State is required.',
            'postal_code.required' => 'Postal code is required.',
            'postal_code.regex' => 'Please provide a valid postal code.',
            'country.required' => 'Country is required.',
            'tax_number.unique' => 'This tax number already exists.',
            'license_number.unique' => 'This license number already exists.',
            'payment_terms.required' => 'Payment terms are required.',
            'payment_terms.in' => 'Payment terms must be one of: 0, 7, 15, 30, 45, 60, or 90 days.',
            'credit_limit.required' => 'Credit limit is required.',
            'credit_limit.min' => 'Credit limit must be at least 0.',
            'credit_limit.max' => 'Credit limit may not be greater than 9,999,999.99.',
            'discount_percentage.min' => 'Discount percentage must be at least 0.',
            'discount_percentage.max' => 'Discount percentage may not be greater than 100.',
            'notes.max' => 'Notes may not be greater than 1000 characters.',
            'metadata.*.key.required_with' => 'Metadata key is required when metadata is provided.',
            'metadata.*.value.required_with' => 'Metadata value is required when metadata is provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'contact_person' => 'contact person',
            'address_line_1' => 'address line 1',
            'address_line_2' => 'address line 2',
            'postal_code' => 'postal code',
            'tax_number' => 'tax number',
            'license_number' => 'license number',
            'payment_terms' => 'payment terms',
            'credit_limit' => 'credit limit',
            'discount_percentage' => 'discount percentage',
            'is_active' => 'active status',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert empty strings to null for nullable fields
        $this->merge([
            'code' => $this->code === '' ? null : $this->code,
            'mobile' => $this->mobile === '' ? null : $this->mobile,
            'fax' => $this->fax === '' ? null : $this->fax,
            'website' => $this->website === '' ? null : $this->website,
            'address_line_2' => $this->address_line_2 === '' ? null : $this->address_line_2,
            'tax_number' => $this->tax_number === '' ? null : $this->tax_number,
            'license_number' => $this->license_number === '' ? null : $this->license_number,
            'discount_percentage' => $this->discount_percentage === '' ? null : $this->discount_percentage,
            'notes' => $this->notes === '' ? null : $this->notes,
            'metadata' => $this->metadata === '' ? null : $this->metadata,
        ]);

        // Set default values
        if (!$this->has('is_active')) {
            $this->merge(['is_active' => true]);
        }

        if (!$this->has('payment_terms')) {
            $this->merge(['payment_terms' => 30]); // Default to 30 days
        }

        if (!$this->has('credit_limit')) {
            $this->merge(['credit_limit' => 0]);
        }

        // Normalize phone numbers (remove spaces and special characters for validation)
        if ($this->phone) {
            $this->merge(['phone' => preg_replace('/[^\+0-9]/', '', $this->phone)]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate email domain for business emails
            if ($this->email && !$this->isValidBusinessEmail($this->email)) {
                $validator->errors()->add('email', 'Please provide a business email address.');
            }

            // Validate metadata structure
            if ($this->metadata) {
                foreach ($this->metadata as $index => $meta) {
                    if (!is_array($meta) || !isset($meta['key']) || !isset($meta['value'])) {
                        $validator->errors()->add("metadata.{$index}", 'Metadata must contain key and value fields.');
                    }
                }
            }

            // Validate credit limit is reasonable
            if ($this->credit_limit > 1000000) {
                $validator->errors()->add('credit_limit', 'Credit limit seems unusually high. Please verify the amount.');
            }
        });
    }

    /**
     * Check if email is a valid business email.
     */
    private function isValidBusinessEmail($email): bool
    {
        $personalDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com'];
        $domain = substr(strrchr($email, "@"), 1);
        
        return !in_array(strtolower($domain), $personalDomains);
    }
}
