<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ApiProtection
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Set JSON response headers
        $request->headers->set('Accept', 'application/json');

        // Check if request is coming from allowed origins (for additional security)
        $allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:8000',
            'http://127.0.0.1:8000',
            config('app.url')
        ];

        $origin = $request->headers->get('Origin');
        if ($origin && !in_array($origin, $allowedOrigins) && config('app.env') === 'production') {
            return response()->json(['error' => 'Unauthorized origin'], 403);
        }

        // Add security headers
        $response = $next($request);

        if (method_exists($response, 'header')) {
            $response->header('X-Content-Type-Options', 'nosniff');
            $response->header('X-Frame-Options', 'DENY');
            $response->header('X-XSS-Protection', '1; mode=block');
        }

        return $response;
    }
}
