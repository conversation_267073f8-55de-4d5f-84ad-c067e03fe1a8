<?php

namespace App\Http\Middleware;

use App\Models\UserActivityLog;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AccountStatusMiddleware
{
    /**
     * Handle an incoming request.
     * Check account status and enforce account policies.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated'
            ], 401);
        }

        $user = $request->user();

        // Check if account is active
        if (!$user->is_active) {
            // Log deactivated account access attempt
            UserActivityLog::log(
                'deactivated_account_access',
                'Attempted to access system with deactivated account',
                $user
            );

            return response()->json([
                'success' => false,
                'message' => 'Account is deactivated. Please contact administrator.',
                'account_status' => 'deactivated'
            ], 403);
        }

        // Check if account is locked
        if ($user->isLocked()) {
            $lockUntil = $user->locked_until;
            $minutesRemaining = $lockUntil->diffInMinutes(now());

            // Log locked account access attempt
            UserActivityLog::log(
                'locked_account_access',
                'Attempted to access system with locked account',
                $user,
                ['locked_until' => $lockUntil, 'minutes_remaining' => $minutesRemaining]
            );

            return response()->json([
                'success' => false,
                'message' => "Account is temporarily locked. Please try again in {$minutesRemaining} minutes.",
                'account_status' => 'locked',
                'locked_until' => $lockUntil,
                'minutes_remaining' => $minutesRemaining
            ], 403);
        }

        // Check if password needs to be changed (except for password change routes)
        if ($user->needsPasswordChange() && !$this->isPasswordChangeRoute($request)) {
            return response()->json([
                'success' => false,
                'message' => 'Password change required. Please update your password.',
                'account_status' => 'password_change_required',
                'requires_password_change' => true,
                'password_age_days' => $user->password_changed_at ? 
                    $user->password_changed_at->diffInDays(now()) : null
            ], 403);
        }

        // Check for suspicious activity (optional security check)
        if ($this->hasSuspiciousActivity($user, $request)) {
            // Log suspicious activity
            UserActivityLog::log(
                'suspicious_activity',
                'Suspicious activity detected',
                $user,
                [
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'reason' => 'Multiple failed login attempts or unusual access pattern'
                ]
            );

            return response()->json([
                'success' => false,
                'message' => 'Suspicious activity detected. Please contact administrator.',
                'account_status' => 'suspicious_activity'
            ], 403);
        }

        return $next($request);
    }

    /**
     * Check if the current route is for password change or profile management.
     */
    private function isPasswordChangeRoute(Request $request): bool
    {
        $allowedRoutes = [
            'api/v1/auth/change-password',
            'api/v1/auth/profile',
            'api/v1/auth/logout',
            'api/v1/auth/me',
        ];

        $currentPath = $request->path();

        foreach ($allowedRoutes as $route) {
            if (str_contains($currentPath, $route)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check for suspicious activity patterns.
     */
    private function hasSuspiciousActivity($user, Request $request): bool
    {
        // Check for too many failed login attempts in the last hour
        if ($user->failed_login_attempts >= 3) {
            $recentFailedLogins = UserActivityLog::where('user_id', $user->id)
                ->where('action', UserActivityLog::ACTION_FAILED_LOGIN)
                ->where('created_at', '>=', now()->subHour())
                ->count();

            if ($recentFailedLogins >= 5) {
                return true;
            }
        }

        // Check for access from unusual IP addresses (if enabled)
        $currentIp = $request->ip();
        $recentLogins = UserActivityLog::where('user_id', $user->id)
            ->where('action', UserActivityLog::ACTION_LOGIN)
            ->where('created_at', '>=', now()->subDays(7))
            ->pluck('ip_address')
            ->unique();

        // If user has logged in from other IPs recently and this is a new IP
        if ($recentLogins->count() > 0 && !$recentLogins->contains($currentIp)) {
            // Allow up to 3 different IPs in a week
            if ($recentLogins->count() >= 3) {
                return true;
            }
        }

        return false;
    }
}
