<?php

namespace App\Http\Middleware;

use App\Models\UserActivityLog;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class StoreAccessMiddleware
{
    /**
     * Handle an incoming request.
     * Ensures users can only access data from their assigned store/location.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated'
            ], 401);
        }

        $user = $request->user();

        // Admins can access all stores
        if ($user->isAdmin()) {
            return $next($request);
        }

        // Check if user has a location assigned
        if (!$user->location_id) {
            return response()->json([
                'success' => false,
                'message' => 'No store/location assigned. Please contact administrator.'
            ], 403);
        }

        // Add location filter to request for non-admin users
        $request->merge(['user_location_id' => $user->location_id]);

        return $next($request);
    }
}
