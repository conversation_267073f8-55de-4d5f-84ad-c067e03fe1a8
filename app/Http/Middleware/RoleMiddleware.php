<?php

namespace App\Http\Middleware;

use App\Models\UserActivityLog;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  ...$roles
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated'
            ], 401);
        }

        $user = $request->user();

        // Check if account is active
        if (!$user->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Account is deactivated. Please contact administrator.'
            ], 403);
        }

        // Check if account is locked
        if ($user->isLocked()) {
            return response()->json([
                'success' => false,
                'message' => 'Account is temporarily locked. Please try again later.'
            ], 403);
        }

        // Check if password needs to be changed
        if ($user->needsPasswordChange() && !$this->isPasswordChangeRoute($request)) {
            return response()->json([
                'success' => false,
                'message' => 'Password change required. Please update your password.',
                'requires_password_change' => true
            ], 403);
        }

        $userRole = $user->role;

        // Check role permissions
        if (!in_array($userRole, $roles)) {
            // Log unauthorized access attempt
            UserActivityLog::log(
                'unauthorized_access',
                "Attempted to access {$request->path()} without proper role",
                $user,
                [
                    'required_roles' => $roles,
                    'user_role' => $userRole,
                    'path' => $request->path(),
                    'method' => $request->method(),
                ]
            );

            return response()->json([
                'success' => false,
                'message' => 'Insufficient permissions. Required roles: ' . implode(', ', $roles)
            ], 403);
        }

        return $next($request);
    }

    /**
     * Check if the current route is for password change.
     */
    private function isPasswordChangeRoute(Request $request): bool
    {
        $passwordChangeRoutes = [
            'api/v1/auth/change-password',
            'api/v1/auth/profile',
            'api/v1/auth/logout',
        ];

        return in_array($request->path(), $passwordChangeRoutes);
    }
}
