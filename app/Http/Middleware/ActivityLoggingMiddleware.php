<?php

namespace App\Http\Middleware;

use App\Models\UserActivityLog;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ActivityLoggingMiddleware
{
    /**
     * Handle an incoming request and log user activities.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only log for authenticated users
        if ($request->user()) {
            $this->logActivity($request, $response);
        }

        return $response;
    }

    /**
     * Log user activity based on request and response.
     */
    private function logActivity(Request $request, Response $response): void
    {
        $user = $request->user();
        $method = $request->method();
        $path = $request->path();
        $statusCode = $response->getStatusCode();

        // Skip logging for certain routes
        if ($this->shouldSkipLogging($path, $method)) {
            return;
        }

        // Determine action and description based on route and method
        [$action, $description] = $this->determineActionAndDescription($path, $method, $statusCode);

        if ($action) {
            // Prepare payload (exclude sensitive data)
            $payload = $this->preparePayload($request);

            UserActivityLog::log(
                $action,
                $description,
                $user,
                $payload,
                $statusCode
            );
        }
    }

    /**
     * Determine if logging should be skipped for this request.
     */
    private function shouldSkipLogging(string $path, string $method): bool
    {
        $skipRoutes = [
            'api/v1/auth/profile' => ['GET'], // Skip profile GET requests
            'api/v1/auth/me' => ['GET'],
            'sanctum/csrf-cookie' => ['GET'],
        ];

        foreach ($skipRoutes as $route => $methods) {
            if (str_contains($path, $route) && in_array($method, $methods)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Determine action and description based on route and method.
     */
    private function determineActionAndDescription(string $path, string $method, int $statusCode): array
    {
        // Only log successful operations (2xx status codes)
        if ($statusCode < 200 || $statusCode >= 300) {
            return [null, null];
        }

        // User management actions
        if (str_contains($path, 'api/v1/users')) {
            return $this->getUserManagementAction($path, $method);
        }

        // Product management actions
        if (str_contains($path, 'api/v1/products')) {
            return $this->getProductManagementAction($path, $method);
        }

        // Sales actions
        if (str_contains($path, 'api/v1/sales')) {
            return $this->getSalesAction($path, $method);
        }

        // Inventory actions
        if (str_contains($path, 'api/v1/inventory')) {
            return $this->getInventoryAction($path, $method);
        }

        // Purchase actions
        if (str_contains($path, 'api/v1/purchases')) {
            return $this->getPurchaseAction($path, $method);
        }

        // Profile actions
        if (str_contains($path, 'api/v1/auth/profile')) {
            return $this->getProfileAction($path, $method);
        }

        return [null, null];
    }

    /**
     * Get user management action.
     */
    private function getUserManagementAction(string $path, string $method): array
    {
        switch ($method) {
            case 'POST':
                return [UserActivityLog::ACTION_CREATE_USER, 'Created new user'];
            case 'PUT':
            case 'PATCH':
                return [UserActivityLog::ACTION_UPDATE_USER, 'Updated user information'];
            case 'DELETE':
                return [UserActivityLog::ACTION_DELETE_USER, 'Deleted user'];
            default:
                return [null, null];
        }
    }

    /**
     * Get product management action.
     */
    private function getProductManagementAction(string $path, string $method): array
    {
        switch ($method) {
            case 'POST':
                return [UserActivityLog::ACTION_CREATE_PRODUCT, 'Created new product'];
            case 'PUT':
            case 'PATCH':
                return [UserActivityLog::ACTION_UPDATE_PRODUCT, 'Updated product information'];
            case 'DELETE':
                return [UserActivityLog::ACTION_DELETE_PRODUCT, 'Deleted product'];
            default:
                return [null, null];
        }
    }

    /**
     * Get sales action.
     */
    private function getSalesAction(string $path, string $method): array
    {
        if (str_contains($path, 'refund')) {
            return [UserActivityLog::ACTION_REFUND_SALE, 'Processed sale refund'];
        }

        switch ($method) {
            case 'POST':
                return [UserActivityLog::ACTION_PROCESS_SALE, 'Processed new sale'];
            default:
                return [null, null];
        }
    }

    /**
     * Get inventory action.
     */
    private function getInventoryAction(string $path, string $method): array
    {
        if (str_contains($path, 'adjust')) {
            return [UserActivityLog::ACTION_STOCK_ADJUSTMENT, 'Made stock adjustment'];
        }

        return [null, null];
    }

    /**
     * Get purchase action.
     */
    private function getPurchaseAction(string $path, string $method): array
    {
        if (str_contains($path, 'receive')) {
            return [UserActivityLog::ACTION_RECEIVE_PURCHASE, 'Received purchase order'];
        }

        switch ($method) {
            case 'POST':
                return [UserActivityLog::ACTION_CREATE_PURCHASE, 'Created purchase order'];
            default:
                return [null, null];
        }
    }

    /**
     * Get profile action.
     */
    private function getProfileAction(string $path, string $method): array
    {
        switch ($method) {
            case 'PUT':
            case 'PATCH':
                return [UserActivityLog::ACTION_PROFILE_UPDATE, 'Updated profile information'];
            default:
                return [null, null];
        }
    }

    /**
     * Prepare payload for logging (exclude sensitive data).
     */
    private function preparePayload(Request $request): ?array
    {
        $payload = $request->except([
            'password',
            'password_confirmation',
            'current_password',
            'new_password',
            'token',
            '_token',
        ]);

        // Limit payload size
        $jsonPayload = json_encode($payload);
        if (strlen($jsonPayload) > 5000) { // 5KB limit
            return ['message' => 'Payload too large to log'];
        }

        return $payload ?: null;
    }
}
