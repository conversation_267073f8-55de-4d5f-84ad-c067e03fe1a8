<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\UserActivityLog;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class ProfileController extends Controller
{
    /**
     * Get user profile.
     */
    public function show(Request $request): JsonResponse
    {
        $user = $request->user();
        $user->load(['location', 'activityLogs' => function($query) {
            $query->latest()->limit(10);
        }]);

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'role_display_name' => $user->role_display_name,
                'phone' => $user->phone,
                'address' => $user->address,
                'employee_id' => $user->employee_id,
                'avatar_url' => $user->avatar_url,
                'location' => $user->location,
                'emergency_contact_name' => $user->emergency_contact_name,
                'emergency_contact_phone' => $user->emergency_contact_phone,
                'hire_date' => $user->hire_date,
                'last_login_at' => $user->last_login_at,
                'login_count' => $user->login_count,
                'must_change_password' => $user->needsPasswordChange(),
                'two_factor_enabled' => $user->two_factor_enabled,
                'recent_activities' => $user->activityLogs,
                'created_at' => $user->created_at,
            ]
        ]);
    }

    /**
     * Update user profile.
     */
    public function update(Request $request): JsonResponse
    {
        $user = $request->user();

        $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
        ]);

        $oldData = [
            'name' => $user->name,
            'phone' => $user->phone,
            'address' => $user->address,
            'emergency_contact_name' => $user->emergency_contact_name,
            'emergency_contact_phone' => $user->emergency_contact_phone,
        ];

        $user->update($request->only([
            'name',
            'phone', 
            'address',
            'emergency_contact_name',
            'emergency_contact_phone',
        ]));

        // Log profile update
        UserActivityLog::log(
            UserActivityLog::ACTION_PROFILE_UPDATE,
            'Profile updated',
            $user,
            [
                'old_data' => $oldData,
                'new_data' => $request->only([
                    'name',
                    'phone',
                    'address', 
                    'emergency_contact_name',
                    'emergency_contact_phone',
                ])
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'Profile updated successfully',
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'address' => $user->address,
                'emergency_contact_name' => $user->emergency_contact_name,
                'emergency_contact_phone' => $user->emergency_contact_phone,
                'avatar_url' => $user->avatar_url,
            ]
        ]);
    }

    /**
     * Upload user avatar.
     */
    public function uploadAvatar(Request $request): JsonResponse
    {
        $request->validate([
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048', // 2MB max
        ]);

        $user = $request->user();

        try {
            // Delete old avatar if exists
            if ($user->avatar) {
                Storage::disk('public')->delete('avatars/' . $user->avatar);
            }

            // Store new avatar
            $file = $request->file('avatar');
            $filename = $user->id . '_' . time() . '.' . $file->getClientOriginalExtension();
            $file->storeAs('avatars', $filename, 'public');

            // Update user avatar
            $user->update(['avatar' => $filename]);

            // Log avatar upload
            UserActivityLog::log(
                UserActivityLog::ACTION_PROFILE_UPDATE,
                'Avatar uploaded',
                $user,
                ['filename' => $filename]
            );

            return response()->json([
                'success' => true,
                'message' => 'Avatar uploaded successfully',
                'data' => [
                    'avatar_url' => $user->avatar_url,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload avatar',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove user avatar.
     */
    public function removeAvatar(Request $request): JsonResponse
    {
        $user = $request->user();

        try {
            // Delete avatar file if exists
            if ($user->avatar) {
                Storage::disk('public')->delete('avatars/' . $user->avatar);
                $user->update(['avatar' => null]);

                // Log avatar removal
                UserActivityLog::log(
                    UserActivityLog::ACTION_PROFILE_UPDATE,
                    'Avatar removed',
                    $user
                );
            }

            return response()->json([
                'success' => true,
                'message' => 'Avatar removed successfully',
                'data' => [
                    'avatar_url' => $user->avatar_url, // Will return default avatar
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove avatar',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user activity logs.
     */
    public function activityLogs(Request $request): JsonResponse
    {
        $user = $request->user();

        $request->validate([
            'per_page' => 'integer|min:1|max:100',
            'action' => 'nullable|string',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        $query = $user->activityLogs()->with('location');

        // Filter by action
        if ($request->action) {
            $query->byAction($request->action);
        }

        // Filter by date range
        if ($request->date_from && $request->date_to) {
            $query->byDateRange($request->date_from, $request->date_to);
        }

        $activities = $query->latest()
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $activities
        ]);
    }

    /**
     * Get user sessions/tokens.
     */
    public function sessions(Request $request): JsonResponse
    {
        $user = $request->user();
        $currentTokenId = $user->currentAccessToken()->id;

        $tokens = $user->tokens()->get()->map(function ($token) use ($currentTokenId) {
            return [
                'id' => $token->id,
                'name' => $token->name,
                'is_current' => $token->id === $currentTokenId,
                'last_used_at' => $token->last_used_at,
                'created_at' => $token->created_at,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => [
                'current_session_id' => $currentTokenId,
                'sessions' => $tokens,
            ]
        ]);
    }

    /**
     * Revoke a specific session/token.
     */
    public function revokeSession(Request $request): JsonResponse
    {
        $request->validate([
            'token_id' => 'required|integer',
        ]);

        $user = $request->user();
        $currentTokenId = $user->currentAccessToken()->id;

        // Prevent revoking current session
        if ($request->token_id == $currentTokenId) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot revoke current session. Use logout instead.'
            ], 422);
        }

        $token = $user->tokens()->find($request->token_id);

        if (!$token) {
            return response()->json([
                'success' => false,
                'message' => 'Session not found.'
            ], 404);
        }

        $token->delete();

        // Log session revocation
        UserActivityLog::log(
            UserActivityLog::ACTION_LOGOUT,
            'Session revoked',
            $user,
            ['revoked_token_id' => $request->token_id]
        );

        return response()->json([
            'success' => true,
            'message' => 'Session revoked successfully.'
        ]);
    }
}
