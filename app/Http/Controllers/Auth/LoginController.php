<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserActivityLog;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class LoginController extends Controller
{
    /**
     * Handle user login with rate limiting.
     */
    public function login(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|string',
            'remember' => 'boolean',
        ]);

        // Rate limiting key
        $key = Str::transliterate(Str::lower($request->input('email')).'|'.$request->ip());

        // Check rate limit (5 attempts per minute)
        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);

            UserActivityLog::log(
                UserActivityLog::ACTION_FAILED_LOGIN,
                'Rate limit exceeded for email: ' . $request->email,
                null,
                ['email' => $request->email, 'reason' => 'rate_limit']
            );

            throw ValidationException::withMessages([
                'email' => "Too many login attempts. Please try again in {$seconds} seconds.",
            ]);
        }

        // Find user
        $user = User::where('email', $request->email)->first();

        if (!$user) {
            RateLimiter::hit($key);

            UserActivityLog::log(
                UserActivityLog::ACTION_FAILED_LOGIN,
                'Login attempt with non-existent email: ' . $request->email,
                null,
                ['email' => $request->email, 'reason' => 'user_not_found']
            );

            throw ValidationException::withMessages([
                'email' => 'The provided credentials are incorrect.',
            ]);
        }

        // Check if account is locked
        if ($user->isLocked()) {
            RateLimiter::hit($key);

            UserActivityLog::log(
                UserActivityLog::ACTION_FAILED_LOGIN,
                'Login attempt on locked account',
                $user,
                ['reason' => 'account_locked']
            );

            throw ValidationException::withMessages([
                'email' => 'Account is temporarily locked. Please try again later.',
            ]);
        }

        // Check if account is active
        if (!$user->is_active) {
            RateLimiter::hit($key);

            UserActivityLog::log(
                UserActivityLog::ACTION_FAILED_LOGIN,
                'Login attempt on inactive account',
                $user,
                ['reason' => 'account_inactive']
            );

            throw ValidationException::withMessages([
                'email' => 'Account is deactivated. Please contact administrator.',
            ]);
        }

        // Verify password
        if (!Hash::check($request->password, $user->password)) {
            RateLimiter::hit($key);
            $user->incrementFailedLoginAttempts();

            UserActivityLog::log(
                UserActivityLog::ACTION_FAILED_LOGIN,
                'Invalid password attempt',
                $user,
                ['reason' => 'invalid_password']
            );

            throw ValidationException::withMessages([
                'email' => 'The provided credentials are incorrect.',
            ]);
        }

        // Clear rate limiting and failed attempts
        RateLimiter::clear($key);
        $user->resetFailedLoginAttempts();

        // Update last login
        $user->updateLastLogin();

        // Create token
        $tokenName = $request->userAgent() ?? 'Unknown Device';
        $token = $user->createToken($tokenName);

        // Log successful login
        UserActivityLog::log(
            UserActivityLog::ACTION_LOGIN,
            'User logged in successfully',
            $user,
            ['token_name' => $tokenName]
        );

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'role_display_name' => $user->role_display_name,
                    'phone' => $user->phone,
                    'avatar_url' => $user->avatar_url,
                    'location_id' => $user->location_id,
                    'location' => $user->location,
                    'last_login_at' => $user->last_login_at,
                    'must_change_password' => $user->needsPasswordChange(),
                ],
                'token' => $token->plainTextToken,
                'expires_at' => $token->accessToken->expires_at,
            ]
        ]);
    }

    /**
     * Handle user logout.
     */
    public function logout(Request $request): JsonResponse
    {
        $user = $request->user();

        // Revoke current token
        $request->user()->currentAccessToken()->delete();

        // Log logout
        UserActivityLog::log(
            UserActivityLog::ACTION_LOGOUT,
            'User logged out successfully',
            $user
        );

        return response()->json([
            'success' => true,
            'message' => 'Logout successful'
        ]);
    }

    /**
     * Logout from all devices.
     */
    public function logoutAll(Request $request): JsonResponse
    {
        $user = $request->user();

        // Revoke all tokens
        $user->tokens()->delete();

        // Log logout from all devices
        UserActivityLog::log(
            UserActivityLog::ACTION_LOGOUT,
            'User logged out from all devices',
            $user
        );

        return response()->json([
            'success' => true,
            'message' => 'Logged out from all devices successfully'
        ]);
    }

    /**
     * Get current user information.
     */
    public function me(Request $request): JsonResponse
    {
        $user = $request->user();
        $user->load('location');

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'role_display_name' => $user->role_display_name,
                'phone' => $user->phone,
                'avatar_url' => $user->avatar_url,
                'location_id' => $user->location_id,
                'location' => $user->location,
                'last_login_at' => $user->last_login_at,
                'login_count' => $user->login_count,
                'must_change_password' => $user->needsPasswordChange(),
                'two_factor_enabled' => $user->two_factor_enabled,
            ]
        ]);
    }
}
