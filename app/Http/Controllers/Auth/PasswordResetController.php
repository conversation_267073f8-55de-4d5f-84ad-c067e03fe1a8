<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserActivityLog;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password as PasswordRule;
use Illuminate\Validation\ValidationException;

class PasswordResetController extends Controller
{
    /**
     * Send password reset link.
     */
    public function sendResetLink(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email|exists:users,email',
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Account is deactivated. Please contact administrator.'
            ], 422);
        }

        $status = Password::sendResetLink(
            $request->only('email')
        );

        if ($status === Password::RESET_LINK_SENT) {
            // Log password reset request
            UserActivityLog::log(
                UserActivityLog::ACTION_PASSWORD_CHANGE,
                'Password reset link requested',
                $user
            );

            return response()->json([
                'success' => true,
                'message' => 'Password reset link sent to your email.'
            ]);
        }

        throw ValidationException::withMessages([
            'email' => [trans($status)],
        ]);
    }

    /**
     * Reset password using token.
     */
    public function resetPassword(Request $request): JsonResponse
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => ['required', 'confirmed', PasswordRule::min(8)
                ->letters()
                ->mixedCase()
                ->numbers()
                ->symbols()
            ],
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function (User $user, string $password) {
                $user->forceFill([
                    'password' => Hash::make($password),
                    'password_changed_at' => now(),
                    'must_change_password' => false,
                ])->setRememberToken(Str::random(60));

                $user->save();

                // Revoke all existing tokens
                $user->tokens()->delete();

                // Log password reset
                UserActivityLog::log(
                    UserActivityLog::ACTION_PASSWORD_CHANGE,
                    'Password reset completed via email token',
                    $user
                );
            }
        );

        if ($status === Password::PASSWORD_RESET) {
            return response()->json([
                'success' => true,
                'message' => 'Password reset successfully. Please login with your new password.'
            ]);
        }

        throw ValidationException::withMessages([
            'email' => [trans($status)],
        ]);
    }

    /**
     * Change password for authenticated user.
     */
    public function changePassword(Request $request): JsonResponse
    {
        $request->validate([
            'current_password' => 'required',
            'password' => ['required', 'confirmed', PasswordRule::min(8)
                ->letters()
                ->mixedCase()
                ->numbers()
                ->symbols()
            ],
        ]);

        $user = $request->user();

        // Verify current password
        if (!Hash::check($request->current_password, $user->password)) {
            throw ValidationException::withMessages([
                'current_password' => ['The current password is incorrect.'],
            ]);
        }

        // Check if new password is different from current
        if (Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'password' => ['New password must be different from current password.'],
            ]);
        }

        // Update password
        $user->update([
            'password' => Hash::make($request->password),
            'password_changed_at' => now(),
            'must_change_password' => false,
        ]);

        // Revoke all other tokens except current
        $user->tokens()->where('id', '!=', $user->currentAccessToken()->id)->delete();

        // Log password change
        UserActivityLog::log(
            UserActivityLog::ACTION_PASSWORD_CHANGE,
            'Password changed by user',
            $user
        );

        return response()->json([
            'success' => true,
            'message' => 'Password changed successfully.'
        ]);
    }

    /**
     * Force password reset for a user (Admin only).
     */
    public function forcePasswordReset(Request $request, User $user): JsonResponse
    {
        // Ensure only admins can force password reset
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Only administrators can force password reset.'
            ], 403);
        }

        $request->validate([
            'new_password' => ['nullable', 'confirmed', PasswordRule::min(8)
                ->letters()
                ->mixedCase()
                ->numbers()
                ->symbols()
            ],
            'must_change_password' => 'boolean',
        ]);

        $newPassword = $request->new_password;
        $mustChangePassword = $request->boolean('must_change_password', true);

        // Generate temporary password if not provided
        if (!$newPassword) {
            $newPassword = 'TempPass' . rand(1000, 9999) . '!';
            $mustChangePassword = true;
        }

        // Update user password
        $user->update([
            'password' => Hash::make($newPassword),
            'password_changed_at' => now(),
            'must_change_password' => $mustChangePassword,
            'locked_until' => null, // Unlock account if locked
            'failed_login_attempts' => 0,
        ]);

        // Revoke all user tokens
        $user->tokens()->delete();

        // Log forced password reset
        UserActivityLog::log(
            UserActivityLog::ACTION_PASSWORD_CHANGE,
            "Password reset forced for user: {$user->name}",
            $request->user(),
            [
                'target_user_id' => $user->id,
                'target_user_email' => $user->email,
                'must_change_password' => $mustChangePassword,
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'Password reset successfully.',
            'data' => [
                'temporary_password' => $request->new_password ? null : $newPassword,
                'must_change_password' => $mustChangePassword,
            ]
        ]);
    }

    /**
     * Generate temporary password for user (Admin only).
     */
    public function generateTempPassword(Request $request, User $user): JsonResponse
    {
        // Ensure only admins can generate temp passwords
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Only administrators can generate temporary passwords.'
            ], 403);
        }

        // Generate temporary password
        $tempPassword = 'TempPass' . rand(1000, 9999) . '!';

        // Update user password
        $user->update([
            'password' => Hash::make($tempPassword),
            'password_changed_at' => now(),
            'must_change_password' => true,
            'locked_until' => null, // Unlock account if locked
            'failed_login_attempts' => 0,
        ]);

        // Revoke all user tokens
        $user->tokens()->delete();

        // Log temp password generation
        UserActivityLog::log(
            UserActivityLog::ACTION_PASSWORD_CHANGE,
            "Temporary password generated for user: {$user->name}",
            $request->user(),
            [
                'target_user_id' => $user->id,
                'target_user_email' => $user->email,
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'Temporary password generated successfully.',
            'data' => [
                'temporary_password' => $tempPassword,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                ]
            ]
        ]);
    }
}
