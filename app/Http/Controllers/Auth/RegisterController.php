<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserActivityLog;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class RegisterController extends Controller
{
    /**
     * Create a new user (Admin only).
     */
    public function register(Request $request): JsonResponse
    {
        // Ensure only admins can create users
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Only administrators can create users.'
            ], 403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', 'confirmed', Password::min(8)
                ->letters()
                ->mixedCase()
                ->numbers()
                ->symbols()
            ],
            'role' => 'required|in:admin,pharmacist,cashier',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'employee_id' => 'required|string|max:50|unique:users',
            'salary' => 'nullable|numeric|min:0',
            'hire_date' => 'nullable|date',
            'location_id' => 'required|exists:locations,id',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:1000',
            'must_change_password' => 'boolean',
        ]);

        try {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => $request->role,
                'phone' => $request->phone,
                'address' => $request->address,
                'employee_id' => $request->employee_id,
                'salary' => $request->salary,
                'hire_date' => $request->hire_date,
                'location_id' => $request->location_id,
                'emergency_contact_name' => $request->emergency_contact_name,
                'emergency_contact_phone' => $request->emergency_contact_phone,
                'notes' => $request->notes,
                'is_active' => true,
                'must_change_password' => $request->boolean('must_change_password', true),
                'password_changed_at' => now(),
            ]);

            // Log user creation
            UserActivityLog::log(
                UserActivityLog::ACTION_CREATE_USER,
                "Created new user: {$user->name} ({$user->email})",
                $request->user(),
                [
                    'created_user_id' => $user->id,
                    'created_user_role' => $user->role,
                    'created_user_email' => $user->email,
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'User created successfully',
                'data' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'role_display_name' => $user->role_display_name,
                    'employee_id' => $user->employee_id,
                    'phone' => $user->phone,
                    'location_id' => $user->location_id,
                    'is_active' => $user->is_active,
                    'must_change_password' => $user->must_change_password,
                    'created_at' => $user->created_at,
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create user',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk create users from CSV/array (Admin only).
     */
    public function bulkRegister(Request $request): JsonResponse
    {
        // Ensure only admins can bulk create users
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Only administrators can bulk create users.'
            ], 403);
        }

        $request->validate([
            'users' => 'required|array|min:1|max:50',
            'users.*.name' => 'required|string|max:255',
            'users.*.email' => 'required|string|email|max:255|unique:users,email',
            'users.*.role' => 'required|in:admin,pharmacist,cashier',
            'users.*.employee_id' => 'required|string|max:50|unique:users,employee_id',
            'users.*.location_id' => 'required|exists:locations,id',
            'users.*.phone' => 'nullable|string|max:20',
        ]);

        $createdUsers = [];
        $errors = [];

        foreach ($request->users as $index => $userData) {
            try {
                // Generate temporary password
                $tempPassword = 'TempPass' . rand(1000, 9999) . '!';
                
                $user = User::create([
                    'name' => $userData['name'],
                    'email' => $userData['email'],
                    'password' => Hash::make($tempPassword),
                    'role' => $userData['role'],
                    'employee_id' => $userData['employee_id'],
                    'location_id' => $userData['location_id'],
                    'phone' => $userData['phone'] ?? null,
                    'is_active' => true,
                    'must_change_password' => true,
                    'password_changed_at' => now(),
                ]);

                $createdUsers[] = [
                    'user' => $user,
                    'temporary_password' => $tempPassword,
                ];

            } catch (\Exception $e) {
                $errors[] = [
                    'index' => $index,
                    'email' => $userData['email'],
                    'error' => $e->getMessage(),
                ];
            }
        }

        // Log bulk user creation
        UserActivityLog::log(
            UserActivityLog::ACTION_CREATE_USER,
            "Bulk created {" . count($createdUsers) . "} users",
            $request->user(),
            [
                'created_count' => count($createdUsers),
                'error_count' => count($errors),
            ]
        );

        return response()->json([
            'success' => true,
            'message' => count($createdUsers) . ' users created successfully',
            'data' => [
                'created_users' => array_map(function($item) {
                    return [
                        'id' => $item['user']->id,
                        'name' => $item['user']->name,
                        'email' => $item['user']->email,
                        'role' => $item['user']->role,
                        'employee_id' => $item['user']->employee_id,
                        'temporary_password' => $item['temporary_password'],
                    ];
                }, $createdUsers),
                'errors' => $errors,
            ]
        ], 201);
    }
}
