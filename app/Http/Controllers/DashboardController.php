<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function stats()
    {
        // Mock data for dashboard statistics
        return response()->json([
            'total_sales' => 12345.67,
            'total_products' => 1234,
            'low_stock_items' => 23,
            'total_orders' => 456,
            'recent_transactions' => [
                ['id' => 1, 'customer' => '<PERSON>', 'amount' => 45.99, 'date' => '2024-01-15'],
                ['id' => 2, 'customer' => '<PERSON>', 'amount' => 23.50, 'date' => '2024-01-15'],
                ['id' => 3, 'customer' => '<PERSON>', 'amount' => 67.25, 'date' => '2024-01-14'],
            ]
        ]);
    }
}
