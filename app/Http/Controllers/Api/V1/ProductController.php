<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\StoreProductRequest;
use App\Http\Requests\Api\V1\UpdateProductRequest;
use App\Http\Resources\Api\V1\ProductResource;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Category;
use App\Models\Supplier;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $query = Product::with(['category', 'supplier', 'inventory']);

        // Search functionality
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('generic_name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->has('category_id')) {
            $query->where('category_id', $request->get('category_id'));
        }

        // Filter by supplier
        if ($request->has('supplier_id')) {
            $query->where('supplier_id', $request->get('supplier_id'));
        }

        // Filter by active status
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Filter by prescription requirement
        if ($request->has('is_prescription_required')) {
            $query->where('is_prescription_required', $request->boolean('is_prescription_required'));
        }

        // Filter by low stock
        if ($request->boolean('low_stock')) {
            $query->lowStock();
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        $products = $query->paginate($request->get('per_page', 15));

        return ProductResource::collection($products);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreProductRequest $request): JsonResponse
    {
        try {
            $product = Product::create($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Product created successfully',
                'data' => new ProductResource($product->load(['category', 'supplier']))
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create product',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product): JsonResponse
    {
        $product->load(['category', 'supplier', 'inventory.location']);

        return response()->json([
            'success' => true,
            'data' => new ProductResource($product)
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateProductRequest $request, Product $product): JsonResponse
    {
        try {
            $product->update($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Product updated successfully',
                'data' => new ProductResource($product->load(['category', 'supplier']))
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update product',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product): JsonResponse
    {
        try {
            // Check if product has any sales or inventory
            if ($product->saleItems()->exists() || $product->inventory()->where('quantity_available', '>', 0)->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete product with existing sales or inventory'
                ], 422);
            }

            $product->delete();

            return response()->json([
                'success' => true,
                'message' => 'Product deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete product',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get products with low stock.
     */
    public function lowStock(Request $request): AnonymousResourceCollection
    {
        $products = Product::with(['category', 'supplier', 'inventory'])
            ->lowStock()
            ->paginate($request->get('per_page', 15));

        return ProductResource::collection($products);
    }

    /**
     * Search products by barcode.
     */
    public function searchByBarcode(Request $request): JsonResponse
    {
        $request->validate([
            'barcode' => 'required|string'
        ]);

        $product = Product::with(['category', 'supplier', 'inventory', 'variants'])
            ->where('barcode', $request->barcode)
            ->first();

        if (!$product) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => new ProductResource($product)
        ]);
    }

    /**
     * Generate a unique barcode for a product.
     */
    public function generateBarcode(): JsonResponse
    {
        try {
            $barcode = Product::generateBarcode();

            return response()->json([
                'success' => true,
                'data' => ['barcode' => $barcode]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate barcode',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload product image.
     */
    public function uploadImage(Request $request, Product $product): JsonResponse
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        try {
            // Delete old image if exists
            if ($product->image && Storage::disk('public')->exists($product->image)) {
                Storage::disk('public')->delete($product->image);
            }

            // Store new image
            $imagePath = $request->file('image')->store('products', 'public');

            $product->update(['image' => $imagePath]);

            return response()->json([
                'success' => true,
                'message' => 'Image uploaded successfully',
                'data' => [
                    'image_path' => $imagePath,
                    'image_url' => Storage::disk('public')->url($imagePath)
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload image',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete product image.
     */
    public function deleteImage(Product $product): JsonResponse
    {
        try {
            if ($product->image && Storage::disk('public')->exists($product->image)) {
                Storage::disk('public')->delete($product->image);
                $product->update(['image' => null]);

                return response()->json([
                    'success' => true,
                    'message' => 'Image deleted successfully'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'No image found to delete'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete image',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk import products from CSV.
     */
    public function bulkImport(Request $request): JsonResponse
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,txt|max:10240', // 10MB max
            'update_existing' => 'boolean'
        ]);

        try {
            $file = $request->file('file');
            $updateExisting = $request->boolean('update_existing', false);

            $csvData = array_map('str_getcsv', file($file->getPathname()));
            $header = array_shift($csvData);

            // Validate CSV headers
            $requiredHeaders = ['name', 'sku', 'category_id', 'supplier_id', 'unit_price', 'selling_price'];
            $missingHeaders = array_diff($requiredHeaders, $header);

            if (!empty($missingHeaders)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Missing required headers: ' . implode(', ', $missingHeaders)
                ], 422);
            }

            $results = [
                'total' => count($csvData),
                'created' => 0,
                'updated' => 0,
                'errors' => []
            ];

            DB::beginTransaction();

            foreach ($csvData as $index => $row) {
                $rowData = array_combine($header, $row);
                $rowNumber = $index + 2; // +2 because we removed header and arrays are 0-indexed

                try {
                    // Validate row data
                    $validator = Validator::make($rowData, [
                        'name' => 'required|string|max:255',
                        'sku' => 'required|string|max:100',
                        'category_id' => 'required|exists:categories,id',
                        'supplier_id' => 'required|exists:suppliers,id',
                        'unit_price' => 'required|numeric|min:0',
                        'selling_price' => 'required|numeric|min:0',
                        'barcode' => 'nullable|string|unique:products,barcode',
                        'generic_name' => 'nullable|string|max:255',
                        'brand' => 'nullable|string|max:255',
                        'strength' => 'nullable|string|max:100',
                        'dosage_form' => 'nullable|string|max:100',
                        'reorder_level' => 'nullable|integer|min:0',
                        'is_prescription_required' => 'nullable|boolean',
                        'is_active' => 'nullable|boolean'
                    ]);

                    if ($validator->fails()) {
                        $results['errors'][] = "Row {$rowNumber}: " . implode(', ', $validator->errors()->all());
                        continue;
                    }

                    // Check if product exists
                    $existingProduct = Product::where('sku', $rowData['sku'])->first();

                    if ($existingProduct) {
                        if ($updateExisting) {
                            $existingProduct->update($rowData);
                            $results['updated']++;
                        } else {
                            $results['errors'][] = "Row {$rowNumber}: Product with SKU {$rowData['sku']} already exists";
                        }
                    } else {
                        // Generate barcode if not provided
                        if (empty($rowData['barcode'])) {
                            $rowData['barcode'] = Product::generateBarcode();
                        }

                        Product::create($rowData);
                        $results['created']++;
                    }
                } catch (\Exception $e) {
                    $results['errors'][] = "Row {$rowNumber}: " . $e->getMessage();
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Bulk import completed',
                'data' => $results
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Bulk import failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get product variants.
     */
    public function variants(Product $product): JsonResponse
    {
        $variants = $product->variants()->active()->get();

        return response()->json([
            'success' => true,
            'data' => $variants
        ]);
    }

    /**
     * Create a product variant.
     */
    public function createVariant(Request $request, Product $product): JsonResponse
    {
        $request->validate([
            'variant_name' => 'required|string|max:255',
            'variant_type' => 'required|in:size,packaging,strength,form',
            'sku' => 'required|string|max:100|unique:product_variants,sku',
            'barcode' => 'nullable|string|unique:product_variants,barcode',
            'unit_size' => 'nullable|numeric|min:0',
            'unit_type' => 'nullable|string|max:50',
            'conversion_factor' => 'required|numeric|min:0.0001',
            'unit_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'is_active' => 'boolean'
        ]);

        try {
            $variantData = $request->validated();
            $variantData['product_id'] = $product->id;

            // Generate barcode if not provided
            if (empty($variantData['barcode'])) {
                $variantData['barcode'] = Product::generateBarcode();
            }

            $variant = ProductVariant::create($variantData);

            return response()->json([
                'success' => true,
                'message' => 'Product variant created successfully',
                'data' => $variant
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create product variant',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a product variant.
     */
    public function updateVariant(Request $request, Product $product, ProductVariant $variant): JsonResponse
    {
        // Ensure variant belongs to product
        if ($variant->product_id !== $product->id) {
            return response()->json([
                'success' => false,
                'message' => 'Variant does not belong to this product'
            ], 422);
        }

        $request->validate([
            'variant_name' => 'sometimes|string|max:255',
            'variant_type' => 'sometimes|in:size,packaging,strength,form',
            'sku' => ['sometimes', 'string', 'max:100', Rule::unique('product_variants')->ignore($variant->id)],
            'barcode' => ['nullable', 'string', Rule::unique('product_variants')->ignore($variant->id)],
            'unit_size' => 'sometimes|numeric|min:0',
            'unit_type' => 'sometimes|string|max:50',
            'conversion_factor' => 'sometimes|numeric|min:0.0001',
            'unit_price' => 'sometimes|numeric|min:0',
            'selling_price' => 'sometimes|numeric|min:0',
            'is_active' => 'sometimes|boolean'
        ]);

        try {
            $variant->update($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Product variant updated successfully',
                'data' => $variant->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update product variant',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a product variant.
     */
    public function deleteVariant(Product $product, ProductVariant $variant): JsonResponse
    {
        // Ensure variant belongs to product
        if ($variant->product_id !== $product->id) {
            return response()->json([
                'success' => false,
                'message' => 'Variant does not belong to this product'
            ], 422);
        }

        try {
            // Check if variant has inventory or sales
            if ($variant->inventory()->exists() || $variant->saleItems()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete variant with existing inventory or sales'
                ], 422);
            }

            $variant->delete();

            return response()->json([
                'success' => true,
                'message' => 'Product variant deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete product variant',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get product inventory summary.
     */
    public function inventorySummary(Product $product): JsonResponse
    {
        $summary = [
            'total_quantity' => $product->total_quantity,
            'current_stock_value' => $product->current_stock_value,
            'potential_selling_value' => $product->potential_selling_value,
            'profit_margin' => $product->profit_margin,
            'is_low_stock' => $product->isLowStock(),
            'locations' => []
        ];

        // Get inventory by location
        $inventoryByLocation = $product->inventory()
            ->with('location')
            ->where('status', 'active')
            ->where('quantity_available', '>', 0)
            ->get()
            ->groupBy('location_id');

        foreach ($inventoryByLocation as $locationId => $inventories) {
            $location = $inventories->first()->location;
            $summary['locations'][] = [
                'location_id' => $locationId,
                'location_name' => $location->name,
                'quantity' => $inventories->sum('quantity_available'),
                'value' => $inventories->sum(function ($inv) {
                    return $inv->quantity_available * $inv->cost_price;
                }),
                'batches' => $inventories->count()
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $summary
        ]);
    }

    /**
     * Get products expiring soon.
     */
    public function expiringSoon(Request $request): AnonymousResourceCollection
    {
        $days = $request->get('days', 30);

        $products = Product::with(['category', 'supplier'])
            ->whereHas('inventory', function ($query) use ($days) {
                $query->expiringSoon($days);
            })
            ->paginate($request->get('per_page', 15));

        return ProductResource::collection($products);
    }

    /**
     * Get expired products.
     */
    public function expired(Request $request): AnonymousResourceCollection
    {
        $products = Product::with(['category', 'supplier'])
            ->whereHas('inventory', function ($query) {
                $query->expired();
            })
            ->paginate($request->get('per_page', 15));

        return ProductResource::collection($products);
    }
}
