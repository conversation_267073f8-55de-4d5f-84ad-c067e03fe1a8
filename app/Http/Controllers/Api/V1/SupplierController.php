<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\StoreSupplierRequest;
use App\Http\Requests\Api\V1\UpdateSupplierRequest;
use App\Http\Resources\Api\V1\SupplierResource;
use App\Http\Resources\Api\V1\ProductResource;
use App\Models\Supplier;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class SupplierController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $query = Supplier::query();

        // Search functionality
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->search($search);
        }

        // Filter by active status
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Filter by payment terms
        if ($request->has('payment_terms')) {
            $query->byPaymentTerms($request->get('payment_terms'));
        }

        // Filter suppliers with outstanding balance
        if ($request->boolean('with_outstanding_balance')) {
            $query->withOutstandingBalance();
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        $suppliers = $query->paginate($request->get('per_page', 15));

        return SupplierResource::collection($suppliers);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSupplierRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            
            // Generate supplier code if not provided
            if (empty($data['code'])) {
                $data['code'] = Supplier::generateCode();
            }

            $supplier = Supplier::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Supplier created successfully',
                'data' => new SupplierResource($supplier)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create supplier',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Supplier $supplier): JsonResponse
    {
        $supplier->load(['products', 'purchaseOrders']);

        return response()->json([
            'success' => true,
            'data' => new SupplierResource($supplier)
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSupplierRequest $request, Supplier $supplier): JsonResponse
    {
        try {
            $supplier->update($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Supplier updated successfully',
                'data' => new SupplierResource($supplier)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update supplier',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Supplier $supplier): JsonResponse
    {
        try {
            // Check if supplier has products or purchase orders
            if ($supplier->products()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete supplier with existing products'
                ], 422);
            }

            if ($supplier->purchaseOrders()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete supplier with existing purchase orders'
                ], 422);
            }

            $supplier->delete();

            return response()->json([
                'success' => true,
                'message' => 'Supplier deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete supplier',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get products for a specific supplier.
     */
    public function products(Supplier $supplier, Request $request): AnonymousResourceCollection
    {
        $products = $supplier->products()
            ->with(['category', 'inventory'])
            ->active()
            ->paginate($request->get('per_page', 15));

        return ProductResource::collection($products);
    }

    /**
     * Get supplier performance metrics.
     */
    public function performance(Supplier $supplier): JsonResponse
    {
        try {
            $metrics = $supplier->getPerformanceMetrics();

            return response()->json([
                'success' => true,
                'data' => $metrics
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get supplier performance',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get suppliers with outstanding balances.
     */
    public function outstandingBalances(Request $request): AnonymousResourceCollection
    {
        $suppliers = Supplier::withOutstandingBalance()
            ->with(['purchaseOrders' => function ($query) {
                $query->where('payment_status', '!=', 'paid')
                      ->where('outstanding_amount', '>', 0);
            }])
            ->paginate($request->get('per_page', 15));

        return SupplierResource::collection($suppliers);
    }

    /**
     * Generate a unique supplier code.
     */
    public function generateCode(): JsonResponse
    {
        try {
            $code = Supplier::generateCode();

            return response()->json([
                'success' => true,
                'data' => ['code' => $code]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate supplier code',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get supplier statistics.
     */
    public function statistics(Supplier $supplier): JsonResponse
    {
        try {
            $stats = [
                'total_products' => $supplier->products()->count(),
                'active_products' => $supplier->products()->active()->count(),
                'total_purchase_orders' => $supplier->purchaseOrders()->count(),
                'completed_orders' => $supplier->purchaseOrders()->where('status', 'completed')->count(),
                'pending_orders' => $supplier->purchaseOrders()->where('status', 'pending')->count(),
                'total_purchase_amount' => $supplier->total_purchase_amount,
                'outstanding_balance' => $supplier->outstanding_balance,
                'available_credit' => $supplier->available_credit,
                'credit_limit' => $supplier->credit_limit,
                'credit_utilization' => $supplier->credit_limit > 0 ? 
                    ($supplier->outstanding_balance / $supplier->credit_limit) * 100 : 0,
                'payment_terms' => $supplier->payment_terms,
                'payment_terms_label' => $supplier->payment_terms_label,
                'primary_contact' => $supplier->primary_contact,
                'full_address' => $supplier->full_address,
                'is_active' => $supplier->is_active,
                'created_at' => $supplier->created_at,
                'updated_at' => $supplier->updated_at
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get supplier statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Compare prices across suppliers for a product.
     */
    public function priceComparison(Request $request): JsonResponse
    {
        $request->validate([
            'product_name' => 'required|string',
            'generic_name' => 'nullable|string'
        ]);

        try {
            $productName = $request->get('product_name');
            $genericName = $request->get('generic_name');

            $query = Supplier::with(['products' => function ($q) use ($productName, $genericName) {
                $q->where('name', 'like', "%{$productName}%");
                if ($genericName) {
                    $q->orWhere('generic_name', 'like', "%{$genericName}%");
                }
            }])->whereHas('products', function ($q) use ($productName, $genericName) {
                $q->where('name', 'like', "%{$productName}%");
                if ($genericName) {
                    $q->orWhere('generic_name', 'like', "%{$genericName}%");
                }
            });

            $suppliers = $query->get();

            $comparison = $suppliers->map(function ($supplier) {
                return [
                    'supplier_id' => $supplier->id,
                    'supplier_name' => $supplier->name,
                    'supplier_code' => $supplier->code,
                    'contact_person' => $supplier->contact_person,
                    'phone' => $supplier->phone,
                    'email' => $supplier->email,
                    'payment_terms' => $supplier->payment_terms_label,
                    'products' => $supplier->products->map(function ($product) {
                        return [
                            'id' => $product->id,
                            'name' => $product->name,
                            'generic_name' => $product->generic_name,
                            'sku' => $product->sku,
                            'unit_price' => $product->unit_price,
                            'selling_price' => $product->selling_price,
                            'mrp' => $product->mrp,
                            'strength' => $product->strength,
                            'dosage_form' => $product->dosage_form
                        ];
                    })
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $comparison
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get price comparison',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
