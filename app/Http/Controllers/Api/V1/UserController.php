<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateUserRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\Models\UserActivityLog;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class UserController extends Controller
{
    /**
     * Display a listing of users.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $request->validate([
            'search' => 'nullable|string|max:255',
            'role' => 'nullable|in:admin,pharmacist,cashier',
            'location_id' => 'nullable|exists:locations,id',
            'is_active' => 'nullable|boolean',
            'sort_by' => 'nullable|in:name,email,role,created_at,last_login_at',
            'sort_order' => 'nullable|in:asc,desc',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $query = User::with(['location']);

        // Search functionality
        if ($request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('employee_id', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Filter by role
        if ($request->role) {
            $query->byRole($request->role);
        }

        // Filter by location (non-admin users can only see users from their location)
        if (!$request->user()->isAdmin()) {
            $query->byLocation($request->user()->location_id);
        } elseif ($request->location_id) {
            $query->byLocation($request->location_id);
        }

        // Filter by active status
        if ($request->has('is_active')) {
            if ($request->boolean('is_active')) {
                $query->active();
            } else {
                $query->where('is_active', false);
            }
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        $users = $query->paginate($request->get('per_page', 15));

        return UserResource::collection($users);
    }

    /**
     * Store a newly created user.
     */
    public function store(CreateUserRequest $request): JsonResponse
    {

        try {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => $request->role,
                'phone' => $request->phone,
                'address' => $request->address,
                'employee_id' => $request->employee_id,
                'salary' => $request->salary,
                'hire_date' => $request->hire_date,
                'location_id' => $request->location_id,
                'emergency_contact_name' => $request->emergency_contact_name,
                'emergency_contact_phone' => $request->emergency_contact_phone,
                'notes' => $request->notes,
                'permissions' => $request->permissions ?? [],
                'is_active' => true,
                'must_change_password' => true,
                'password_changed_at' => now(),
            ]);

            // Log user creation
            UserActivityLog::log(
                UserActivityLog::ACTION_CREATE_USER,
                "Created new user: {$user->name} ({$user->email})",
                $request->user(),
                [
                    'created_user_id' => $user->id,
                    'created_user_role' => $user->role,
                    'created_user_email' => $user->email,
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'User created successfully',
                'data' => new UserResource($user->load('location'))
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create user',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified user.
     */
    public function show(User $user): JsonResponse
    {
        // Non-admin users can only view users from their location
        if (!request()->user()->isAdmin() && $user->location_id !== request()->user()->location_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to view this user'
            ], 403);
        }

        $user->load(['location', 'activityLogs' => function($query) {
            $query->latest()->limit(10);
        }]);

        return response()->json([
            'success' => true,
            'data' => new UserResource($user)
        ]);
    }

    /**
     * Update the specified user.
     */
    public function update(UpdateUserRequest $request, User $user): JsonResponse
    {
        try {
            $oldData = $user->only([
                'name', 'email', 'role', 'phone', 'address', 'employee_id',
                'salary', 'hire_date', 'location_id', 'is_active'
            ]);

            $user->update($request->validated());

            // Log role change if role was updated
            if ($oldData['role'] !== $user->role) {
                UserActivityLog::log(
                    UserActivityLog::ACTION_ROLE_CHANGE,
                    "Role changed from {$oldData['role']} to {$user->role}",
                    auth()->user(),
                    [
                        'target_user_id' => $user->id,
                        'target_user_email' => $user->email,
                        'old_role' => $oldData['role'],
                        'new_role' => $user->role,
                    ]
                );
            }

            // Log user update
            UserActivityLog::log(
                UserActivityLog::ACTION_UPDATE_USER,
                "Updated user: {$user->name}",
                auth()->user(),
                [
                    'updated_user_id' => $user->id,
                    'updated_user_email' => $user->email,
                    'changes' => array_diff_assoc($user->only(array_keys($oldData)), $oldData)
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'User updated successfully',
                'data' => new UserResource($user->load('location'))
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update user',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified user (soft delete).
     */
    public function destroy(Request $request, User $user): JsonResponse
    {
        // Prevent self-deletion
        if ($user->id === $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete your own account'
            ], 422);
        }

        // Non-admin users can only delete users from their location
        if (!$request->user()->isAdmin() && $user->location_id !== $request->user()->location_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to delete this user'
            ], 403);
        }

        try {
            // Revoke all user tokens
            $user->tokens()->delete();

            // Soft delete the user
            $user->delete();

            // Log user deletion
            UserActivityLog::log(
                UserActivityLog::ACTION_DELETE_USER,
                "Deleted user: {$user->name} ({$user->email})",
                $request->user(),
                [
                    'deleted_user_id' => $user->id,
                    'deleted_user_email' => $user->email,
                    'deleted_user_role' => $user->role,
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'User deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete user',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Activate a user account.
     */
    public function activate(Request $request, User $user): JsonResponse
    {
        if ($user->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'User account is already active'
            ], 422);
        }

        $user->update([
            'is_active' => true,
            'locked_until' => null,
            'failed_login_attempts' => 0,
        ]);

        // Log account activation
        UserActivityLog::log(
            UserActivityLog::ACTION_ACCOUNT_ACTIVATION,
            "Activated user account: {$user->name}",
            $request->user(),
            [
                'activated_user_id' => $user->id,
                'activated_user_email' => $user->email,
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'User account activated successfully',
            'data' => new UserResource($user->load('location'))
        ]);
    }

    /**
     * Deactivate a user account.
     */
    public function deactivate(Request $request, User $user): JsonResponse
    {
        // Prevent self-deactivation
        if ($user->id === $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot deactivate your own account'
            ], 422);
        }

        if (!$user->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'User account is already deactivated'
            ], 422);
        }

        // Revoke all user tokens
        $user->tokens()->delete();

        $user->update(['is_active' => false]);

        // Log account deactivation
        UserActivityLog::log(
            UserActivityLog::ACTION_ACCOUNT_DEACTIVATION,
            "Deactivated user account: {$user->name}",
            $request->user(),
            [
                'deactivated_user_id' => $user->id,
                'deactivated_user_email' => $user->email,
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'User account deactivated successfully',
            'data' => new UserResource($user->load('location'))
        ]);
    }

    /**
     * Unlock a user account.
     */
    public function unlock(Request $request, User $user): JsonResponse
    {
        if (!$user->isLocked()) {
            return response()->json([
                'success' => false,
                'message' => 'User account is not locked'
            ], 422);
        }

        $user->unlockAccount();

        // Log account unlock
        UserActivityLog::log(
            UserActivityLog::ACTION_ACCOUNT_UNLOCK,
            "Unlocked user account: {$user->name}",
            $request->user(),
            [
                'unlocked_user_id' => $user->id,
                'unlocked_user_email' => $user->email,
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'User account unlocked successfully',
            'data' => new UserResource($user->load('location'))
        ]);
    }

    /**
     * Get user activity logs.
     */
    public function activityLogs(Request $request, User $user): JsonResponse
    {
        // Non-admin users can only view activity logs for users from their location
        if (!$request->user()->isAdmin() && $user->location_id !== $request->user()->location_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to view this user\'s activity logs'
            ], 403);
        }

        $request->validate([
            'per_page' => 'integer|min:1|max:100',
            'action' => 'nullable|string',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        $query = $user->activityLogs()->with('location');

        // Filter by action
        if ($request->action) {
            $query->byAction($request->action);
        }

        // Filter by date range
        if ($request->date_from && $request->date_to) {
            $query->byDateRange($request->date_from, $request->date_to);
        }

        $activities = $query->latest()
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $activities
        ]);
    }
}
