{"version": 3, "file": "VLazy.js", "names": ["makeComponentProps", "makeDimensionProps", "useDimension", "useProxiedModel", "makeTagProps", "makeTransitionProps", "MaybeTransition", "vIntersect", "genericComponent", "propsFactory", "useRender", "makeVLazyProps", "modelValue", "Boolean", "options", "type", "Object", "default", "root", "undefined", "rootMargin", "threshold", "transition", "VLazy", "name", "directives", "props", "emits", "value", "setup", "_ref", "slots", "dimensionStyles", "isActive", "onIntersect", "isIntersecting", "_withDirectives", "_createVNode", "tag", "_normalizeClass", "class", "_normalizeStyle", "style", "handler"], "sources": ["../../../src/components/VLazy/VLazy.tsx"], "sourcesContent": ["// Composables\nimport { makeComponentProps } from '@/composables/component'\nimport { makeDimensionProps, useDimension } from '@/composables/dimensions'\nimport { useProxiedModel } from '@/composables/proxiedModel'\nimport { makeTagProps } from '@/composables/tag'\nimport { makeTransitionProps, MaybeTransition } from '@/composables/transition'\n\n// Directives\nimport vIntersect from '@/directives/intersect'\n\n// Utilities\nimport { genericComponent, propsFactory, useRender } from '@/util'\n\n// Types\nimport type { PropType } from 'vue'\n\nexport const makeVLazyProps = propsFactory({\n  modelValue: Boolean,\n  options: {\n    type: Object as PropType<IntersectionObserverInit>,\n    // For more information on types, navigate to:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API\n    default: () => ({\n      root: undefined,\n      rootMargin: undefined,\n      threshold: undefined,\n    }),\n  },\n\n  ...makeComponentProps(),\n  ...makeDimensionProps(),\n  ...makeTagProps(),\n  ...makeTransitionProps({ transition: 'fade-transition' }),\n}, 'VLazy')\n\nexport const VLazy = genericComponent()({\n  name: 'VLazy',\n\n  directives: { vIntersect },\n\n  props: makeVLazyProps(),\n\n  emits: {\n    'update:modelValue': (value: boolean) => true,\n  },\n\n  setup (props, { slots }) {\n    const { dimensionStyles } = useDimension(props)\n\n    const isActive = useProxiedModel(props, 'modelValue')\n\n    function onIntersect (isIntersecting: boolean) {\n      if (isActive.value) return\n\n      isActive.value = isIntersecting\n    }\n\n    useRender(() => (\n      <props.tag\n        class={[\n          'v-lazy',\n          props.class,\n        ]}\n        v-intersect={[\n          {\n            handler: onIntersect,\n            options: props.options,\n          },\n          null,\n          isActive.value ? [] : ['once'],\n        ]}\n        style={[\n          dimensionStyles.value,\n          props.style,\n        ]}\n      >\n        { isActive.value && (\n          <MaybeTransition transition={ props.transition } appear>\n            { slots.default?.() }\n          </MaybeTransition>\n        )}\n      </props.tag>\n    ))\n\n    return {}\n  },\n})\n\nexport type VLazy = InstanceType<typeof VLazy>\n"], "mappings": ";AAAA;AAAA,SACSA,kBAAkB;AAAA,SAClBC,kBAAkB,EAAEC,YAAY;AAAA,SAChCC,eAAe;AAAA,SACfC,YAAY;AAAA,SACZC,mBAAmB,EAAEC,eAAe,2CAE7C;AAAA,OACOC,UAAU,6CAEjB;AAAA,SACSC,gBAAgB,EAAEC,YAAY,EAAEC,SAAS,+BAElD;AAGA,OAAO,MAAMC,cAAc,GAAGF,YAAY,CAAC;EACzCG,UAAU,EAAEC,OAAO;EACnBC,OAAO,EAAE;IACPC,IAAI,EAAEC,MAA4C;IAClD;IACA;IACAC,OAAO,EAAEA,CAAA,MAAO;MACdC,IAAI,EAAEC,SAAS;MACfC,UAAU,EAAED,SAAS;MACrBE,SAAS,EAAEF;IACb,CAAC;EACH,CAAC;EAED,GAAGnB,kBAAkB,CAAC,CAAC;EACvB,GAAGC,kBAAkB,CAAC,CAAC;EACvB,GAAGG,YAAY,CAAC,CAAC;EACjB,GAAGC,mBAAmB,CAAC;IAAEiB,UAAU,EAAE;EAAkB,CAAC;AAC1D,CAAC,EAAE,OAAO,CAAC;AAEX,OAAO,MAAMC,KAAK,GAAGf,gBAAgB,CAAC,CAAC,CAAC;EACtCgB,IAAI,EAAE,OAAO;EAEbC,UAAU,EAAE;IAAElB;EAAW,CAAC;EAE1BmB,KAAK,EAAEf,cAAc,CAAC,CAAC;EAEvBgB,KAAK,EAAE;IACL,mBAAmB,EAAGC,KAAc,IAAK;EAC3C,CAAC;EAEDC,KAAKA,CAAEH,KAAK,EAAAI,IAAA,EAAa;IAAA,IAAX;MAAEC;IAAM,CAAC,GAAAD,IAAA;IACrB,MAAM;MAAEE;IAAgB,CAAC,GAAG9B,YAAY,CAACwB,KAAK,CAAC;IAE/C,MAAMO,QAAQ,GAAG9B,eAAe,CAACuB,KAAK,EAAE,YAAY,CAAC;IAErD,SAASQ,WAAWA,CAAEC,cAAuB,EAAE;MAC7C,IAAIF,QAAQ,CAACL,KAAK,EAAE;MAEpBK,QAAQ,CAACL,KAAK,GAAGO,cAAc;IACjC;IAEAzB,SAAS,CAAC,MAAA0B,eAAA,CAAAC,YAAA,CAAAX,KAAA,CAAAY,GAAA;MAAA,SAAAC,eAAA,CAEC,CACL,QAAQ,EACRb,KAAK,CAACc,KAAK,CACZ;MAAA,SAAAC,eAAA,CASM,CACLT,eAAe,CAACJ,KAAK,EACrBF,KAAK,CAACgB,KAAK,CACZ;IAAA;MAAAzB,OAAA,EAAAA,CAAA,MAECgB,QAAQ,CAACL,KAAK,IAAAS,YAAA,CAAA/B,eAAA;QAAA,cACgBoB,KAAK,CAACJ,UAAU;QAAA;MAAA;QAAAL,OAAA,EAAAA,CAAA,MAC1Cc,KAAK,CAACd,OAAO,GAAG,CAAC;MAAA,EAEtB;IAAA,MAAAV,UAAA,EAhBC;MACEoC,OAAO,EAAET,WAAW;MACpBpB,OAAO,EAAEY,KAAK,CAACZ;IACjB,CAAC,EACD,IAAI,GAcT,CAAC;IAEF,OAAO,CAAC,CAAC;EACX;AACF,CAAC,CAAC", "ignoreList": []}