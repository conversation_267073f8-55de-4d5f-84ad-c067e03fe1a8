{"version": 3, "file": "VLabel.js", "names": ["makeComponentProps", "makeThemeProps", "EventProp", "genericComponent", "propsFactory", "useRender", "makeVLabelProps", "text", "String", "onClick", "VLabel", "name", "props", "setup", "_ref", "slots", "_createElementVNode", "_normalizeClass", "class", "_normalizeStyle", "style", "default"], "sources": ["../../../src/components/VLabel/VLabel.tsx"], "sourcesContent": ["// Styles\nimport './VLabel.sass'\n\n// Composables\nimport { makeComponentProps } from '@/composables/component'\nimport { makeThemeProps } from '@/composables/theme'\n\n// Utilities\nimport { EventProp, genericComponent, propsFactory, useRender } from '@/util'\n\nexport const makeVLabelProps = propsFactory({\n  text: String,\n\n  onClick: EventProp<[MouseEvent]>(),\n\n  ...makeComponentProps(),\n  ...makeThemeProps(),\n}, 'VLabel')\n\nexport const VLabel = genericComponent()({\n  name: 'VLabel',\n\n  props: makeVLabelProps(),\n\n  setup (props, { slots }) {\n    useRender(() => (\n      <label\n        class={[\n          'v-label',\n          {\n            'v-label--clickable': !!props.onClick,\n          },\n          props.class,\n        ]}\n        style={ props.style }\n        onClick={ props.onClick }\n      >\n        { props.text }\n\n        { slots.default?.() }\n      </label>\n    ))\n\n    return {}\n  },\n})\n\nexport type VLabel = InstanceType<typeof VLabel>\n"], "mappings": ";AAAA;AACA;;AAEA;AAAA,SACSA,kBAAkB;AAAA,SAClBC,cAAc,sCAEvB;AAAA,SACSC,SAAS,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,SAAS;AAE7D,OAAO,MAAMC,eAAe,GAAGF,YAAY,CAAC;EAC1CG,IAAI,EAAEC,MAAM;EAEZC,OAAO,EAAEP,SAAS,CAAe,CAAC;EAElC,GAAGF,kBAAkB,CAAC,CAAC;EACvB,GAAGC,cAAc,CAAC;AACpB,CAAC,EAAE,QAAQ,CAAC;AAEZ,OAAO,MAAMS,MAAM,GAAGP,gBAAgB,CAAC,CAAC,CAAC;EACvCQ,IAAI,EAAE,QAAQ;EAEdC,KAAK,EAAEN,eAAe,CAAC,CAAC;EAExBO,KAAKA,CAAED,KAAK,EAAAE,IAAA,EAAa;IAAA,IAAX;MAAEC;IAAM,CAAC,GAAAD,IAAA;IACrBT,SAAS,CAAC,MAAAW,mBAAA;MAAA,SAAAC,eAAA,CAEC,CACL,SAAS,EACT;QACE,oBAAoB,EAAE,CAAC,CAACL,KAAK,CAACH;MAChC,CAAC,EACDG,KAAK,CAACM,KAAK,CACZ;MAAA,SAAAC,eAAA,CACOP,KAAK,CAACQ,KAAK;MAAA,WACTR,KAAK,CAACH;IAAO,IAErBG,KAAK,CAACL,IAAI,EAEVQ,KAAK,CAACM,OAAO,GAAG,CAAC,EAEtB,CAAC;IAEF,OAAO,CAAC,CAAC;EACX;AACF,CAAC,CAAC", "ignoreList": []}