{"version": 3, "file": "index.js", "names": ["createSimpleFunctional", "VKbd"], "sources": ["../../../src/components/VKbd/index.ts"], "sourcesContent": ["// Styles\nimport './VKbd.sass'\n\n// Utilities\nimport { createSimpleFunctional } from '@/util'\n\nexport const VKbd = createSimpleFunctional('v-kbd', 'kbd')\n\nexport type VKbd = InstanceType<typeof VKbd>\n"], "mappings": "AAAA;AACA;;AAEA;AAAA,SACSA,sBAAsB;AAE/B,OAAO,MAAMC,IAAI,GAAGD,sBAAsB,CAAC,OAAO,EAAE,KAAK,CAAC", "ignoreList": []}