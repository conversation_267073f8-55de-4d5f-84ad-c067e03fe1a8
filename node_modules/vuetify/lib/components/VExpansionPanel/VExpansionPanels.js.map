{"version": 3, "file": "VExpansionPanels.js", "names": ["VExpansionPanelSymbol", "makeVExpansionPanelProps", "makeComponentProps", "provideDefaults", "makeGroupProps", "useGroup", "makeTagProps", "makeThemeProps", "provideTheme", "toRef", "genericComponent", "pick", "propsFactory", "useRender", "allowedVariants", "makeVExpansionPanelsProps", "flat", "Boolean", "variant", "type", "String", "default", "validator", "v", "includes", "VExpansionPanels", "name", "props", "emits", "val", "setup", "_ref", "slots", "next", "prev", "themeClasses", "variantClass", "VExpansionPanel", "bgColor", "collapseIcon", "color", "eager", "elevation", "expandIcon", "focusable", "hideActions", "readonly", "ripple", "rounded", "static", "_createVNode", "tag", "_normalizeClass", "tile", "value", "class", "_normalizeStyle", "style"], "sources": ["../../../src/components/VExpansionPanel/VExpansionPanels.tsx"], "sourcesContent": ["// Styles\nimport './VExpansionPanel.sass'\n\n// Components\nimport { VExpansionPanelSymbol } from './shared'\nimport { makeVExpansionPanelProps } from './VExpansionPanel'\n\n// Composables\nimport { makeComponentProps } from '@/composables/component'\nimport { provideDefaults } from '@/composables/defaults'\nimport { makeGroupProps, useGroup } from '@/composables/group'\nimport { makeTagProps } from '@/composables/tag'\nimport { makeThemeProps, provideTheme } from '@/composables/theme'\n\n// Utilities\nimport { toRef } from 'vue'\nimport { genericComponent, pick, propsFactory, useRender } from '@/util'\n\n// Types\nimport type { PropType } from 'vue'\n\nconst allowedVariants = ['default', 'accordion', 'inset', 'popout'] as const\n\ntype Variant = typeof allowedVariants[number]\n\nexport type VExpansionPanelSlot = {\n  prev: () => void\n  next: () => void\n}\n\nexport type VExpansionPanelSlots = {\n  default: VExpansionPanelSlot\n}\n\nexport const makeVExpansionPanelsProps = propsFactory({\n  flat: Boolean,\n\n  ...makeGroupProps(),\n  ...pick(makeVExpansionPanelProps(), [\n    'bgColor',\n    'collapseIcon',\n    'color',\n    'eager',\n    'elevation',\n    'expandIcon',\n    'focusable',\n    'hideActions',\n    'readonly',\n    'ripple',\n    'rounded',\n    'tile',\n    'static',\n  ]),\n  ...makeThemeProps(),\n  ...makeComponentProps(),\n  ...makeTagProps(),\n\n  variant: {\n    type: String as PropType<Variant>,\n    default: 'default',\n    validator: (v: any) => allowedVariants.includes(v),\n  },\n}, 'VExpansionPanels')\n\nexport const VExpansionPanels = genericComponent<VExpansionPanelSlots>()({\n  name: 'VExpansionPanels',\n\n  props: makeVExpansionPanelsProps(),\n\n  emits: {\n    'update:modelValue': (val: unknown) => true,\n  },\n\n  setup (props, { slots }) {\n    const { next, prev } = useGroup(props, VExpansionPanelSymbol)\n\n    const { themeClasses } = provideTheme(props)\n\n    const variantClass = toRef(() => props.variant && `v-expansion-panels--variant-${props.variant}`)\n\n    provideDefaults({\n      VExpansionPanel: {\n        bgColor: toRef(() => props.bgColor),\n        collapseIcon: toRef(() => props.collapseIcon),\n        color: toRef(() => props.color),\n        eager: toRef(() => props.eager),\n        elevation: toRef(() => props.elevation),\n        expandIcon: toRef(() => props.expandIcon),\n        focusable: toRef(() => props.focusable),\n        hideActions: toRef(() => props.hideActions),\n        readonly: toRef(() => props.readonly),\n        ripple: toRef(() => props.ripple),\n        rounded: toRef(() => props.rounded),\n        static: toRef(() => props.static),\n      },\n    })\n\n    useRender(() => (\n      <props.tag\n        class={[\n          'v-expansion-panels',\n          {\n            'v-expansion-panels--flat': props.flat,\n            'v-expansion-panels--tile': props.tile,\n          },\n          themeClasses.value,\n          variantClass.value,\n          props.class,\n        ]}\n        style={ props.style }\n      >\n        { slots.default?.({ prev, next }) }\n      </props.tag>\n    ))\n\n    return {\n      next,\n      prev,\n    }\n  },\n})\n\nexport type VExpansionPanels = InstanceType<typeof VExpansionPanels>\n"], "mappings": ";AAAA;AACA;;AAEA;AAAA,SACSA,qBAAqB;AAAA,SACrBC,wBAAwB,gCAEjC;AAAA,SACSC,kBAAkB;AAAA,SAClBC,eAAe;AAAA,SACfC,cAAc,EAAEC,QAAQ;AAAA,SACxBC,YAAY;AAAA,SACZC,cAAc,EAAEC,YAAY,sCAErC;AACA,SAASC,KAAK,QAAQ,KAAK;AAAA,SAClBC,gBAAgB,EAAEC,IAAI,EAAEC,YAAY,EAAEC,SAAS,+BAExD;AAGA,MAAMC,eAAe,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAU;AAa5E,OAAO,MAAMC,yBAAyB,GAAGH,YAAY,CAAC;EACpDI,IAAI,EAAEC,OAAO;EAEb,GAAGb,cAAc,CAAC,CAAC;EACnB,GAAGO,IAAI,CAACV,wBAAwB,CAAC,CAAC,EAAE,CAClC,SAAS,EACT,cAAc,EACd,OAAO,EACP,OAAO,EACP,WAAW,EACX,YAAY,EACZ,WAAW,EACX,aAAa,EACb,UAAU,EACV,QAAQ,EACR,SAAS,EACT,MAAM,EACN,QAAQ,CACT,CAAC;EACF,GAAGM,cAAc,CAAC,CAAC;EACnB,GAAGL,kBAAkB,CAAC,CAAC;EACvB,GAAGI,YAAY,CAAC,CAAC;EAEjBY,OAAO,EAAE;IACPC,IAAI,EAAEC,MAA2B;IACjCC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAGC,CAAM,IAAKT,eAAe,CAACU,QAAQ,CAACD,CAAC;EACnD;AACF,CAAC,EAAE,kBAAkB,CAAC;AAEtB,OAAO,MAAME,gBAAgB,GAAGf,gBAAgB,CAAuB,CAAC,CAAC;EACvEgB,IAAI,EAAE,kBAAkB;EAExBC,KAAK,EAAEZ,yBAAyB,CAAC,CAAC;EAElCa,KAAK,EAAE;IACL,mBAAmB,EAAGC,GAAY,IAAK;EACzC,CAAC;EAEDC,KAAKA,CAAEH,KAAK,EAAAI,IAAA,EAAa;IAAA,IAAX;MAAEC;IAAM,CAAC,GAAAD,IAAA;IACrB,MAAM;MAAEE,IAAI;MAAEC;IAAK,CAAC,GAAG7B,QAAQ,CAACsB,KAAK,EAAE3B,qBAAqB,CAAC;IAE7D,MAAM;MAAEmC;IAAa,CAAC,GAAG3B,YAAY,CAACmB,KAAK,CAAC;IAE5C,MAAMS,YAAY,GAAG3B,KAAK,CAAC,MAAMkB,KAAK,CAACT,OAAO,IAAI,+BAA+BS,KAAK,CAACT,OAAO,EAAE,CAAC;IAEjGf,eAAe,CAAC;MACdkC,eAAe,EAAE;QACfC,OAAO,EAAE7B,KAAK,CAAC,MAAMkB,KAAK,CAACW,OAAO,CAAC;QACnCC,YAAY,EAAE9B,KAAK,CAAC,MAAMkB,KAAK,CAACY,YAAY,CAAC;QAC7CC,KAAK,EAAE/B,KAAK,CAAC,MAAMkB,KAAK,CAACa,KAAK,CAAC;QAC/BC,KAAK,EAAEhC,KAAK,CAAC,MAAMkB,KAAK,CAACc,KAAK,CAAC;QAC/BC,SAAS,EAAEjC,KAAK,CAAC,MAAMkB,KAAK,CAACe,SAAS,CAAC;QACvCC,UAAU,EAAElC,KAAK,CAAC,MAAMkB,KAAK,CAACgB,UAAU,CAAC;QACzCC,SAAS,EAAEnC,KAAK,CAAC,MAAMkB,KAAK,CAACiB,SAAS,CAAC;QACvCC,WAAW,EAAEpC,KAAK,CAAC,MAAMkB,KAAK,CAACkB,WAAW,CAAC;QAC3CC,QAAQ,EAAErC,KAAK,CAAC,MAAMkB,KAAK,CAACmB,QAAQ,CAAC;QACrCC,MAAM,EAAEtC,KAAK,CAAC,MAAMkB,KAAK,CAACoB,MAAM,CAAC;QACjCC,OAAO,EAAEvC,KAAK,CAAC,MAAMkB,KAAK,CAACqB,OAAO,CAAC;QACnCC,MAAM,EAAExC,KAAK,CAAC,MAAMkB,KAAK,CAACsB,MAAM;MAClC;IACF,CAAC,CAAC;IAEFpC,SAAS,CAAC,MAAAqC,YAAA,CAAAvB,KAAA,CAAAwB,GAAA;MAAA,SAAAC,eAAA,CAEC,CACL,oBAAoB,EACpB;QACE,0BAA0B,EAAEzB,KAAK,CAACX,IAAI;QACtC,0BAA0B,EAAEW,KAAK,CAAC0B;MACpC,CAAC,EACDlB,YAAY,CAACmB,KAAK,EAClBlB,YAAY,CAACkB,KAAK,EAClB3B,KAAK,CAAC4B,KAAK,CACZ;MAAA,SAAAC,eAAA,CACO7B,KAAK,CAAC8B,KAAK;IAAA;MAAApC,OAAA,EAAAA,CAAA,MAEjBW,KAAK,CAACX,OAAO,GAAG;QAAEa,IAAI;QAAED;MAAK,CAAC,CAAC;IAAA,EAEpC,CAAC;IAEF,OAAO;MACLA,IAAI;MACJC;IACF,CAAC;EACH;AACF,CAAC,CAAC", "ignoreList": []}