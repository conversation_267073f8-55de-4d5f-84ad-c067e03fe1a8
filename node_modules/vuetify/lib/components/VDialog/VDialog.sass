@use '../../styles/tools'
@use './variables' as *

@include tools.layer('components')
  .v-dialog
    align-items: center
    justify-content: center
    margin: auto

    > .v-overlay__content
      max-height: calc(100% - #{$dialog-margin * 2})
      width: calc(100% - #{$dialog-margin * 2})
      max-width: calc(100% - #{$dialog-margin * 2})
      margin: $dialog-margin

      &,
      > form
        display: flex
        flex-direction: column
        min-height: 0

        > .v-card,
        > .v-sheet
          --v-scrollbar-offset: 0px
          border-radius: $dialog-border-radius
          overflow-y: auto
          flex: 1 1 100%

          @include tools.elevation($dialog-elevation)

        > .v-card
          display: flex
          flex-direction: column

          > .v-card-item
            padding: $dialog-card-header-padding

            + .v-card-text
              padding-top: $dialog-card-header-text-padding-top

          > .v-card-text
            font-size: inherit
            letter-spacing: $dialog-card-text-letter-spacing
            line-height: inherit
            padding: $dialog-card-text-padding

          > .v-card-actions
            justify-content: $dialog-card-actions-justify

  .v-dialog--fullscreen
    --v-scrollbar-offset: 0px

    > .v-overlay__content
      border-radius: 0
      margin: 0
      padding: 0
      width: 100%
      height: 100%
      max-width: 100%
      max-height: 100%
      overflow-y: auto
      top: 0
      left: 0

      &,
      > form
        > .v-card,
        > .v-sheet
          min-height: 100%
          min-width: 100%
          border-radius: 0

  .v-dialog--scrollable > .v-overlay__content
    > form
      &,
      > .v-card
        max-height: 100%
        max-width: 100%

    &,
    > form
      &,
      > .v-card
        display: flex
        flex: 1 1 100%
        flex-direction: column

      > .v-card > .v-card-text
        backface-visibility: hidden
        overflow-y: auto
