{"version": 3, "file": "VDialog.js", "names": ["VDialogTransition", "VDefaultsProvider", "VOverlay", "makeVOverlayProps", "forwardRefs", "useProxiedModel", "useScopeId", "mergeProps", "nextTick", "onBeforeUnmount", "ref", "watch", "focusableC<PERSON><PERSON>n", "genericComponent", "IN_BROWSER", "propsFactory", "useRender", "makeVDialogProps", "fullscreen", "Boolean", "retainFocus", "type", "default", "scrollable", "origin", "scrollStrategy", "transition", "component", "zIndex", "VDialog", "name", "props", "emits", "value", "afterEnter", "afterLeave", "setup", "_ref", "emit", "slots", "isActive", "scopeId", "overlay", "onFocusin", "e", "before", "relatedTarget", "after", "target", "contentEl", "globalTop", "document", "includes", "contains", "focusable", "length", "firstElement", "lastElement", "focus", "removeEventListener", "val", "addEventListener", "immediate", "onAfterEnter", "scrim", "activeElement", "preventScroll", "onAfterLeave", "activatorEl", "overlayProps", "filterProps", "activatorProps", "contentProps", "tabindex", "_createVNode", "_mergeProps", "class", "style", "$event", "height", "undefined", "width", "maxHeight", "max<PERSON><PERSON><PERSON>", "activator", "_len", "arguments", "args", "Array", "_key"], "sources": ["../../../src/components/VDialog/VDialog.tsx"], "sourcesContent": ["// Styles\nimport './VDialog.sass'\n\n// Components\nimport { VDialogTransition } from '@/components/transitions'\nimport { VDefaultsProvider } from '@/components/VDefaultsProvider'\nimport { VOverlay } from '@/components/VOverlay'\nimport { makeVOverlayProps } from '@/components/VOverlay/VOverlay'\n\n// Composables\nimport { forwardRefs } from '@/composables/forwardRefs'\nimport { useProxiedModel } from '@/composables/proxiedModel'\nimport { useScopeId } from '@/composables/scopeId'\n\n// Utilities\nimport { mergeProps, nextTick, onBeforeUnmount, ref, watch } from 'vue'\nimport { focusableChildren, genericComponent, IN_BROWSER, propsFactory, useRender } from '@/util'\n\n// Types\nimport type { OverlaySlots } from '@/components/VOverlay/VOverlay'\n\nexport const makeVDialogProps = propsFactory({\n  fullscreen: Boolean,\n  retainFocus: {\n    type: Boolean,\n    default: true,\n  },\n  scrollable: Boolean,\n\n  ...makeVOverlayProps({\n    origin: 'center center' as const,\n    scrollStrategy: 'block' as const,\n    transition: { component: VDialogTransition },\n    zIndex: 2400,\n  }),\n}, 'VDialog')\n\nexport const VDialog = genericComponent<OverlaySlots>()({\n  name: 'VDialog',\n\n  props: makeVDialogProps(),\n\n  emits: {\n    'update:modelValue': (value: boolean) => true,\n    afterEnter: () => true,\n    afterLeave: () => true,\n  },\n\n  setup (props, { emit, slots }) {\n    const isActive = useProxiedModel(props, 'modelValue')\n    const { scopeId } = useScopeId()\n\n    const overlay = ref<VOverlay>()\n    function onFocusin (e: FocusEvent) {\n      const before = e.relatedTarget as HTMLElement | null\n      const after = e.target as HTMLElement | null\n\n      if (\n        before !== after &&\n        overlay.value?.contentEl &&\n        // We're the topmost dialog\n        overlay.value?.globalTop &&\n        // It isn't the document or the dialog body\n        ![document, overlay.value.contentEl].includes(after!) &&\n        // It isn't inside the dialog body\n        !overlay.value.contentEl.contains(after)\n      ) {\n        const focusable = focusableChildren(overlay.value.contentEl)\n\n        if (!focusable.length) return\n\n        const firstElement = focusable[0]\n        const lastElement = focusable[focusable.length - 1]\n\n        if (before === firstElement) {\n          lastElement.focus()\n        } else {\n          firstElement.focus()\n        }\n      }\n    }\n\n    onBeforeUnmount(() => {\n      document.removeEventListener('focusin', onFocusin)\n    })\n\n    if (IN_BROWSER) {\n      watch(() => isActive.value && props.retainFocus, val => {\n        val\n          ? document.addEventListener('focusin', onFocusin)\n          : document.removeEventListener('focusin', onFocusin)\n      }, { immediate: true })\n    }\n\n    function onAfterEnter () {\n      emit('afterEnter')\n      if (\n        (props.scrim || props.retainFocus) &&\n        overlay.value?.contentEl &&\n        !overlay.value.contentEl.contains(document.activeElement)\n      ) {\n        overlay.value.contentEl.focus({ preventScroll: true })\n      }\n    }\n\n    function onAfterLeave () {\n      emit('afterLeave')\n    }\n\n    watch(isActive, async val => {\n      if (!val) {\n        await nextTick()\n        overlay.value!.activatorEl?.focus({ preventScroll: true })\n      }\n    })\n\n    useRender(() => {\n      const overlayProps = VOverlay.filterProps(props)\n      const activatorProps = mergeProps({\n        'aria-haspopup': 'dialog',\n      }, props.activatorProps)\n      const contentProps = mergeProps({\n        tabindex: -1,\n      }, props.contentProps)\n\n      return (\n        <VOverlay\n          ref={ overlay }\n          class={[\n            'v-dialog',\n            {\n              'v-dialog--fullscreen': props.fullscreen,\n              'v-dialog--scrollable': props.scrollable,\n            },\n            props.class,\n          ]}\n          style={ props.style }\n          { ...overlayProps }\n          v-model={ isActive.value }\n          aria-modal=\"true\"\n          activatorProps={ activatorProps }\n          contentProps={ contentProps }\n          height={ !props.fullscreen ? props.height : undefined }\n          width={ !props.fullscreen ? props.width : undefined }\n          maxHeight={ !props.fullscreen ? props.maxHeight : undefined }\n          maxWidth={ !props.fullscreen ? props.maxWidth : undefined }\n          role=\"dialog\"\n          onAfterEnter={ onAfterEnter }\n          onAfterLeave={ onAfterLeave }\n          { ...scopeId }\n        >\n          {{\n            activator: slots.activator,\n            default: (...args) => (\n              <VDefaultsProvider root=\"VDialog\">\n                { slots.default?.(...args) }\n              </VDefaultsProvider>\n            ),\n          }}\n        </VOverlay>\n      )\n    })\n\n    return forwardRefs({}, overlay)\n  },\n})\n\nexport type VDialog = InstanceType<typeof VDialog>\n"], "mappings": ";AAAA;AACA;;AAEA;AAAA,SACSA,iBAAiB;AAAA,SACjBC,iBAAiB;AAAA,SACjBC,QAAQ;AAAA,SACRC,iBAAiB,mCAE1B;AAAA,SACSC,WAAW;AAAA,SACXC,eAAe;AAAA,SACfC,UAAU,wCAEnB;AACA,SAASC,UAAU,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,GAAG,EAAEC,KAAK,QAAQ,KAAK;AAAA,SAC9DC,iBAAiB,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,YAAY,EAAEC,SAAS,+BAEjF;AAGA,OAAO,MAAMC,gBAAgB,GAAGF,YAAY,CAAC;EAC3CG,UAAU,EAAEC,OAAO;EACnBC,WAAW,EAAE;IACXC,IAAI,EAAEF,OAAO;IACbG,OAAO,EAAE;EACX,CAAC;EACDC,UAAU,EAAEJ,OAAO;EAEnB,GAAGhB,iBAAiB,CAAC;IACnBqB,MAAM,EAAE,eAAwB;IAChCC,cAAc,EAAE,OAAgB;IAChCC,UAAU,EAAE;MAAEC,SAAS,EAAE3B;IAAkB,CAAC;IAC5C4B,MAAM,EAAE;EACV,CAAC;AACH,CAAC,EAAE,SAAS,CAAC;AAEb,OAAO,MAAMC,OAAO,GAAGhB,gBAAgB,CAAe,CAAC,CAAC;EACtDiB,IAAI,EAAE,SAAS;EAEfC,KAAK,EAAEd,gBAAgB,CAAC,CAAC;EAEzBe,KAAK,EAAE;IACL,mBAAmB,EAAGC,KAAc,IAAK,IAAI;IAC7CC,UAAU,EAAEA,CAAA,KAAM,IAAI;IACtBC,UAAU,EAAEA,CAAA,KAAM;EACpB,CAAC;EAEDC,KAAKA,CAAEL,KAAK,EAAAM,IAAA,EAAmB;IAAA,IAAjB;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAAF,IAAA;IAC3B,MAAMG,QAAQ,GAAGnC,eAAe,CAAC0B,KAAK,EAAE,YAAY,CAAC;IACrD,MAAM;MAAEU;IAAQ,CAAC,GAAGnC,UAAU,CAAC,CAAC;IAEhC,MAAMoC,OAAO,GAAGhC,GAAG,CAAW,CAAC;IAC/B,SAASiC,SAASA,CAAEC,CAAa,EAAE;MACjC,MAAMC,MAAM,GAAGD,CAAC,CAACE,aAAmC;MACpD,MAAMC,KAAK,GAAGH,CAAC,CAACI,MAA4B;MAE5C,IACEH,MAAM,KAAKE,KAAK,IAChBL,OAAO,CAACT,KAAK,EAAEgB,SAAS;MACxB;MACAP,OAAO,CAACT,KAAK,EAAEiB,SAAS;MACxB;MACA,CAAC,CAACC,QAAQ,EAAET,OAAO,CAACT,KAAK,CAACgB,SAAS,CAAC,CAACG,QAAQ,CAACL,KAAM,CAAC;MACrD;MACA,CAACL,OAAO,CAACT,KAAK,CAACgB,SAAS,CAACI,QAAQ,CAACN,KAAK,CAAC,EACxC;QACA,MAAMO,SAAS,GAAG1C,iBAAiB,CAAC8B,OAAO,CAACT,KAAK,CAACgB,SAAS,CAAC;QAE5D,IAAI,CAACK,SAAS,CAACC,MAAM,EAAE;QAEvB,MAAMC,YAAY,GAAGF,SAAS,CAAC,CAAC,CAAC;QACjC,MAAMG,WAAW,GAAGH,SAAS,CAACA,SAAS,CAACC,MAAM,GAAG,CAAC,CAAC;QAEnD,IAAIV,MAAM,KAAKW,YAAY,EAAE;UAC3BC,WAAW,CAACC,KAAK,CAAC,CAAC;QACrB,CAAC,MAAM;UACLF,YAAY,CAACE,KAAK,CAAC,CAAC;QACtB;MACF;IACF;IAEAjD,eAAe,CAAC,MAAM;MACpB0C,QAAQ,CAACQ,mBAAmB,CAAC,SAAS,EAAEhB,SAAS,CAAC;IACpD,CAAC,CAAC;IAEF,IAAI7B,UAAU,EAAE;MACdH,KAAK,CAAC,MAAM6B,QAAQ,CAACP,KAAK,IAAIF,KAAK,CAACX,WAAW,EAAEwC,GAAG,IAAI;QACtDA,GAAG,GACCT,QAAQ,CAACU,gBAAgB,CAAC,SAAS,EAAElB,SAAS,CAAC,GAC/CQ,QAAQ,CAACQ,mBAAmB,CAAC,SAAS,EAAEhB,SAAS,CAAC;MACxD,CAAC,EAAE;QAAEmB,SAAS,EAAE;MAAK,CAAC,CAAC;IACzB;IAEA,SAASC,YAAYA,CAAA,EAAI;MACvBzB,IAAI,CAAC,YAAY,CAAC;MAClB,IACE,CAACP,KAAK,CAACiC,KAAK,IAAIjC,KAAK,CAACX,WAAW,KACjCsB,OAAO,CAACT,KAAK,EAAEgB,SAAS,IACxB,CAACP,OAAO,CAACT,KAAK,CAACgB,SAAS,CAACI,QAAQ,CAACF,QAAQ,CAACc,aAAa,CAAC,EACzD;QACAvB,OAAO,CAACT,KAAK,CAACgB,SAAS,CAACS,KAAK,CAAC;UAAEQ,aAAa,EAAE;QAAK,CAAC,CAAC;MACxD;IACF;IAEA,SAASC,YAAYA,CAAA,EAAI;MACvB7B,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA3B,KAAK,CAAC6B,QAAQ,EAAE,MAAMoB,GAAG,IAAI;MAC3B,IAAI,CAACA,GAAG,EAAE;QACR,MAAMpD,QAAQ,CAAC,CAAC;QAChBkC,OAAO,CAACT,KAAK,CAAEmC,WAAW,EAAEV,KAAK,CAAC;UAAEQ,aAAa,EAAE;QAAK,CAAC,CAAC;MAC5D;IACF,CAAC,CAAC;IAEFlD,SAAS,CAAC,MAAM;MACd,MAAMqD,YAAY,GAAGnE,QAAQ,CAACoE,WAAW,CAACvC,KAAK,CAAC;MAChD,MAAMwC,cAAc,GAAGhE,UAAU,CAAC;QAChC,eAAe,EAAE;MACnB,CAAC,EAAEwB,KAAK,CAACwC,cAAc,CAAC;MACxB,MAAMC,YAAY,GAAGjE,UAAU,CAAC;QAC9BkE,QAAQ,EAAE,CAAC;MACb,CAAC,EAAE1C,KAAK,CAACyC,YAAY,CAAC;MAEtB,OAAAE,YAAA,CAAAxE,QAAA,EAAAyE,WAAA;QAAA,OAEUjC,OAAO;QAAA,SACN,CACL,UAAU,EACV;UACE,sBAAsB,EAAEX,KAAK,CAACb,UAAU;UACxC,sBAAsB,EAAEa,KAAK,CAACR;QAChC,CAAC,EACDQ,KAAK,CAAC6C,KAAK,CACZ;QAAA,SACO7C,KAAK,CAAC8C;MAAK,GACdR,YAAY;QAAA,cACP7B,QAAQ,CAACP,KAAK;QAAA,uBAAA6C,MAAA,IAAdtC,QAAQ,CAACP,KAAK,GAAA6C,MAAA;QAAA;QAAA,kBAEPP,cAAc;QAAA,gBAChBC,YAAY;QAAA,UAClB,CAACzC,KAAK,CAACb,UAAU,GAAGa,KAAK,CAACgD,MAAM,GAAGC,SAAS;QAAA,SAC7C,CAACjD,KAAK,CAACb,UAAU,GAAGa,KAAK,CAACkD,KAAK,GAAGD,SAAS;QAAA,aACvC,CAACjD,KAAK,CAACb,UAAU,GAAGa,KAAK,CAACmD,SAAS,GAAGF,SAAS;QAAA,YAChD,CAACjD,KAAK,CAACb,UAAU,GAAGa,KAAK,CAACoD,QAAQ,GAAGH,SAAS;QAAA;QAAA,gBAE1CjB,YAAY;QAAA,gBACZI;MAAY,GACtB1B,OAAO;QAGV2C,SAAS,EAAE7C,KAAK,CAAC6C,SAAS;QAC1B9D,OAAO,EAAE,SAAAA,CAAA;UAAA,SAAA+D,IAAA,GAAAC,SAAA,CAAA/B,MAAA,EAAIgC,IAAI,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;YAAJF,IAAI,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;UAAA;UAAA,OAAAf,YAAA,CAAAzE,iBAAA;YAAA;UAAA;YAAAqB,OAAA,EAAAA,CAAA,MAEXiB,KAAK,CAACjB,OAAO,GAAG,GAAGiE,IAAI,CAAC;UAAA;QAAA;MAE7B;IAIT,CAAC,CAAC;IAEF,OAAOnF,WAAW,CAAC,CAAC,CAAC,EAAEsC,OAAO,CAAC;EACjC;AACF,CAAC,CAAC", "ignoreList": []}