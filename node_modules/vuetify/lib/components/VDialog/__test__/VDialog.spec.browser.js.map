{"version": 3, "file": "VDialog.spec.browser.js", "names": ["VDialog", "render", "screen", "userEvent", "nextTick", "ref", "describe", "it", "model", "element", "_createElementVNode", "_createVNode", "value", "$event", "default", "_createTextVNode", "expect", "queryByTestId", "toBeNull", "findByTestId", "resolves", "toBeVisible", "click", "poll", "toBeFalsy", "onAfterLeave", "vi", "fn", "toHaveBeenCalledTimes"], "sources": ["../../../../src/components/VDialog/__test__/VDialog.spec.browser.tsx"], "sourcesContent": ["// Components\nimport { VDialog } from '../VDialog'\n\n// Utilities\nimport { render, screen, userEvent } from '@test'\nimport { nextTick, ref } from 'vue'\n\n// Tests\ndescribe('VDialog', () => {\n  it('should render correctly', async () => {\n    const model = ref(false)\n    const { element } = render(() => (\n      <div>\n        <VDialog v-model={ model.value } data-testid=\"dialog\">\n          <div data-testid=\"content\">Content</div>\n        </VDialog>\n      </div>\n    ))\n\n    expect(screen.queryByTestId('dialog')).toBeNull()\n\n    model.value = true\n    await nextTick()\n    await expect(screen.findByTestId('dialog')).resolves.toBeVisible()\n    await expect.element(await screen.findByTestId('content')).toBeVisible()\n\n    await userEvent.click(element)\n    await expect.poll(() => model.value).toBeFalsy()\n    await expect.poll(() => screen.queryByTestId('dialog')).toBeNull()\n    await expect.poll(() => screen.queryByTestId('content')).toBeNull()\n  })\n\n  it('should emit afterLeave', async () => {\n    const model = ref(true)\n    const onAfterLeave = vi.fn()\n    const { element } = render(() => (\n      <div>\n        <VDialog v-model={ model.value } onAfterLeave={ onAfterLeave }>\n          <div data-test=\"content\">Content</div>\n        </VDialog>\n      </div>\n    ))\n\n    await userEvent.click(element)\n    await expect.poll(() => onAfterLeave).toHaveBeenCalledTimes(1)\n  })\n})\n"], "mappings": ";AAAA;AAAA,SACSA,OAAO,yBAEhB;AACA,SAASC,MAAM,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACjD,SAASC,QAAQ,EAAEC,GAAG,QAAQ,KAAK;;AAEnC;AACAC,QAAQ,CAAC,SAAS,EAAE,MAAM;EACxBC,EAAE,CAAC,yBAAyB,EAAE,YAAY;IACxC,MAAMC,KAAK,GAAGH,GAAG,CAAC,KAAK,CAAC;IACxB,MAAM;MAAEI;IAAQ,CAAC,GAAGR,MAAM,CAAC,MAAAS,mBAAA,eAAAC,YAAA,CAAAX,OAAA;MAAA,cAEJQ,KAAK,CAACI,KAAK;MAAA,uBAAAC,MAAA,IAAXL,KAAK,CAACI,KAAK,GAAAC,MAAA;MAAA;IAAA;MAAAC,OAAA,EAAAA,CAAA,MAAAJ,mBAAA;QAAA;MAAA,IAAAK,gBAAA;IAAA,IAIjC,CAAC;IAEFC,MAAM,CAACd,MAAM,CAACe,aAAa,CAAC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;IAEjDV,KAAK,CAACI,KAAK,GAAG,IAAI;IAClB,MAAMR,QAAQ,CAAC,CAAC;IAChB,MAAMY,MAAM,CAACd,MAAM,CAACiB,YAAY,CAAC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAACC,WAAW,CAAC,CAAC;IAClE,MAAML,MAAM,CAACP,OAAO,CAAC,MAAMP,MAAM,CAACiB,YAAY,CAAC,SAAS,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;IAExE,MAAMlB,SAAS,CAACmB,KAAK,CAACb,OAAO,CAAC;IAC9B,MAAMO,MAAM,CAACO,IAAI,CAAC,MAAMf,KAAK,CAACI,KAAK,CAAC,CAACY,SAAS,CAAC,CAAC;IAChD,MAAMR,MAAM,CAACO,IAAI,CAAC,MAAMrB,MAAM,CAACe,aAAa,CAAC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;IAClE,MAAMF,MAAM,CAACO,IAAI,CAAC,MAAMrB,MAAM,CAACe,aAAa,CAAC,SAAS,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;EACrE,CAAC,CAAC;EAEFX,EAAE,CAAC,wBAAwB,EAAE,YAAY;IACvC,MAAMC,KAAK,GAAGH,GAAG,CAAC,IAAI,CAAC;IACvB,MAAMoB,YAAY,GAAGC,EAAE,CAACC,EAAE,CAAC,CAAC;IAC5B,MAAM;MAAElB;IAAQ,CAAC,GAAGR,MAAM,CAAC,MAAAS,mBAAA,eAAAC,YAAA,CAAAX,OAAA;MAAA,cAEJQ,KAAK,CAACI,KAAK;MAAA,uBAAAC,MAAA,IAAXL,KAAK,CAACI,KAAK,GAAAC,MAAA;MAAA,gBAAkBY;IAAY;MAAAX,OAAA,EAAAA,CAAA,MAAAJ,mBAAA;QAAA;MAAA,IAAAK,gBAAA;IAAA,IAI/D,CAAC;IAEF,MAAMZ,SAAS,CAACmB,KAAK,CAACb,OAAO,CAAC;IAC9B,MAAMO,MAAM,CAACO,IAAI,CAAC,MAAME,YAAY,CAAC,CAACG,qBAAqB,CAAC,CAAC,CAAC;EAChE,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}