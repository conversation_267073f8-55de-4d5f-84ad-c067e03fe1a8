@use 'sass:map';
@use '../../styles/settings';
@use '../../styles/tools';

// VList
$list-background: rgba(var(--v-theme-surface)) !default;
$list-border-color: settings.$border-color-root !default;
$list-border-radius: 0 !default;
$list-border-style: settings.$border-style-root !default;
$list-border-thin-width: thin !default;
$list-border-width: 0 !default;
$list-color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity)) !default;
$list-disabled-opacity: 0.6 !default;
$list-elevation: 0 !default;
$list-padding: 8px 0 !default;
$list-rounded-border-radius: map.get(settings.$rounded, null) !default;
$list-indent-size: 16px !default;

$list-nav-padding: 8px !default;
$list-nav-subheader-font-size: .75rem !default;

// VListSubheader
$list-subheader-color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity)) !default;
$list-subheader-font-size: .875rem !default;
$list-subheader-font-weight: 400 !default;
$list-subheader-inset-padding-start: 56px !default;
$list-subheader-line-height: 1.375rem !default;
$list-subheader-min-height: 40px !default;
$list-subheader-padding-end: 16px !default;
$list-subheader-padding-top: 0 !default;
$list-subheader-min-height-multiplier: 1 !default;
$list-subheader-transition: 0.2s min-height settings.$standard-easing !default;

// VListItem
$list-item-border-color: settings.$border-color-root !default;
$list-item-border-radius: 0 !default;
$list-item-border-style: settings.$border-style-root !default;
$list-item-border-width: 0 !default;
$list-item-border-thin-width: thin !default;
$list-item-content-min-width: 40px !default;
$list-item-elevation: 1 !default;
$list-item-icon-opacity: var(--v-medium-emphasis-opacity) !default;
$list-item-icon-active-opacity: 1 !default;
$list-item-min-height: 40px !default;
$list-item-padding: 4px 16px !default;
$list-item-prepend-size: 40px !default;
$list-item-slim-prepend-size: 28px !default;
$list-item-plain-opacity: .62 !default;
$list-item-rounded-border-radius: map.get(settings.$rounded, null) !default;
$list-item-one-line-min-height: 48px !default;
$list-item-two-line-min-height: 64px !default;
$list-item-two-line-padding: 12px 16px !default;
$list-item-three-line-min-height: 88px !default;
$list-item-three-line-padding: 16px 16px !default;

$list-item-action-spacer-width: 16px !default;
$list-item-slim-action-spacer-width: 4px !default;
$list-item-avatar-margin-end: 16px !default;
$list-item-avatar-margin-start: 16px !default;
$list-item-slim-spacer-width: 20px !default;
$list-item-slim-avatar-spacer-width: 4px !default;

$list-item-action-margin-end: 8px !default;
$list-item-action-margin-start: 8px !default;

$list-item-icon-margin-end: 32px !default;
$list-item-icon-margin-start: 32px !default;

$list-item-media-margin-bottom: 0 !default;
$list-item-media-margin-end: 16px !default;
$list-item-media-margin-start: 16px !default;
$list-item-media-margin-top: 0 !default;
$list-item-media-two-line-margin-bottom: -4px !default;
$list-item-media-two-line-margin-top: -4px !default;
$list-item-media-three-line-margin-bottom: 0 !default;
$list-item-media-three-line-margin-top: 0 !default;

$list-item-nav-margin-top: 4px !default;
$list-item-nav-title-font-size: .8125rem !default;
$list-item-nav-title-font-weight: 500 !default;
$list-item-nav-title-letter-spacing: normal !default;
$list-item-nav-title-line-height: 1rem !default;
$list-item-nav-subtitle-font-size: .75rem !default;
$list-item-nav-subtitle-font-weight: tools.map-deep-get(settings.$typography, 'body-2', 'weight') !default;
$list-item-nav-subtitle-letter-spacing: tools.map-deep-get(settings.$typography, 'body-2', 'letter-spacing') !default;
$list-item-nav-subtitle-line-height: 1rem !default;

$list-item-subtitle-opacity: var(--v-list-item-subtitle-opacity, var(--v-medium-emphasis-opacity)) !default;
$list-item-subtitle-font-size: tools.map-deep-get(settings.$typography, 'body-2', 'size') !default;
$list-item-subtitle-font-weight: tools.map-deep-get(settings.$typography, 'body-2', 'weight') !default;
$list-item-subtitle-letter-spacing: tools.map-deep-get(settings.$typography, 'body-2', 'letter-spacing') !default;
$list-item-subtitle-line-height: 1rem !default;
$list-item-subtitle-padding: 0 !default;
$list-item-subtitle-text-transform: none !default;
$list-item-subtitle-overflow-wrap: break-word !default;
$list-item-subtitle-word-break: initial !default;

$list-item-title-font-size: tools.map-deep-get(settings.$typography, 'body-1', 'size') !default;
$list-item-title-font-weight: tools.map-deep-get(settings.$typography, 'body-1', 'weight') !default;
$list-item-title-hyphens: auto !default;
$list-item-title-letter-spacing: tools.map-deep-get(settings.$typography, 'subtitle-1', 'letter-spacing') !default;
$list-item-title-line-height: tools.map-deep-get(settings.$typography, 'body-1', 'line-height') !default;
$list-item-title-overflow-wrap: normal !default;
$list-item-title-padding: 0 !default;
$list-item-title-text-transform: none !default;
$list-item-title-word-break: normal !default;
$list-item-title-word-wrap: break-word !default;

$list-density: ('default': 0, 'comfortable': -1, 'compact': -2) !default;

$list-border: (
  $list-border-color,
  $list-border-style,
  $list-border-width,
  $list-border-thin-width
) !default;

$list-theme: (
  $list-background,
  $list-color
) !default;

$list-item-border: (
  $list-item-border-color,
  $list-item-border-style,
  $list-item-border-width,
  $list-item-border-thin-width
) !default;

$list-item-title-typography: (
  $list-item-title-font-size,
  $list-item-title-font-weight,
  $list-item-title-letter-spacing,
  $list-item-title-line-height,
  $list-item-title-text-transform
) !default;

$list-item-subtitle-typography: (
  $list-item-subtitle-font-size,
  $list-item-subtitle-font-weight,
  $list-item-subtitle-letter-spacing,
  $list-item-subtitle-line-height,
  $list-item-subtitle-text-transform
) !default;

$list-item-nav-title-typography: (
  $list-item-nav-title-font-size,
  $list-item-nav-title-font-weight,
  $list-item-nav-title-letter-spacing,
  $list-item-nav-title-line-height,
  null
) !default;

$list-item-nav-subtitle-typography: (
  $list-item-nav-subtitle-font-size,
  $list-item-nav-subtitle-font-weight,
  $list-item-nav-subtitle-letter-spacing,
  $list-item-nav-subtitle-line-height,
  null
) !default;

$list-item-variants: (
  $list-background,
  $list-color,
  $list-item-elevation,
  $list-item-plain-opacity,
  'v-list-item'
) !default;
