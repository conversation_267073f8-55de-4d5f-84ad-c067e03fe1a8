{"version": 3, "file": "list.js", "names": ["computed", "inject", "provide", "shallowRef", "<PERSON><PERSON>h<PERSON><PERSON>", "Symbol", "for", "useDepth", "hasPrepend", "parent", "depth", "value", "List<PERSON>ey", "createList", "updateHasPrepend", "data", "useList"], "sources": ["../../../src/components/VList/list.ts"], "sourcesContent": ["// Utilities\nimport { computed, inject, provide, shallowRef } from 'vue'\n\n// Types\nimport type { InjectionKey, Ref } from 'vue'\n\n// Depth\nexport const DepthKey: InjectionKey<Ref<number>> = Symbol.for('vuetify:depth')\n\nexport function useDepth (hasPrepend?: Ref<boolean>) {\n  const parent = inject(DepthKey, shallowRef(-1))\n\n  const depth = computed(() => parent.value + 1 + (hasPrepend?.value ? 1 : 0))\n\n  provide(DepthKey, depth)\n\n  return depth\n}\n\n// List\nexport const ListKey: InjectionKey<{\n  hasPrepend: Ref<boolean>\n  updateHasPrepend: (value: boolean) => void\n}> = Symbol.for('vuetify:list')\n\nexport function createList () {\n  const parent = inject(ListKey, { hasPrepend: shallowRef(false), updateHasPrepend: () => null })\n\n  const data = {\n    hasPrepend: shallowRef(false),\n    updateHasPrepend: (value: boolean) => {\n      if (value) data.hasPrepend.value = value\n    },\n  }\n\n  provide(<PERSON>K<PERSON>, data)\n\n  return parent\n}\n\nexport function useList () {\n  return inject(ListKey, null)\n}\n"], "mappings": "AAAA;AACA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,QAAQ,KAAK;;AAE3D;;AAGA;AACA,OAAO,MAAMC,QAAmC,GAAGC,MAAM,CAACC,GAAG,CAAC,eAAe,CAAC;AAE9E,OAAO,SAASC,QAAQA,CAAEC,UAAyB,EAAE;EACnD,MAAMC,MAAM,GAAGR,MAAM,CAACG,QAAQ,EAAED,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;EAE/C,MAAMO,KAAK,GAAGV,QAAQ,CAAC,MAAMS,MAAM,CAACE,KAAK,GAAG,CAAC,IAAIH,UAAU,EAAEG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EAE5ET,OAAO,CAACE,QAAQ,EAAEM,KAAK,CAAC;EAExB,OAAOA,KAAK;AACd;;AAEA;AACA,OAAO,MAAME,OAGX,GAAGP,MAAM,CAACC,GAAG,CAAC,cAAc,CAAC;AAE/B,OAAO,SAASO,UAAUA,CAAA,EAAI;EAC5B,MAAMJ,MAAM,GAAGR,MAAM,CAACW,OAAO,EAAE;IAAEJ,UAAU,EAAEL,UAAU,CAAC,KAAK,CAAC;IAAEW,gBAAgB,EAAEA,CAAA,KAAM;EAAK,CAAC,CAAC;EAE/F,MAAMC,IAAI,GAAG;IACXP,UAAU,EAAEL,UAAU,CAAC,KAAK,CAAC;IAC7BW,gBAAgB,EAAGH,KAAc,IAAK;MACpC,IAAIA,KAAK,EAAEI,IAAI,CAACP,UAAU,CAACG,KAAK,GAAGA,KAAK;IAC1C;EACF,CAAC;EAEDT,OAAO,CAACU,OAAO,EAAEG,IAAI,CAAC;EAEtB,OAAON,MAAM;AACf;AAEA,OAAO,SAASO,OAAOA,CAAA,EAAI;EACzB,OAAOf,MAAM,CAACW,OAAO,EAAE,IAAI,CAAC;AAC9B", "ignoreList": []}