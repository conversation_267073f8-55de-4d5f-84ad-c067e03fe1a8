{"version": 3, "file": "VDivider.js", "names": ["useTextColor", "makeComponentProps", "makeThemeProps", "provideTheme", "computed", "convertToUnit", "genericComponent", "propsFactory", "useRender", "makeVDividerProps", "color", "String", "inset", "Boolean", "length", "Number", "opacity", "thickness", "vertical", "VDivider", "name", "props", "setup", "_ref", "attrs", "slots", "themeClasses", "textColorClasses", "textColorStyles", "dividerStyles", "styles", "divider", "_createElementVNode", "_normalizeClass", "value", "class", "_normalizeStyle", "style", "role", "undefined", "default"], "sources": ["../../../src/components/VDivider/VDivider.tsx"], "sourcesContent": ["// Styles\nimport './VDivider.sass'\n\n// Composables\nimport { useTextColor } from '@/composables/color'\nimport { makeComponentProps } from '@/composables/component'\nimport { makeThemeProps, provideTheme } from '@/composables/theme'\n\n// Utilities\nimport { computed } from 'vue'\nimport { convertToUnit, genericComponent, propsFactory, useRender } from '@/util'\n\ntype DividerKey = 'borderRightWidth' | 'borderTopWidth' | 'height' | 'width'\ntype DividerStyles = Partial<Record<DividerKey, string>>\n\nexport const makeVDividerProps = propsFactory({\n  color: String,\n  inset: Boolean,\n  length: [Number, String],\n  opacity: [Number, String],\n  thickness: [Number, String],\n  vertical: Boolean,\n\n  ...makeComponentProps(),\n  ...makeThemeProps(),\n}, 'VDivider')\n\nexport const VDivider = genericComponent()({\n  name: 'VDivider',\n\n  props: makeVDividerProps(),\n\n  setup (props, { attrs, slots }) {\n    const { themeClasses } = provideTheme(props)\n    const { textColorClasses, textColorStyles } = useTextColor(() => props.color)\n    const dividerStyles = computed(() => {\n      const styles: DividerStyles = {}\n\n      if (props.length) {\n        styles[props.vertical ? 'height' : 'width'] = convertToUnit(props.length)\n      }\n\n      if (props.thickness) {\n        styles[props.vertical ? 'borderRightWidth' : 'borderTopWidth'] = convertToUnit(props.thickness)\n      }\n\n      return styles\n    })\n\n    useRender(() => {\n      const divider = (\n        <hr\n          class={[\n            {\n              'v-divider': true,\n              'v-divider--inset': props.inset,\n              'v-divider--vertical': props.vertical,\n            },\n            themeClasses.value,\n            textColorClasses.value,\n            props.class,\n          ]}\n          style={[\n            dividerStyles.value,\n            textColorStyles.value,\n            { '--v-border-opacity': props.opacity },\n            props.style,\n          ]}\n          aria-orientation={\n            !attrs.role || attrs.role === 'separator'\n              ? props.vertical ? 'vertical' : 'horizontal'\n              : undefined\n          }\n          role={ `${attrs.role || 'separator'}` }\n        />\n      )\n\n      if (!slots.default) return divider\n\n      return (\n        <div\n          class={[\n            'v-divider__wrapper',\n            {\n              'v-divider__wrapper--vertical': props.vertical,\n              'v-divider__wrapper--inset': props.inset,\n            },\n          ]}\n        >\n          { divider }\n\n          <div class=\"v-divider__content\">\n            { slots.default() }\n          </div>\n\n          { divider }\n        </div>\n      )\n    })\n\n    return {}\n  },\n})\n\nexport type VDivider = InstanceType<typeof VDivider>\n"], "mappings": ";AAAA;AACA;;AAEA;AAAA,SACSA,YAAY;AAAA,SACZC,kBAAkB;AAAA,SAClBC,cAAc,EAAEC,YAAY,sCAErC;AACA,SAASC,QAAQ,QAAQ,KAAK;AAAA,SACrBC,aAAa,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,SAAS;AAKjE,OAAO,MAAMC,iBAAiB,GAAGF,YAAY,CAAC;EAC5CG,KAAK,EAAEC,MAAM;EACbC,KAAK,EAAEC,OAAO;EACdC,MAAM,EAAE,CAACC,MAAM,EAAEJ,MAAM,CAAC;EACxBK,OAAO,EAAE,CAACD,MAAM,EAAEJ,MAAM,CAAC;EACzBM,SAAS,EAAE,CAACF,MAAM,EAAEJ,MAAM,CAAC;EAC3BO,QAAQ,EAAEL,OAAO;EAEjB,GAAGZ,kBAAkB,CAAC,CAAC;EACvB,GAAGC,cAAc,CAAC;AACpB,CAAC,EAAE,UAAU,CAAC;AAEd,OAAO,MAAMiB,QAAQ,GAAGb,gBAAgB,CAAC,CAAC,CAAC;EACzCc,IAAI,EAAE,UAAU;EAEhBC,KAAK,EAAEZ,iBAAiB,CAAC,CAAC;EAE1Ba,KAAKA,CAAED,KAAK,EAAAE,IAAA,EAAoB;IAAA,IAAlB;MAAEC,KAAK;MAAEC;IAAM,CAAC,GAAAF,IAAA;IAC5B,MAAM;MAAEG;IAAa,CAAC,GAAGvB,YAAY,CAACkB,KAAK,CAAC;IAC5C,MAAM;MAAEM,gBAAgB;MAAEC;IAAgB,CAAC,GAAG5B,YAAY,CAAC,MAAMqB,KAAK,CAACX,KAAK,CAAC;IAC7E,MAAMmB,aAAa,GAAGzB,QAAQ,CAAC,MAAM;MACnC,MAAM0B,MAAqB,GAAG,CAAC,CAAC;MAEhC,IAAIT,KAAK,CAACP,MAAM,EAAE;QAChBgB,MAAM,CAACT,KAAK,CAACH,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC,GAAGb,aAAa,CAACgB,KAAK,CAACP,MAAM,CAAC;MAC3E;MAEA,IAAIO,KAAK,CAACJ,SAAS,EAAE;QACnBa,MAAM,CAACT,KAAK,CAACH,QAAQ,GAAG,kBAAkB,GAAG,gBAAgB,CAAC,GAAGb,aAAa,CAACgB,KAAK,CAACJ,SAAS,CAAC;MACjG;MAEA,OAAOa,MAAM;IACf,CAAC,CAAC;IAEFtB,SAAS,CAAC,MAAM;MACd,MAAMuB,OAAO,GAAAC,mBAAA;QAAA,SAAAC,eAAA,CAEF,CACL;UACE,WAAW,EAAE,IAAI;UACjB,kBAAkB,EAAEZ,KAAK,CAACT,KAAK;UAC/B,qBAAqB,EAAES,KAAK,CAACH;QAC/B,CAAC,EACDQ,YAAY,CAACQ,KAAK,EAClBP,gBAAgB,CAACO,KAAK,EACtBb,KAAK,CAACc,KAAK,CACZ;QAAA,SAAAC,eAAA,CACM,CACLP,aAAa,CAACK,KAAK,EACnBN,eAAe,CAACM,KAAK,EACrB;UAAE,oBAAoB,EAAEb,KAAK,CAACL;QAAQ,CAAC,EACvCK,KAAK,CAACgB,KAAK,CACZ;QAAA,oBAEC,CAACb,KAAK,CAACc,IAAI,IAAId,KAAK,CAACc,IAAI,KAAK,WAAW,GACrCjB,KAAK,CAACH,QAAQ,GAAG,UAAU,GAAG,YAAY,GAC1CqB,SAAS;QAAA,QAER,GAAGf,KAAK,CAACc,IAAI,IAAI,WAAW;MAAE,QAExC;MAED,IAAI,CAACb,KAAK,CAACe,OAAO,EAAE,OAAOT,OAAO;MAElC,OAAAC,mBAAA;QAAA,SAAAC,eAAA,CAEW,CACL,oBAAoB,EACpB;UACE,8BAA8B,EAAEZ,KAAK,CAACH,QAAQ;UAC9C,2BAA2B,EAAEG,KAAK,CAACT;QACrC,CAAC,CACF;MAAA,IAECmB,OAAO,EAAAC,mBAAA;QAAA;MAAA,IAGLP,KAAK,CAACe,OAAO,CAAC,CAAC,IAGjBT,OAAO;IAGf,CAAC,CAAC;IAEF,OAAO,CAAC,CAAC;EACX;AACF,CAAC,CAAC", "ignoreList": []}