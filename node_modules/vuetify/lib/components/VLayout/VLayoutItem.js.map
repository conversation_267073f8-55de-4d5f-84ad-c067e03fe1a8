{"version": 3, "file": "VLayoutItem.js", "names": ["makeComponentProps", "makeLayoutItemProps", "useLayoutItem", "computed", "toRef", "genericComponent", "propsFactory", "makeVLayoutItemProps", "position", "type", "String", "required", "size", "Number", "default", "modelValue", "Boolean", "VLayoutItem", "name", "props", "setup", "_ref", "slots", "layoutItemStyles", "id", "order", "parseInt", "elementSize", "layoutSize", "active", "absolute", "_createElementVNode", "_normalizeClass", "class", "_normalizeStyle", "value", "style"], "sources": ["../../../src/components/VLayout/VLayoutItem.tsx"], "sourcesContent": ["// Styles\nimport './VLayoutItem.sass'\n\n// Composables\nimport { makeComponentProps } from '@/composables/component'\nimport { makeLayoutItemProps, useLayoutItem } from '@/composables/layout'\n\n// Utilities\nimport { computed, toRef } from 'vue'\nimport { genericComponent, propsFactory } from '@/util'\n\n// Types\nimport type { PropType } from 'vue'\n\nexport const makeVLayoutItemProps = propsFactory({\n  position: {\n    type: String as PropType<'top' | 'right' | 'bottom' | 'left'>,\n    required: true,\n  },\n  size: {\n    type: [Number, String],\n    default: 300,\n  },\n  modelValue: Boolean,\n\n  ...makeComponentProps(),\n  ...makeLayoutItemProps(),\n}, 'VLayoutItem')\n\nexport const VLayoutItem = genericComponent()({\n  name: 'VLayoutItem',\n\n  props: makeVLayoutItemProps(),\n\n  setup (props, { slots }) {\n    const { layoutItemStyles } = useLayoutItem({\n      id: props.name,\n      order: computed(() => parseInt(props.order, 10)),\n      position: toRef(() => props.position),\n      elementSize: toRef(() => props.size),\n      layoutSize: toRef(() => props.size),\n      active: toRef(() => props.modelValue),\n      absolute: toRef(() => props.absolute),\n    })\n\n    return () => (\n      <div\n        class={[\n          'v-layout-item',\n          props.class,\n        ]}\n        style={[\n          layoutItemStyles.value,\n          props.style,\n        ]}\n      >\n        { slots.default?.() }\n      </div>\n    )\n  },\n})\n\nexport type VLayoutItem = InstanceType<typeof VLayoutItem>\n"], "mappings": ";AAAA;AACA;;AAEA;AAAA,SACSA,kBAAkB;AAAA,SAClBC,mBAAmB,EAAEC,aAAa,uCAE3C;AACA,SAASC,QAAQ,EAAEC,KAAK,QAAQ,KAAK;AAAA,SAC5BC,gBAAgB,EAAEC,YAAY,+BAEvC;AAGA,OAAO,MAAMC,oBAAoB,GAAGD,YAAY,CAAC;EAC/CE,QAAQ,EAAE;IACRC,IAAI,EAAEC,MAAuD;IAC7DC,QAAQ,EAAE;EACZ,CAAC;EACDC,IAAI,EAAE;IACJH,IAAI,EAAE,CAACI,MAAM,EAAEH,MAAM,CAAC;IACtBI,OAAO,EAAE;EACX,CAAC;EACDC,UAAU,EAAEC,OAAO;EAEnB,GAAGhB,kBAAkB,CAAC,CAAC;EACvB,GAAGC,mBAAmB,CAAC;AACzB,CAAC,EAAE,aAAa,CAAC;AAEjB,OAAO,MAAMgB,WAAW,GAAGZ,gBAAgB,CAAC,CAAC,CAAC;EAC5Ca,IAAI,EAAE,aAAa;EAEnBC,KAAK,EAAEZ,oBAAoB,CAAC,CAAC;EAE7Ba,KAAKA,CAAED,KAAK,EAAAE,IAAA,EAAa;IAAA,IAAX;MAAEC;IAAM,CAAC,GAAAD,IAAA;IACrB,MAAM;MAAEE;IAAiB,CAAC,GAAGrB,aAAa,CAAC;MACzCsB,EAAE,EAAEL,KAAK,CAACD,IAAI;MACdO,KAAK,EAAEtB,QAAQ,CAAC,MAAMuB,QAAQ,CAACP,KAAK,CAACM,KAAK,EAAE,EAAE,CAAC,CAAC;MAChDjB,QAAQ,EAAEJ,KAAK,CAAC,MAAMe,KAAK,CAACX,QAAQ,CAAC;MACrCmB,WAAW,EAAEvB,KAAK,CAAC,MAAMe,KAAK,CAACP,IAAI,CAAC;MACpCgB,UAAU,EAAExB,KAAK,CAAC,MAAMe,KAAK,CAACP,IAAI,CAAC;MACnCiB,MAAM,EAAEzB,KAAK,CAAC,MAAMe,KAAK,CAACJ,UAAU,CAAC;MACrCe,QAAQ,EAAE1B,KAAK,CAAC,MAAMe,KAAK,CAACW,QAAQ;IACtC,CAAC,CAAC;IAEF,OAAO,MAAAC,mBAAA;MAAA,SAAAC,eAAA,CAEI,CACL,eAAe,EACfb,KAAK,CAACc,KAAK,CACZ;MAAA,SAAAC,eAAA,CACM,CACLX,gBAAgB,CAACY,KAAK,EACtBhB,KAAK,CAACiB,KAAK,CACZ;IAAA,IAECd,KAAK,CAACR,OAAO,GAAG,CAAC,EAEtB;EACH;AACF,CAAC,CAAC", "ignoreList": []}