{"version": 3, "file": "VLayout.js", "names": ["makeComponentProps", "makeDimensionProps", "useDimension", "createLayout", "makeLayoutProps", "genericComponent", "propsFactory", "useRender", "makeVLayoutProps", "VLayout", "name", "props", "setup", "_ref", "slots", "layoutClasses", "layoutStyles", "getLayoutItem", "items", "layoutRef", "dimensionStyles", "_createElementVNode", "_normalizeClass", "value", "class", "_normalizeStyle", "style", "default"], "sources": ["../../../src/components/VLayout/VLayout.tsx"], "sourcesContent": ["// Styles\nimport './VLayout.sass'\n\n// Composables\nimport { makeComponentProps } from '@/composables/component'\nimport { makeDimensionProps, useDimension } from '@/composables/dimensions'\nimport { createLayout, makeLayoutProps } from '@/composables/layout'\n\n// Utilities\nimport { genericComponent, propsFactory, useRender } from '@/util'\n\nexport const makeVLayoutProps = propsFactory({\n  ...makeComponentProps(),\n  ...makeDimensionProps(),\n  ...makeLayoutProps(),\n}, 'VLayout')\n\nexport const VLayout = genericComponent()({\n  name: 'VLayout',\n\n  props: makeVLayoutProps(),\n\n  setup (props, { slots }) {\n    const { layoutClasses, layoutStyles, getLayoutItem, items, layoutRef } = createLayout(props)\n    const { dimensionStyles } = useDimension(props)\n\n    useRender(() => (\n      <div\n        ref={ layoutRef }\n        class={[\n          layoutClasses.value,\n          props.class,\n        ]}\n        style={[\n          dimensionStyles.value,\n          layoutStyles.value,\n          props.style,\n        ]}\n      >\n        { slots.default?.() }\n      </div>\n    ))\n\n    return {\n      getLayoutItem,\n      items,\n    }\n  },\n})\n\nexport type VLayout = InstanceType<typeof VLayout>\n"], "mappings": ";AAAA;AACA;;AAEA;AAAA,SACSA,kBAAkB;AAAA,SAClBC,kBAAkB,EAAEC,YAAY;AAAA,SAChCC,YAAY,EAAEC,eAAe,uCAEtC;AAAA,SACSC,gBAAgB,EAAEC,YAAY,EAAEC,SAAS;AAElD,OAAO,MAAMC,gBAAgB,GAAGF,YAAY,CAAC;EAC3C,GAAGN,kBAAkB,CAAC,CAAC;EACvB,GAAGC,kBAAkB,CAAC,CAAC;EACvB,GAAGG,eAAe,CAAC;AACrB,CAAC,EAAE,SAAS,CAAC;AAEb,OAAO,MAAMK,OAAO,GAAGJ,gBAAgB,CAAC,CAAC,CAAC;EACxCK,IAAI,EAAE,SAAS;EAEfC,KAAK,EAAEH,gBAAgB,CAAC,CAAC;EAEzBI,KAAKA,CAAED,KAAK,EAAAE,IAAA,EAAa;IAAA,IAAX;MAAEC;IAAM,CAAC,GAAAD,IAAA;IACrB,MAAM;MAAEE,aAAa;MAAEC,YAAY;MAAEC,aAAa;MAAEC,KAAK;MAAEC;IAAU,CAAC,GAAGhB,YAAY,CAACQ,KAAK,CAAC;IAC5F,MAAM;MAAES;IAAgB,CAAC,GAAGlB,YAAY,CAACS,KAAK,CAAC;IAE/CJ,SAAS,CAAC,MAAAc,mBAAA;MAAA,OAEAF,SAAS;MAAA,SAAAG,eAAA,CACR,CACLP,aAAa,CAACQ,KAAK,EACnBZ,KAAK,CAACa,KAAK,CACZ;MAAA,SAAAC,eAAA,CACM,CACLL,eAAe,CAACG,KAAK,EACrBP,YAAY,CAACO,KAAK,EAClBZ,KAAK,CAACe,KAAK,CACZ;IAAA,IAECZ,KAAK,CAACa,OAAO,GAAG,CAAC,EAEtB,CAAC;IAEF,OAAO;MACLV,aAAa;MACbC;IACF,CAAC;EACH;AACF,CAAC,CAAC", "ignoreList": []}