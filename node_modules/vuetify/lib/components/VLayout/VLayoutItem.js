import { normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, createElementVNode as _createElementVNode } from "vue";
// Styles
import "./VLayoutItem.css";

// Composables
import { makeComponentProps } from "../../composables/component.js";
import { makeLayoutItemProps, useLayoutItem } from "../../composables/layout.js"; // Utilities
import { computed, toRef } from 'vue';
import { genericComponent, propsFactory } from "../../util/index.js"; // Types
export const makeVLayoutItemProps = propsFactory({
  position: {
    type: String,
    required: true
  },
  size: {
    type: [Number, String],
    default: 300
  },
  modelValue: Boolean,
  ...makeComponentProps(),
  ...makeLayoutItemProps()
}, 'VLayoutItem');
export const VLayoutItem = genericComponent()({
  name: 'VLayoutItem',
  props: makeVLayoutItemProps(),
  setup(props, _ref) {
    let {
      slots
    } = _ref;
    const {
      layoutItemStyles
    } = useLayoutItem({
      id: props.name,
      order: computed(() => parseInt(props.order, 10)),
      position: toRef(() => props.position),
      elementSize: toRef(() => props.size),
      layoutSize: toRef(() => props.size),
      active: toRef(() => props.modelValue),
      absolute: toRef(() => props.absolute)
    });
    return () => _createElementVNode("div", {
      "class": _normalizeClass(['v-layout-item', props.class]),
      "style": _normalizeStyle([layoutItemStyles.value, props.style])
    }, [slots.default?.()]);
  }
});
//# sourceMappingURL=VLayoutItem.js.map