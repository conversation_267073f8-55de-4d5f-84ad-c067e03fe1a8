{"version": 3, "file": "VDefaultsProvider.js", "names": ["provideDefaults", "toRefs", "genericComponent", "propsFactory", "makeVDefaultsProviderProps", "defaults", "Object", "disabled", "Boolean", "reset", "Number", "String", "root", "scoped", "VDefaultsProvider", "name", "props", "setup", "_ref", "slots", "default"], "sources": ["../../../src/components/VDefaultsProvider/VDefaultsProvider.tsx"], "sourcesContent": ["// Composables\nimport { provideDefaults } from '@/composables/defaults'\n\n// Utilities\nimport { toRefs } from 'vue'\nimport { genericComponent, propsFactory } from '@/util'\n\n// Types\nimport type { PropType } from 'vue'\nimport type { DefaultsOptions } from '@/composables/defaults'\n\nexport const makeVDefaultsProviderProps = propsFactory({\n  defaults: Object as PropType<DefaultsOptions>,\n  disabled: Boolean,\n  reset: [Number, String],\n  root: [Boolean, String],\n  scoped: Boolean,\n}, 'VDefaultsProvider')\n\nexport const VDefaultsProvider = genericComponent(false)({\n  name: 'VDefaultsProvider',\n\n  props: makeVDefaultsProviderProps(),\n\n  setup (props, { slots }) {\n    const { defaults, disabled, reset, root, scoped } = toRefs(props)\n\n    provideDefaults(defaults, {\n      reset,\n      root,\n      scoped,\n      disabled,\n    })\n\n    return () => slots.default?.()\n  },\n})\n\nexport type VDefaultsProvider = InstanceType<typeof VDefaultsProvider>\n"], "mappings": "AAAA;AAAA,SACSA,eAAe,yCAExB;AACA,SAASC,MAAM,QAAQ,KAAK;AAAA,SACnBC,gBAAgB,EAAEC,YAAY,+BAEvC;AAIA,OAAO,MAAMC,0BAA0B,GAAGD,YAAY,CAAC;EACrDE,QAAQ,EAAEC,MAAmC;EAC7CC,QAAQ,EAAEC,OAAO;EACjBC,KAAK,EAAE,CAACC,MAAM,EAAEC,MAAM,CAAC;EACvBC,IAAI,EAAE,CAACJ,OAAO,EAAEG,MAAM,CAAC;EACvBE,MAAM,EAAEL;AACV,CAAC,EAAE,mBAAmB,CAAC;AAEvB,OAAO,MAAMM,iBAAiB,GAAGZ,gBAAgB,CAAC,KAAK,CAAC,CAAC;EACvDa,IAAI,EAAE,mBAAmB;EAEzBC,KAAK,EAAEZ,0BAA0B,CAAC,CAAC;EAEnCa,KAAKA,CAAED,KAAK,EAAAE,IAAA,EAAa;IAAA,IAAX;MAAEC;IAAM,CAAC,GAAAD,IAAA;IACrB,MAAM;MAAEb,QAAQ;MAAEE,QAAQ;MAAEE,KAAK;MAAEG,IAAI;MAAEC;IAAO,CAAC,GAAGZ,MAAM,CAACe,KAAK,CAAC;IAEjEhB,eAAe,CAACK,QAAQ,EAAE;MACxBI,KAAK;MACLG,IAAI;MACJC,MAAM;MACNN;IACF,CAAC,CAAC;IAEF,OAAO,MAAMY,KAAK,CAACC,OAAO,GAAG,CAAC;EAChC;AACF,CAAC,CAAC", "ignoreList": []}