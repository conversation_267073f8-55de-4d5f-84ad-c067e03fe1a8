{"version": 3, "file": "VEmptyState.js", "names": ["VBtn", "VDefaultsProvider", "VIcon", "VImg", "useBackgroundColor", "makeComponentProps", "makeDimensionProps", "useDimension", "useDisplay", "IconValue", "makeSizeProps", "makeThemeProps", "provideTheme", "convertToUnit", "genericComponent", "propsFactory", "useRender", "makeVEmptyStateProps", "actionText", "String", "bgColor", "color", "icon", "image", "justify", "type", "default", "headline", "title", "text", "textWidth", "Number", "href", "to", "size", "undefined", "VEmptyState", "name", "props", "emits", "e", "setup", "_ref", "emit", "slots", "themeClasses", "backgroundColorClasses", "backgroundColorStyles", "dimensionStyles", "displayClasses", "onClickAction", "hasActions", "actions", "hasHeadline", "hasTitle", "hasText", "hasMedia", "media", "_createElementVNode", "_normalizeClass", "value", "class", "_normalizeStyle", "style", "_Fragment", "_createVNode", "src", "height", "max<PERSON><PERSON><PERSON>", "onClick"], "sources": ["../../../src/components/VEmptyState/VEmptyState.tsx"], "sourcesContent": ["// Styles\nimport './VEmptyState.sass'\n\n// Components\nimport { VBtn } from '@/components/VBtn'\nimport { VDefaultsProvider } from '@/components/VDefaultsProvider'\nimport { VIcon } from '@/components/VIcon'\nimport { VImg } from '@/components/VImg'\n\n// Composables\nimport { useBackgroundColor } from '@/composables/color'\nimport { makeComponentProps } from '@/composables/component'\nimport { makeDimensionProps, useDimension } from '@/composables/dimensions'\nimport { useDisplay } from '@/composables/display'\nimport { IconValue } from '@/composables/icons'\nimport { makeSizeProps } from '@/composables/size'\nimport { makeThemeProps, provideTheme } from '@/composables/theme'\n\n// Utilities\nimport { convertToUnit, genericComponent, propsFactory, useRender } from '@/util'\n\n// Types\nimport type { PropType } from 'vue'\n\n// Types\n\nexport type VEmptyStateSlots = {\n  actions: {\n    props: {\n      onClick: (e: Event) => void\n    }\n  }\n  default: never\n  headline: never\n  title: never\n  media: never\n  text: never\n}\n\nexport const makeVEmptyStateProps = propsFactory({\n  actionText: String,\n  bgColor: String,\n  color: String,\n  icon: IconValue,\n  image: String,\n  justify: {\n    type: String as PropType<'start' | 'center' | 'end'>,\n    default: 'center',\n  },\n  headline: String,\n  title: String,\n  text: String,\n  textWidth: {\n    type: [Number, String],\n    default: 500,\n  },\n  href: String,\n  to: String,\n\n  ...makeComponentProps(),\n  ...makeDimensionProps(),\n  ...makeSizeProps({ size: undefined }),\n  ...makeThemeProps(),\n}, 'VEmptyState')\n\nexport const VEmptyState = genericComponent<VEmptyStateSlots>()({\n  name: 'VEmptyState',\n\n  props: makeVEmptyStateProps(),\n\n  emits: {\n    'click:action': (e: Event) => true,\n  },\n\n  setup (props, { emit, slots }) {\n    const { themeClasses } = provideTheme(props)\n    const { backgroundColorClasses, backgroundColorStyles } = useBackgroundColor(() => props.bgColor)\n    const { dimensionStyles } = useDimension(props)\n    const { displayClasses } = useDisplay()\n\n    function onClickAction (e: Event) {\n      emit('click:action', e)\n    }\n\n    useRender(() => {\n      const hasActions = !!(slots.actions || props.actionText)\n      const hasHeadline = !!(slots.headline || props.headline)\n      const hasTitle = !!(slots.title || props.title)\n      const hasText = !!(slots.text || props.text)\n      const hasMedia = !!(slots.media || props.image || props.icon)\n      const size = props.size || (props.image ? 200 : 96)\n\n      return (\n        <div\n          class={[\n            'v-empty-state',\n            {\n              [`v-empty-state--${props.justify}`]: true,\n            },\n            themeClasses.value,\n            backgroundColorClasses.value,\n            displayClasses.value,\n            props.class,\n          ]}\n          style={[\n            backgroundColorStyles.value,\n            dimensionStyles.value,\n            props.style,\n          ]}\n        >\n          { hasMedia && (\n            <div key=\"media\" class=\"v-empty-state__media\">\n              { !slots.media ? (\n                <>\n                  { props.image ? (\n                    <VImg\n                      key=\"image\"\n                      src={ props.image }\n                      height={ size }\n                    />\n                  ) : props.icon ? (\n                    <VIcon\n                      key=\"icon\"\n                      color={ props.color }\n                      size={ size }\n                      icon={ props.icon }\n                    />\n                  ) : undefined }\n                </>\n              ) : (\n                <VDefaultsProvider\n                  key=\"media-defaults\"\n                  defaults={{\n                    VImg: {\n                      src: props.image,\n                      height: size,\n                    },\n                    VIcon: {\n                      size,\n                      icon: props.icon,\n                    },\n                  }}\n                >\n                  { slots.media() }\n                </VDefaultsProvider>\n              )}\n            </div>\n          )}\n\n          { hasHeadline && (\n            <div key=\"headline\" class=\"v-empty-state__headline\">\n              { slots.headline?.() ?? props.headline }\n            </div>\n          )}\n\n          { hasTitle && (\n            <div key=\"title\" class=\"v-empty-state__title\">\n              { slots.title?.() ?? props.title }\n            </div>\n          )}\n\n          { hasText && (\n            <div\n              key=\"text\"\n              class=\"v-empty-state__text\"\n              style={{\n                maxWidth: convertToUnit(props.textWidth),\n              }}\n            >\n              { slots.text?.() ?? props.text }\n            </div>\n          )}\n\n          { slots.default && (\n            <div key=\"content\" class=\"v-empty-state__content\">\n              { slots.default() }\n            </div>\n          )}\n\n          { hasActions && (\n            <div key=\"actions\" class=\"v-empty-state__actions\">\n              <VDefaultsProvider\n                defaults={{\n                  VBtn: {\n                    class: 'v-empty-state__action-btn',\n                    color: props.color ?? 'surface-variant',\n                    href: props.href,\n                    text: props.actionText,\n                    to: props.to,\n                  },\n                }}\n              >\n                {\n                  slots.actions?.({ props: { onClick: onClickAction } }) ?? (\n                    <VBtn onClick={ onClickAction } />\n                  )\n                }\n              </VDefaultsProvider>\n            </div>\n          )}\n        </div>\n      )\n    })\n\n    return {}\n  },\n})\n\nexport type VEmptyState = InstanceType<typeof VEmptyState>\n"], "mappings": ";AAAA;AACA;;AAEA;AAAA,SACSA,IAAI;AAAA,SACJC,iBAAiB;AAAA,SACjBC,KAAK;AAAA,SACLC,IAAI,4BAEb;AAAA,SACSC,kBAAkB;AAAA,SAClBC,kBAAkB;AAAA,SAClBC,kBAAkB,EAAEC,YAAY;AAAA,SAChCC,UAAU;AAAA,SACVC,SAAS;AAAA,SACTC,aAAa;AAAA,SACbC,cAAc,EAAEC,YAAY,sCAErC;AAAA,SACSC,aAAa,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,SAAS,+BAEjE;AAGA;AAeA,OAAO,MAAMC,oBAAoB,GAAGF,YAAY,CAAC;EAC/CG,UAAU,EAAEC,MAAM;EAClBC,OAAO,EAAED,MAAM;EACfE,KAAK,EAAEF,MAAM;EACbG,IAAI,EAAEb,SAAS;EACfc,KAAK,EAAEJ,MAAM;EACbK,OAAO,EAAE;IACPC,IAAI,EAAEN,MAA8C;IACpDO,OAAO,EAAE;EACX,CAAC;EACDC,QAAQ,EAAER,MAAM;EAChBS,KAAK,EAAET,MAAM;EACbU,IAAI,EAAEV,MAAM;EACZW,SAAS,EAAE;IACTL,IAAI,EAAE,CAACM,MAAM,EAAEZ,MAAM,CAAC;IACtBO,OAAO,EAAE;EACX,CAAC;EACDM,IAAI,EAAEb,MAAM;EACZc,EAAE,EAAEd,MAAM;EAEV,GAAGd,kBAAkB,CAAC,CAAC;EACvB,GAAGC,kBAAkB,CAAC,CAAC;EACvB,GAAGI,aAAa,CAAC;IAAEwB,IAAI,EAAEC;EAAU,CAAC,CAAC;EACrC,GAAGxB,cAAc,CAAC;AACpB,CAAC,EAAE,aAAa,CAAC;AAEjB,OAAO,MAAMyB,WAAW,GAAGtB,gBAAgB,CAAmB,CAAC,CAAC;EAC9DuB,IAAI,EAAE,aAAa;EAEnBC,KAAK,EAAErB,oBAAoB,CAAC,CAAC;EAE7BsB,KAAK,EAAE;IACL,cAAc,EAAGC,CAAQ,IAAK;EAChC,CAAC;EAEDC,KAAKA,CAAEH,KAAK,EAAAI,IAAA,EAAmB;IAAA,IAAjB;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAAF,IAAA;IAC3B,MAAM;MAAEG;IAAa,CAAC,GAAGjC,YAAY,CAAC0B,KAAK,CAAC;IAC5C,MAAM;MAAEQ,sBAAsB;MAAEC;IAAsB,CAAC,GAAG3C,kBAAkB,CAAC,MAAMkC,KAAK,CAAClB,OAAO,CAAC;IACjG,MAAM;MAAE4B;IAAgB,CAAC,GAAGzC,YAAY,CAAC+B,KAAK,CAAC;IAC/C,MAAM;MAAEW;IAAe,CAAC,GAAGzC,UAAU,CAAC,CAAC;IAEvC,SAAS0C,aAAaA,CAAEV,CAAQ,EAAE;MAChCG,IAAI,CAAC,cAAc,EAAEH,CAAC,CAAC;IACzB;IAEAxB,SAAS,CAAC,MAAM;MACd,MAAMmC,UAAU,GAAG,CAAC,EAAEP,KAAK,CAACQ,OAAO,IAAId,KAAK,CAACpB,UAAU,CAAC;MACxD,MAAMmC,WAAW,GAAG,CAAC,EAAET,KAAK,CAACjB,QAAQ,IAAIW,KAAK,CAACX,QAAQ,CAAC;MACxD,MAAM2B,QAAQ,GAAG,CAAC,EAAEV,KAAK,CAAChB,KAAK,IAAIU,KAAK,CAACV,KAAK,CAAC;MAC/C,MAAM2B,OAAO,GAAG,CAAC,EAAEX,KAAK,CAACf,IAAI,IAAIS,KAAK,CAACT,IAAI,CAAC;MAC5C,MAAM2B,QAAQ,GAAG,CAAC,EAAEZ,KAAK,CAACa,KAAK,IAAInB,KAAK,CAACf,KAAK,IAAIe,KAAK,CAAChB,IAAI,CAAC;MAC7D,MAAMY,IAAI,GAAGI,KAAK,CAACJ,IAAI,KAAKI,KAAK,CAACf,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC;MAEnD,OAAAmC,mBAAA;QAAA,SAAAC,eAAA,CAEW,CACL,eAAe,EACf;UACE,CAAC,kBAAkBrB,KAAK,CAACd,OAAO,EAAE,GAAG;QACvC,CAAC,EACDqB,YAAY,CAACe,KAAK,EAClBd,sBAAsB,CAACc,KAAK,EAC5BX,cAAc,CAACW,KAAK,EACpBtB,KAAK,CAACuB,KAAK,CACZ;QAAA,SAAAC,eAAA,CACM,CACLf,qBAAqB,CAACa,KAAK,EAC3BZ,eAAe,CAACY,KAAK,EACrBtB,KAAK,CAACyB,KAAK,CACZ;MAAA,IAECP,QAAQ,IAAAE,mBAAA;QAAA;QAAA;MAAA,IAEJ,CAACd,KAAK,CAACa,KAAK,GAAAC,mBAAA,CAAAM,SAAA,SAER1B,KAAK,CAACf,KAAK,GAAA0C,YAAA,CAAA9D,IAAA;QAAA;QAAA,OAGHmC,KAAK,CAACf,KAAK;QAAA,UACRW;MAAI,WAEbI,KAAK,CAAChB,IAAI,GAAA2C,YAAA,CAAA/D,KAAA;QAAA;QAAA,SAGFoC,KAAK,CAACjB,KAAK;QAAA,QACZa,IAAI;QAAA,QACJI,KAAK,CAAChB;MAAI,WAEjBa,SAAS,KAAA8B,YAAA,CAAAhE,iBAAA;QAAA;QAAA,YAKH;UACRE,IAAI,EAAE;YACJ+D,GAAG,EAAE5B,KAAK,CAACf,KAAK;YAChB4C,MAAM,EAAEjC;UACV,CAAC;UACDhC,KAAK,EAAE;YACLgC,IAAI;YACJZ,IAAI,EAAEgB,KAAK,CAAChB;UACd;QACF;MAAC;QAAAI,OAAA,EAAAA,CAAA,MAECkB,KAAK,CAACa,KAAK,CAAC,CAAC;MAAA,EAElB,EAEJ,EAECJ,WAAW,IAAAK,mBAAA;QAAA;QAAA;MAAA,IAEPd,KAAK,CAACjB,QAAQ,GAAG,CAAC,IAAIW,KAAK,CAACX,QAAQ,EAEzC,EAEC2B,QAAQ,IAAAI,mBAAA;QAAA;QAAA;MAAA,IAEJd,KAAK,CAAChB,KAAK,GAAG,CAAC,IAAIU,KAAK,CAACV,KAAK,EAEnC,EAEC2B,OAAO,IAAAG,mBAAA;QAAA;QAAA;QAAA,SAIE;UACLU,QAAQ,EAAEvD,aAAa,CAACyB,KAAK,CAACR,SAAS;QACzC;MAAC,IAECc,KAAK,CAACf,IAAI,GAAG,CAAC,IAAIS,KAAK,CAACT,IAAI,EAEjC,EAECe,KAAK,CAAClB,OAAO,IAAAgC,mBAAA;QAAA;QAAA;MAAA,IAETd,KAAK,CAAClB,OAAO,CAAC,CAAC,EAEpB,EAECyB,UAAU,IAAAO,mBAAA;QAAA;QAAA;MAAA,IAAAO,YAAA,CAAAhE,iBAAA;QAAA,YAGI;UACRD,IAAI,EAAE;YACJ6D,KAAK,EAAE,2BAA2B;YAClCxC,KAAK,EAAEiB,KAAK,CAACjB,KAAK,IAAI,iBAAiB;YACvCW,IAAI,EAAEM,KAAK,CAACN,IAAI;YAChBH,IAAI,EAAES,KAAK,CAACpB,UAAU;YACtBe,EAAE,EAAEK,KAAK,CAACL;UACZ;QACF;MAAC;QAAAP,OAAA,EAAAA,CAAA,MAGCkB,KAAK,CAACQ,OAAO,GAAG;UAAEd,KAAK,EAAE;YAAE+B,OAAO,EAAEnB;UAAc;QAAE,CAAC,CAAC,IAAAe,YAAA,CAAAjE,IAAA;UAAA,WACpCkD;QAAa,QAC9B;MAAA,IAIR;IAGP,CAAC,CAAC;IAEF,OAAO,CAAC,CAAC;EACX;AACF,CAAC,CAAC", "ignoreList": []}