@use '../../styles/tools'
@use './variables' as *

@include tools.layer('components')
  .v-empty-state
    align-items: center
    display: flex
    flex-direction: column
    justify-content: center
    min-height: $empty-state-min-height
    padding: $empty-state-padding

    &--start
      align-items: flex-start

    &--center
      align-items: center

    &--end
      align-items: flex-end

  .v-empty-state__media
    text-align: center
    width: 100%

    .v-icon
      color: $empty-state-media-icon-color

  .v-empty-state__headline
    color: $empty-state-headline-color
    font-size: $empty-state-headline-font-size
    font-weight: $empty-state-headline-font-weight
    line-height: $empty-state-headline-line-height
    text-align: center
    margin-bottom: $empty-state-headline-margin-bottom

    .v-empty-state--mobile &
      font-size: $empty-state-headline-mobile-font-size

  .v-empty-state__title
    font-size: $empty-state-title-font-size
    font-weight: $empty-state-title-font-weight
    line-height: $empty-state-title-line-height
    margin-bottom: $empty-state-title-margin-bottom
    text-align: center

  .v-empty-state__text
    font-size: $empty-state-text-font-size
    font-weight: $empty-state-text-font-weight
    line-height: $empty-state-text-line-height
    padding: $empty-state-text-padding
    text-align: center

  .v-empty-state__content
    padding: $empty-state-content-padding

  .v-empty-state__actions
    display: flex
    gap: $empty-state-actions-gap
    padding: $empty-state-actions-padding

  .v-empty-state__action-btn.v-btn
    background-color: $empty-state-actions-btn-background-color
    color: $empty-state-actions-btn-color
