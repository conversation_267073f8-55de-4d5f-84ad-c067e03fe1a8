.v-empty-state {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 100%;
  padding: 16px;
}
.v-empty-state--start {
  align-items: flex-start;
}
.v-empty-state--center {
  align-items: center;
}
.v-empty-state--end {
  align-items: flex-end;
}

.v-empty-state__media {
  text-align: center;
  width: 100%;
}
.v-empty-state__media .v-icon {
  color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));
}

.v-empty-state__headline {
  color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));
  font-size: 3.75rem;
  font-weight: 300;
  line-height: 1;
  text-align: center;
  margin-bottom: 8px;
}
.v-empty-state--mobile .v-empty-state__headline {
  font-size: 2.125rem;
}

.v-empty-state__title {
  font-size: 1.25rem;
  font-weight: 500;
  line-height: 1.6;
  margin-bottom: 4px;
  text-align: center;
}

.v-empty-state__text {
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.425;
  padding: 0 16px;
  text-align: center;
}

.v-empty-state__content {
  padding: 24px 0;
}

.v-empty-state__actions {
  display: flex;
  gap: 8px;
  padding: 16px;
}

.v-empty-state__action-btn.v-btn {
  background-color: initial;
  color: initial;
}