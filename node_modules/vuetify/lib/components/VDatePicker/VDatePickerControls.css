.v-date-picker-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.875rem;
  height: var(--v-date-picker-controls-height, 56px);
  padding-top: 4px;
  padding-bottom: 4px;
  padding-inline-start: 6px;
  padding-inline-end: 12px;
}
.v-date-picker-controls > .v-btn:first-child {
  text-transform: none;
  font-weight: 400;
  line-height: initial;
  letter-spacing: initial;
}
.v-date-picker-controls--variant-classic {
  padding-inline-start: 12px;
}
.v-date-picker-controls--variant-modern .v-date-picker__title:not(:hover) {
  opacity: 0.7;
}
.v-date-picker--month .v-date-picker-controls--variant-modern .v-date-picker__title {
  cursor: pointer;
}
.v-date-picker--year .v-date-picker-controls--variant-modern .v-date-picker__title {
  opacity: 1;
}
.v-date-picker-controls .v-btn:last-child {
  margin-inline-start: 4px;
}
.v-date-picker--year .v-date-picker-controls .v-date-picker-controls__mode-btn {
  transform: rotate(180deg);
}

.v-date-picker-controls__date {
  margin-inline-end: 4px;
}
.v-date-picker-controls--variant-classic .v-date-picker-controls__date {
  margin: auto;
  text-align: center;
}

.v-date-picker-controls__month {
  display: flex;
}
.v-locale--is-rtl.v-date-picker-controls__month, .v-locale--is-rtl .v-date-picker-controls__month {
  flex-direction: row-reverse;
}

.v-date-picker-controls--variant-classic .v-date-picker-controls__month {
  flex: 1 0 auto;
}

.v-date-picker__title {
  display: inline-block;
}