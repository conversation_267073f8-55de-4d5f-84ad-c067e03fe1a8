{"version": 3, "file": "VDatePickerHeader.js", "names": ["VBtn", "VDefaultsProvider", "useBackgroundColor", "IconValue", "MaybeTransition", "EventProp", "genericComponent", "propsFactory", "useRender", "makeVDatePickerHeaderProps", "appendIcon", "color", "String", "header", "transition", "onClick", "VDatePickerHeader", "name", "props", "emits", "click", "click:append", "setup", "_ref", "emit", "slots", "backgroundColorClasses", "backgroundColorStyles", "onClickAppend", "<PERSON><PERSON><PERSON><PERSON>", "default", "hasAppend", "append", "_createElementVNode", "_normalizeClass", "value", "_normalizeStyle", "prepend", "_createVNode", "icon", "variant"], "sources": ["../../../src/components/VDatePicker/VDatePickerHeader.tsx"], "sourcesContent": ["// Styles\nimport './VDatePickerHeader.sass'\n\n// Components\nimport { VBtn } from '@/components/VBtn'\nimport { VDefaultsProvider } from '@/components/VDefaultsProvider'\n\n// Composables\nimport { useBackgroundColor } from '@/composables/color'\nimport { IconValue } from '@/composables/icons'\nimport { MaybeTransition } from '@/composables/transition'\n\n// Utilities\nimport { EventProp, genericComponent, propsFactory, useRender } from '@/util'\n\n// Types\nexport type VDatePickerHeaderSlots = {\n  prepend: never\n  default: never\n  append: never\n}\n\nexport const makeVDatePickerHeaderProps = propsFactory({\n  appendIcon: IconValue,\n  color: String,\n  header: String,\n  transition: String,\n  onClick: EventProp<[MouseEvent]>(),\n}, 'VDatePickerHeader')\n\nexport const VDatePickerHeader = genericComponent<VDatePickerHeaderSlots>()({\n  name: 'VDatePickerHeader',\n\n  props: makeVDatePickerHeaderProps(),\n\n  emits: {\n    click: () => true,\n    'click:append': () => true,\n  },\n\n  setup (props, { emit, slots }) {\n    const { backgroundColorClasses, backgroundColorStyles } = useBackgroundColor(() => props.color)\n\n    function onClick () {\n      emit('click')\n    }\n\n    function onClickAppend () {\n      emit('click:append')\n    }\n\n    useRender(() => {\n      const hasContent = !!(slots.default || props.header)\n      const hasAppend = !!(slots.append || props.appendIcon)\n\n      return (\n        <div\n          class={[\n            'v-date-picker-header',\n            {\n              'v-date-picker-header--clickable': !!props.onClick,\n            },\n            backgroundColorClasses.value,\n          ]}\n          style={ backgroundColorStyles.value }\n          onClick={ onClick }\n        >\n          { slots.prepend && (\n            <div key=\"prepend\" class=\"v-date-picker-header__prepend\">\n              { slots.prepend() }\n            </div>\n          )}\n\n          { hasContent && (\n            <MaybeTransition key=\"content\" name={ props.transition }>\n              <div key={ props.header } class=\"v-date-picker-header__content\">\n                { slots.default?.() ?? props.header }\n              </div>\n            </MaybeTransition>\n          )}\n\n          { hasAppend && (\n            <div class=\"v-date-picker-header__append\">\n              { !slots.append ? (\n                <VBtn\n                  key=\"append-btn\"\n                  icon={ props.appendIcon }\n                  variant=\"text\"\n                  onClick={ onClickAppend }\n                />\n              ) : (\n                <VDefaultsProvider\n                  key=\"append-defaults\"\n                  disabled={ !props.appendIcon }\n                  defaults={{\n                    VBtn: {\n                      icon: props.appendIcon,\n                      variant: 'text',\n                    },\n                  }}\n                >\n                  { slots.append?.() }\n                </VDefaultsProvider>\n              )}\n            </div>\n          )}\n        </div>\n      )\n    })\n\n    return {}\n  },\n})\n\nexport type VDatePickerHeader = InstanceType<typeof VDatePickerHeader>\n"], "mappings": ";AAAA;AACA;;AAEA;AAAA,SACSA,IAAI;AAAA,SACJC,iBAAiB,yCAE1B;AAAA,SACSC,kBAAkB;AAAA,SAClBC,SAAS;AAAA,SACTC,eAAe,2CAExB;AAAA,SACSC,SAAS,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,SAAS,+BAE7D;AAOA,OAAO,MAAMC,0BAA0B,GAAGF,YAAY,CAAC;EACrDG,UAAU,EAAEP,SAAS;EACrBQ,KAAK,EAAEC,MAAM;EACbC,MAAM,EAAED,MAAM;EACdE,UAAU,EAAEF,MAAM;EAClBG,OAAO,EAAEV,SAAS,CAAe;AACnC,CAAC,EAAE,mBAAmB,CAAC;AAEvB,OAAO,MAAMW,iBAAiB,GAAGV,gBAAgB,CAAyB,CAAC,CAAC;EAC1EW,IAAI,EAAE,mBAAmB;EAEzBC,KAAK,EAAET,0BAA0B,CAAC,CAAC;EAEnCU,KAAK,EAAE;IACLC,KAAK,EAAEA,CAAA,KAAM,IAAI;IACjB,cAAc,EAAEC,CAAA,KAAM;EACxB,CAAC;EAEDC,KAAKA,CAAEJ,KAAK,EAAAK,IAAA,EAAmB;IAAA,IAAjB;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAAF,IAAA;IAC3B,MAAM;MAAEG,sBAAsB;MAAEC;IAAsB,CAAC,GAAGzB,kBAAkB,CAAC,MAAMgB,KAAK,CAACP,KAAK,CAAC;IAE/F,SAASI,OAAOA,CAAA,EAAI;MAClBS,IAAI,CAAC,OAAO,CAAC;IACf;IAEA,SAASI,aAAaA,CAAA,EAAI;MACxBJ,IAAI,CAAC,cAAc,CAAC;IACtB;IAEAhB,SAAS,CAAC,MAAM;MACd,MAAMqB,UAAU,GAAG,CAAC,EAAEJ,KAAK,CAACK,OAAO,IAAIZ,KAAK,CAACL,MAAM,CAAC;MACpD,MAAMkB,SAAS,GAAG,CAAC,EAAEN,KAAK,CAACO,MAAM,IAAId,KAAK,CAACR,UAAU,CAAC;MAEtD,OAAAuB,mBAAA;QAAA,SAAAC,eAAA,CAEW,CACL,sBAAsB,EACtB;UACE,iCAAiC,EAAE,CAAC,CAAChB,KAAK,CAACH;QAC7C,CAAC,EACDW,sBAAsB,CAACS,KAAK,CAC7B;QAAA,SAAAC,eAAA,CACOT,qBAAqB,CAACQ,KAAK;QAAA,WACzBpB;MAAO,IAEfU,KAAK,CAACY,OAAO,IAAAJ,mBAAA;QAAA;QAAA;MAAA,IAETR,KAAK,CAACY,OAAO,CAAC,CAAC,EAEpB,EAECR,UAAU,IAAAS,YAAA,CAAAlC,eAAA;QAAA;QAAA,QAC4Bc,KAAK,CAACJ;MAAU;QAAAgB,OAAA,EAAAA,CAAA,MAAAG,mBAAA;UAAA,OACzCf,KAAK,CAACL,MAAM;UAAA;QAAA,IACnBY,KAAK,CAACK,OAAO,GAAG,CAAC,IAAIZ,KAAK,CAACL,MAAM;MAAA,EAGxC,EAECkB,SAAS,IAAAE,mBAAA;QAAA;MAAA,IAEL,CAACR,KAAK,CAACO,MAAM,GAAAM,YAAA,CAAAtC,IAAA;QAAA;QAAA,QAGJkB,KAAK,CAACR,UAAU;QAAA;QAAA,WAEbkB;MAAa,WAAAU,YAAA,CAAArC,iBAAA;QAAA;QAAA,YAKZ,CAACiB,KAAK,CAACR,UAAU;QAAA,YAClB;UACRV,IAAI,EAAE;YACJuC,IAAI,EAAErB,KAAK,CAACR,UAAU;YACtB8B,OAAO,EAAE;UACX;QACF;MAAC;QAAAV,OAAA,EAAAA,CAAA,MAECL,KAAK,CAACO,MAAM,GAAG,CAAC;MAAA,EAErB,EAEJ;IAGP,CAAC,CAAC;IAEF,OAAO,CAAC,CAAC;EACX;AACF,CAAC,CAAC", "ignoreList": []}