.v-date-picker-month {
  display: flex;
  justify-content: center;
  padding: 0 12px 8px;
  --v-date-picker-month-day-diff: 4px;
}

.v-date-picker-month__weeks {
  display: flex;
  flex-direction: column;
  column-gap: 4px;
  font-size: 0.85rem;
}

.v-date-picker-month__weekday {
  font-size: 0.85rem;
}

.v-date-picker-month__days {
  display: grid;
  grid-template-columns: repeat(var(--v-date-picker-days-in-week), min-content);
  column-gap: 4px;
}

.v-date-picker-month__day {
  align-items: center;
  display: flex;
  justify-content: center;
  position: relative;
  height: 40px;
  width: 40px;
}
.v-date-picker-month__day--selected .v-btn {
  background-color: rgb(var(--v-theme-surface-variant));
  color: rgb(var(--v-theme-on-surface-variant));
}
.v-date-picker-month__day .v-btn.v-date-picker-month__day-btn {
  --v-btn-height: 24px;
  --v-btn-size: 0.85rem;
}
.v-date-picker-month__day--week {
  font-size: var(--v-btn-size);
}

.v-date-picker-month__day--adjacent {
  opacity: 0.5;
}

.v-date-picker-month__day--hide-adjacent {
  opacity: 0;
}