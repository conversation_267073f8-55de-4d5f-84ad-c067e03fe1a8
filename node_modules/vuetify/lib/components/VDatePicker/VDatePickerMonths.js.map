{"version": 3, "file": "VDatePickerMonths.js", "names": ["VBtn", "useDate", "useProxiedModel", "computed", "watchEffect", "convertToUnit", "createRange", "genericComponent", "propsFactory", "useRender", "makeVDatePickerMonthsProps", "color", "String", "height", "Number", "min", "max", "modelValue", "year", "allowedMont<PERSON>", "Array", "Function", "VDatePickerMonths", "name", "props", "emits", "date", "setup", "_ref", "emit", "slots", "adapter", "model", "months", "startOfYear", "setYear", "map", "i", "text", "format", "isDisabled", "isMonthAllowed", "isAfter", "startOfMonth", "getNextMonth", "value", "getMonth", "month", "isArray", "length", "includes", "_createElementVNode", "btnProps", "active", "undefined", "disabled", "rounded", "variant", "onClick", "_createVNode", "_mergeProps"], "sources": ["../../../src/components/VDatePicker/VDatePickerMonths.tsx"], "sourcesContent": ["// Styles\nimport './VDatePickerMonths.sass'\n\n// Components\nimport { VBtn } from '@/components/VBtn'\n\n// Composables\nimport { useDate } from '@/composables/date'\nimport { useProxiedModel } from '@/composables/proxiedModel'\n\n// Utilities\nimport { computed, watchEffect } from 'vue'\nimport { convertToUnit, createRange, genericComponent, propsFactory, useRender } from '@/util'\n\n// Types\nimport type { PropType } from 'vue'\n\nexport type VDatePickerMonthsSlots = {\n  month: {\n    month: {\n      text: string\n      value: number\n    }\n    i: number\n    props: {\n      onClick: () => void\n    }\n  }\n}\n\nexport const makeVDatePickerMonthsProps = propsFactory({\n  color: String,\n  height: [String, Number],\n  min: null as any as PropType<unknown>,\n  max: null as any as PropType<unknown>,\n  modelValue: Number,\n  year: Number,\n  allowedMonths: [Array, Function] as PropType<number[] | ((date: number) => boolean)>,\n}, 'VDatePickerMonths')\n\nexport const VDatePickerMonths = genericComponent<VDatePickerMonthsSlots>()({\n  name: 'VDatePickerMonths',\n\n  props: makeVDatePickerMonthsProps(),\n\n  emits: {\n    'update:modelValue': (date: any) => true,\n  },\n\n  setup (props, { emit, slots }) {\n    const adapter = useDate()\n    const model = useProxiedModel(props, 'modelValue')\n\n    const months = computed(() => {\n      let date = adapter.startOfYear(adapter.date())\n      if (props.year) {\n        date = adapter.setYear(date, props.year)\n      }\n      return createRange(12).map(i => {\n        const text = adapter.format(date, 'monthShort')\n        const isDisabled =\n          !!(\n            !isMonthAllowed(i) ||\n            (props.min && adapter.isAfter(adapter.startOfMonth(adapter.date(props.min)), date)) ||\n            (props.max && adapter.isAfter(date, adapter.startOfMonth(adapter.date(props.max))))\n          )\n        date = adapter.getNextMonth(date)\n\n        return {\n          isDisabled,\n          text,\n          value: i,\n        }\n      })\n    })\n\n    watchEffect(() => {\n      model.value = model.value ?? adapter.getMonth(adapter.date())\n    })\n\n    function isMonthAllowed (month: number) {\n      if (Array.isArray(props.allowedMonths) && props.allowedMonths.length) {\n        return props.allowedMonths.includes(month)\n      }\n\n      if (typeof props.allowedMonths === 'function') {\n        return props.allowedMonths(month)\n      }\n\n      return true\n    }\n\n    useRender(() => (\n      <div\n        class=\"v-date-picker-months\"\n        style={{\n          height: convertToUnit(props.height),\n        }}\n      >\n        <div class=\"v-date-picker-months__content\">\n          { months.value.map((month, i) => {\n            const btnProps = {\n              active: model.value === i,\n              color: model.value === i ? props.color : undefined,\n              disabled: month.isDisabled,\n              rounded: true,\n              text: month.text,\n              variant: model.value === month.value ? 'flat' : 'text',\n              onClick: () => onClick(i),\n            } as const\n\n            function onClick (i: number) {\n              if (model.value === i) {\n                emit('update:modelValue', model.value)\n                return\n              }\n              model.value = i\n            }\n\n            return slots.month?.({\n              month,\n              i,\n              props: btnProps,\n            }) ?? (\n              <VBtn\n                key=\"month\"\n                { ...btnProps }\n              />\n            )\n          })}\n        </div>\n      </div>\n    ))\n\n    return {}\n  },\n})\n\nexport type VDatePickerMonths = InstanceType<typeof VDatePickerMonths>\n"], "mappings": ";AAAA;AACA;;AAEA;AAAA,SACSA,IAAI,4BAEb;AAAA,SACSC,OAAO;AAAA,SACPC,eAAe,6CAExB;AACA,SAASC,QAAQ,EAAEC,WAAW,QAAQ,KAAK;AAAA,SAClCC,aAAa,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,SAAS,+BAE9E;AAgBA,OAAO,MAAMC,0BAA0B,GAAGF,YAAY,CAAC;EACrDG,KAAK,EAAEC,MAAM;EACbC,MAAM,EAAE,CAACD,MAAM,EAAEE,MAAM,CAAC;EACxBC,GAAG,EAAE,IAAgC;EACrCC,GAAG,EAAE,IAAgC;EACrCC,UAAU,EAAEH,MAAM;EAClBI,IAAI,EAAEJ,MAAM;EACZK,aAAa,EAAE,CAACC,KAAK,EAAEC,QAAQ;AACjC,CAAC,EAAE,mBAAmB,CAAC;AAEvB,OAAO,MAAMC,iBAAiB,GAAGf,gBAAgB,CAAyB,CAAC,CAAC;EAC1EgB,IAAI,EAAE,mBAAmB;EAEzBC,KAAK,EAAEd,0BAA0B,CAAC,CAAC;EAEnCe,KAAK,EAAE;IACL,mBAAmB,EAAGC,IAAS,IAAK;EACtC,CAAC;EAEDC,KAAKA,CAAEH,KAAK,EAAAI,IAAA,EAAmB;IAAA,IAAjB;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAAF,IAAA;IAC3B,MAAMG,OAAO,GAAG9B,OAAO,CAAC,CAAC;IACzB,MAAM+B,KAAK,GAAG9B,eAAe,CAACsB,KAAK,EAAE,YAAY,CAAC;IAElD,MAAMS,MAAM,GAAG9B,QAAQ,CAAC,MAAM;MAC5B,IAAIuB,IAAI,GAAGK,OAAO,CAACG,WAAW,CAACH,OAAO,CAACL,IAAI,CAAC,CAAC,CAAC;MAC9C,IAAIF,KAAK,CAACN,IAAI,EAAE;QACdQ,IAAI,GAAGK,OAAO,CAACI,OAAO,CAACT,IAAI,EAAEF,KAAK,CAACN,IAAI,CAAC;MAC1C;MACA,OAAOZ,WAAW,CAAC,EAAE,CAAC,CAAC8B,GAAG,CAACC,CAAC,IAAI;QAC9B,MAAMC,IAAI,GAAGP,OAAO,CAACQ,MAAM,CAACb,IAAI,EAAE,YAAY,CAAC;QAC/C,MAAMc,UAAU,GACd,CAAC,EACC,CAACC,cAAc,CAACJ,CAAC,CAAC,IACjBb,KAAK,CAACT,GAAG,IAAIgB,OAAO,CAACW,OAAO,CAACX,OAAO,CAACY,YAAY,CAACZ,OAAO,CAACL,IAAI,CAACF,KAAK,CAACT,GAAG,CAAC,CAAC,EAAEW,IAAI,CAAE,IAClFF,KAAK,CAACR,GAAG,IAAIe,OAAO,CAACW,OAAO,CAAChB,IAAI,EAAEK,OAAO,CAACY,YAAY,CAACZ,OAAO,CAACL,IAAI,CAACF,KAAK,CAACR,GAAG,CAAC,CAAC,CAAE,CACpF;QACHU,IAAI,GAAGK,OAAO,CAACa,YAAY,CAAClB,IAAI,CAAC;QAEjC,OAAO;UACLc,UAAU;UACVF,IAAI;UACJO,KAAK,EAAER;QACT,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFjC,WAAW,CAAC,MAAM;MAChB4B,KAAK,CAACa,KAAK,GAAGb,KAAK,CAACa,KAAK,IAAId,OAAO,CAACe,QAAQ,CAACf,OAAO,CAACL,IAAI,CAAC,CAAC,CAAC;IAC/D,CAAC,CAAC;IAEF,SAASe,cAAcA,CAAEM,KAAa,EAAE;MACtC,IAAI3B,KAAK,CAAC4B,OAAO,CAACxB,KAAK,CAACL,aAAa,CAAC,IAAIK,KAAK,CAACL,aAAa,CAAC8B,MAAM,EAAE;QACpE,OAAOzB,KAAK,CAACL,aAAa,CAAC+B,QAAQ,CAACH,KAAK,CAAC;MAC5C;MAEA,IAAI,OAAOvB,KAAK,CAACL,aAAa,KAAK,UAAU,EAAE;QAC7C,OAAOK,KAAK,CAACL,aAAa,CAAC4B,KAAK,CAAC;MACnC;MAEA,OAAO,IAAI;IACb;IAEAtC,SAAS,CAAC,MAAA0C,mBAAA;MAAA;MAAA,SAGC;QACLtC,MAAM,EAAER,aAAa,CAACmB,KAAK,CAACX,MAAM;MACpC;IAAC,IAAAsC,mBAAA;MAAA;IAAA,IAGGlB,MAAM,CAACY,KAAK,CAACT,GAAG,CAAC,CAACW,KAAK,EAAEV,CAAC,KAAK;MAC/B,MAAMe,QAAQ,GAAG;QACfC,MAAM,EAAErB,KAAK,CAACa,KAAK,KAAKR,CAAC;QACzB1B,KAAK,EAAEqB,KAAK,CAACa,KAAK,KAAKR,CAAC,GAAGb,KAAK,CAACb,KAAK,GAAG2C,SAAS;QAClDC,QAAQ,EAAER,KAAK,CAACP,UAAU;QAC1BgB,OAAO,EAAE,IAAI;QACblB,IAAI,EAAES,KAAK,CAACT,IAAI;QAChBmB,OAAO,EAAEzB,KAAK,CAACa,KAAK,KAAKE,KAAK,CAACF,KAAK,GAAG,MAAM,GAAG,MAAM;QACtDa,OAAO,EAAEA,CAAA,KAAMA,OAAO,CAACrB,CAAC;MAC1B,CAAU;MAEV,SAASqB,OAAOA,CAAErB,CAAS,EAAE;QAC3B,IAAIL,KAAK,CAACa,KAAK,KAAKR,CAAC,EAAE;UACrBR,IAAI,CAAC,mBAAmB,EAAEG,KAAK,CAACa,KAAK,CAAC;UACtC;QACF;QACAb,KAAK,CAACa,KAAK,GAAGR,CAAC;MACjB;MAEA,OAAOP,KAAK,CAACiB,KAAK,GAAG;QACnBA,KAAK;QACLV,CAAC;QACDb,KAAK,EAAE4B;MACT,CAAC,CAAC,IAAAO,YAAA,CAAA3D,IAAA,EAAA4D,WAAA;QAAA;MAAA,GAGOR,QAAQ,QAEhB;IACH,CAAC,CAAC,IAGP,CAAC;IAEF,OAAO,CAAC,CAAC;EACX;AACF,CAAC,CAAC", "ignoreList": []}