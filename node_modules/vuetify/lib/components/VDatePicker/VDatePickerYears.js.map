{"version": 3, "file": "VDatePickerYears.js", "names": ["VBtn", "useDate", "useProxiedModel", "computed", "nextTick", "onMounted", "watchEffect", "convertToUnit", "createRange", "genericComponent", "propsFactory", "templateRef", "useRender", "makeVDatePickerYearsProps", "color", "String", "height", "Number", "min", "max", "modelValue", "<PERSON><PERSON><PERSON>s", "Array", "Function", "VDatePickerYears", "name", "props", "emits", "year", "setup", "_ref", "emit", "slots", "adapter", "model", "years", "getYear", "date", "startOfYear", "setYear", "map", "i", "text", "format", "value", "isDisabled", "isYearAllowed", "yearRef", "el", "focus", "isArray", "length", "includes", "_createElementVNode", "btnProps", "ref", "undefined", "active", "rounded", "disabled", "variant", "onClick", "_createVNode", "_mergeProps"], "sources": ["../../../src/components/VDatePicker/VDatePickerYears.tsx"], "sourcesContent": ["// Styles\nimport './VDatePickerYears.sass'\n\n// Components\nimport { VBtn } from '@/components/VBtn'\n\n// Composables\nimport { useDate } from '@/composables/date'\nimport { useProxiedModel } from '@/composables/proxiedModel'\n\n// Utilities\nimport { computed, nextTick, onMounted, watchEffect } from 'vue'\nimport { convertToUnit, createRange, genericComponent, propsFactory, templateRef, useRender } from '@/util'\n\n// Types\nimport type { PropType } from 'vue'\n\n// Types\nexport type VDatePickerYearsSlots = {\n  year: {\n    year: {\n      text: string\n      value: number\n    }\n    i: number\n    props: {\n      active: boolean\n      color?: string\n      rounded: boolean\n      text: string\n      variant: 'flat' | 'text'\n      onClick: () => void\n    }\n  }\n}\n\nexport const makeVDatePickerYearsProps = propsFactory({\n  color: String,\n  height: [String, Number],\n  min: null as any as PropType<unknown>,\n  max: null as any as PropType<unknown>,\n  modelValue: Number,\n  allowedYears: [Array, Function] as PropType<number[] | ((date: number) => boolean)>,\n}, 'VDatePickerYears')\n\nexport const VDatePickerYears = genericComponent<VDatePickerYearsSlots>()({\n  name: 'VDatePickerYears',\n\n  props: makeVDatePickerYearsProps(),\n\n  emits: {\n    'update:modelValue': (year: number) => true,\n  },\n\n  setup (props, { emit, slots }) {\n    const adapter = useDate()\n    const model = useProxiedModel(props, 'modelValue')\n    const years = computed(() => {\n      const year = adapter.getYear(adapter.date())\n\n      let min = year - 100\n      let max = year + 52\n\n      if (props.min) {\n        min = adapter.getYear(adapter.date(props.min))\n      }\n\n      if (props.max) {\n        max = adapter.getYear(adapter.date(props.max))\n      }\n\n      let date = adapter.startOfYear(adapter.date())\n\n      date = adapter.setYear(date, min)\n\n      return createRange(max - min + 1, min).map(i => {\n        const text = adapter.format(date, 'year')\n        date = adapter.setYear(date, adapter.getYear(date) + 1)\n\n        return {\n          text,\n          value: i,\n          isDisabled: !isYearAllowed(i),\n        }\n      })\n    })\n\n    watchEffect(() => {\n      model.value = model.value ?? adapter.getYear(adapter.date())\n    })\n\n    const yearRef = templateRef()\n\n    onMounted(async () => {\n      await nextTick()\n      yearRef.el?.focus()\n    })\n\n    function isYearAllowed (year: number) {\n      if (Array.isArray(props.allowedYears) && props.allowedYears.length) {\n        return props.allowedYears.includes(year)\n      }\n\n      if (typeof props.allowedYears === 'function') {\n        return props.allowedYears(year)\n      }\n\n      return true\n    }\n\n    useRender(() => (\n      <div\n        class=\"v-date-picker-years\"\n        style={{\n          height: convertToUnit(props.height),\n        }}\n      >\n        <div class=\"v-date-picker-years__content\">\n          { years.value.map((year, i) => {\n            const btnProps = {\n              ref: model.value === year.value ? yearRef : undefined,\n              active: model.value === year.value,\n              color: model.value === year.value ? props.color : undefined,\n              rounded: true,\n              text: year.text,\n              disabled: year.isDisabled,\n              variant: model.value === year.value ? 'flat' : 'text',\n              onClick: () => {\n                if (model.value === year.value) {\n                  emit('update:modelValue', model.value)\n                  return\n                }\n                model.value = year.value\n              },\n            } as const\n\n            return slots.year?.({\n              year,\n              i,\n              props: btnProps,\n            }) ?? (\n              <VBtn\n                key=\"month\"\n                { ...btnProps }\n              />\n            )\n          })}\n        </div>\n      </div>\n    ))\n\n    return {}\n  },\n})\n\nexport type VDatePickerYears = InstanceType<typeof VDatePickerYears>\n"], "mappings": ";AAAA;AACA;;AAEA;AAAA,SACSA,IAAI,4BAEb;AAAA,SACSC,OAAO;AAAA,SACPC,eAAe,6CAExB;AACA,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,KAAK;AAAA,SACvDC,aAAa,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,WAAW,EAAEC,SAAS,+BAE3F;AAGA;AAmBA,OAAO,MAAMC,yBAAyB,GAAGH,YAAY,CAAC;EACpDI,KAAK,EAAEC,MAAM;EACbC,MAAM,EAAE,CAACD,MAAM,EAAEE,MAAM,CAAC;EACxBC,GAAG,EAAE,IAAgC;EACrCC,GAAG,EAAE,IAAgC;EACrCC,UAAU,EAAEH,MAAM;EAClBI,YAAY,EAAE,CAACC,KAAK,EAAEC,QAAQ;AAChC,CAAC,EAAE,kBAAkB,CAAC;AAEtB,OAAO,MAAMC,gBAAgB,GAAGf,gBAAgB,CAAwB,CAAC,CAAC;EACxEgB,IAAI,EAAE,kBAAkB;EAExBC,KAAK,EAAEb,yBAAyB,CAAC,CAAC;EAElCc,KAAK,EAAE;IACL,mBAAmB,EAAGC,IAAY,IAAK;EACzC,CAAC;EAEDC,KAAKA,CAAEH,KAAK,EAAAI,IAAA,EAAmB;IAAA,IAAjB;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAAF,IAAA;IAC3B,MAAMG,OAAO,GAAGhC,OAAO,CAAC,CAAC;IACzB,MAAMiC,KAAK,GAAGhC,eAAe,CAACwB,KAAK,EAAE,YAAY,CAAC;IAClD,MAAMS,KAAK,GAAGhC,QAAQ,CAAC,MAAM;MAC3B,MAAMyB,IAAI,GAAGK,OAAO,CAACG,OAAO,CAACH,OAAO,CAACI,IAAI,CAAC,CAAC,CAAC;MAE5C,IAAInB,GAAG,GAAGU,IAAI,GAAG,GAAG;MACpB,IAAIT,GAAG,GAAGS,IAAI,GAAG,EAAE;MAEnB,IAAIF,KAAK,CAACR,GAAG,EAAE;QACbA,GAAG,GAAGe,OAAO,CAACG,OAAO,CAACH,OAAO,CAACI,IAAI,CAACX,KAAK,CAACR,GAAG,CAAC,CAAC;MAChD;MAEA,IAAIQ,KAAK,CAACP,GAAG,EAAE;QACbA,GAAG,GAAGc,OAAO,CAACG,OAAO,CAACH,OAAO,CAACI,IAAI,CAACX,KAAK,CAACP,GAAG,CAAC,CAAC;MAChD;MAEA,IAAIkB,IAAI,GAAGJ,OAAO,CAACK,WAAW,CAACL,OAAO,CAACI,IAAI,CAAC,CAAC,CAAC;MAE9CA,IAAI,GAAGJ,OAAO,CAACM,OAAO,CAACF,IAAI,EAAEnB,GAAG,CAAC;MAEjC,OAAOV,WAAW,CAACW,GAAG,GAAGD,GAAG,GAAG,CAAC,EAAEA,GAAG,CAAC,CAACsB,GAAG,CAACC,CAAC,IAAI;QAC9C,MAAMC,IAAI,GAAGT,OAAO,CAACU,MAAM,CAACN,IAAI,EAAE,MAAM,CAAC;QACzCA,IAAI,GAAGJ,OAAO,CAACM,OAAO,CAACF,IAAI,EAAEJ,OAAO,CAACG,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEvD,OAAO;UACLK,IAAI;UACJE,KAAK,EAAEH,CAAC;UACRI,UAAU,EAAE,CAACC,aAAa,CAACL,CAAC;QAC9B,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFnC,WAAW,CAAC,MAAM;MAChB4B,KAAK,CAACU,KAAK,GAAGV,KAAK,CAACU,KAAK,IAAIX,OAAO,CAACG,OAAO,CAACH,OAAO,CAACI,IAAI,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC;IAEF,MAAMU,OAAO,GAAGpC,WAAW,CAAC,CAAC;IAE7BN,SAAS,CAAC,YAAY;MACpB,MAAMD,QAAQ,CAAC,CAAC;MAChB2C,OAAO,CAACC,EAAE,EAAEC,KAAK,CAAC,CAAC;IACrB,CAAC,CAAC;IAEF,SAASH,aAAaA,CAAElB,IAAY,EAAE;MACpC,IAAIN,KAAK,CAAC4B,OAAO,CAACxB,KAAK,CAACL,YAAY,CAAC,IAAIK,KAAK,CAACL,YAAY,CAAC8B,MAAM,EAAE;QAClE,OAAOzB,KAAK,CAACL,YAAY,CAAC+B,QAAQ,CAACxB,IAAI,CAAC;MAC1C;MAEA,IAAI,OAAOF,KAAK,CAACL,YAAY,KAAK,UAAU,EAAE;QAC5C,OAAOK,KAAK,CAACL,YAAY,CAACO,IAAI,CAAC;MACjC;MAEA,OAAO,IAAI;IACb;IAEAhB,SAAS,CAAC,MAAAyC,mBAAA;MAAA;MAAA,SAGC;QACLrC,MAAM,EAAET,aAAa,CAACmB,KAAK,CAACV,MAAM;MACpC;IAAC,IAAAqC,mBAAA;MAAA;IAAA,IAGGlB,KAAK,CAACS,KAAK,CAACJ,GAAG,CAAC,CAACZ,IAAI,EAAEa,CAAC,KAAK;MAC7B,MAAMa,QAAQ,GAAG;QACfC,GAAG,EAAErB,KAAK,CAACU,KAAK,KAAKhB,IAAI,CAACgB,KAAK,GAAGG,OAAO,GAAGS,SAAS;QACrDC,MAAM,EAAEvB,KAAK,CAACU,KAAK,KAAKhB,IAAI,CAACgB,KAAK;QAClC9B,KAAK,EAAEoB,KAAK,CAACU,KAAK,KAAKhB,IAAI,CAACgB,KAAK,GAAGlB,KAAK,CAACZ,KAAK,GAAG0C,SAAS;QAC3DE,OAAO,EAAE,IAAI;QACbhB,IAAI,EAAEd,IAAI,CAACc,IAAI;QACfiB,QAAQ,EAAE/B,IAAI,CAACiB,UAAU;QACzBe,OAAO,EAAE1B,KAAK,CAACU,KAAK,KAAKhB,IAAI,CAACgB,KAAK,GAAG,MAAM,GAAG,MAAM;QACrDiB,OAAO,EAAEA,CAAA,KAAM;UACb,IAAI3B,KAAK,CAACU,KAAK,KAAKhB,IAAI,CAACgB,KAAK,EAAE;YAC9Bb,IAAI,CAAC,mBAAmB,EAAEG,KAAK,CAACU,KAAK,CAAC;YACtC;UACF;UACAV,KAAK,CAACU,KAAK,GAAGhB,IAAI,CAACgB,KAAK;QAC1B;MACF,CAAU;MAEV,OAAOZ,KAAK,CAACJ,IAAI,GAAG;QAClBA,IAAI;QACJa,CAAC;QACDf,KAAK,EAAE4B;MACT,CAAC,CAAC,IAAAQ,YAAA,CAAA9D,IAAA,EAAA+D,WAAA;QAAA;MAAA,GAGOT,QAAQ,QAEhB;IACH,CAAC,CAAC,IAGP,CAAC;IAEF,OAAO,CAAC,CAAC;EACX;AACF,CAAC,CAAC", "ignoreList": []}