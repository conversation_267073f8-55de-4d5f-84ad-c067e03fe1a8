.v-date-picker-header {
  align-items: flex-end;
  height: 70px;
  display: grid;
  grid-template-areas: "prepend content append";
  grid-template-columns: min-content minmax(0, 1fr) min-content;
  overflow: hidden;
  padding-inline: 24px 12px;
  padding-bottom: 12px;
}

.v-date-picker-header__append {
  grid-area: append;
}

.v-date-picker-header__prepend {
  grid-area: prepend;
  padding-inline-start: 8px;
}

.v-date-picker-header__content {
  align-items: center;
  display: inline-flex;
  font-size: 32px;
  line-height: 40px;
  grid-area: content;
  justify-content: space-between;
}
.v-date-picker-header--clickable .v-date-picker-header__content {
  cursor: pointer;
}
.v-date-picker-header--clickable .v-date-picker-header__content:not(:hover) {
  opacity: 0.7;
}

.date-picker-header-transition-enter-active,
.date-picker-header-reverse-transition-enter-active {
  transition-duration: 0.3s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.date-picker-header-transition-leave-active,
.date-picker-header-reverse-transition-leave-active {
  transition-duration: 0.3s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.date-picker-header-transition-enter-from {
  transform: translate(0, 100%);
}
.date-picker-header-transition-leave-to {
  opacity: 0;
  transform: translate(0, -100%);
}

.date-picker-header-reverse-transition-enter-from {
  transform: translate(0, -100%);
}
.date-picker-header-reverse-transition-leave-to {
  opacity: 0;
  transform: translate(0, 100%);
}