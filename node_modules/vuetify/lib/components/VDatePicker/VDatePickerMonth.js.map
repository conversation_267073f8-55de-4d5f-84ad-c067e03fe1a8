{"version": 3, "file": "VDatePickerMonth.js", "names": ["VBtn", "makeCalendarProps", "useCalendar", "createDateRange", "useDate", "MaybeTransition", "computed", "ref", "shallowRef", "toRef", "watch", "genericComponent", "omit", "propsFactory", "useRender", "makeVDatePickerMonthProps", "color", "String", "hideWeekdays", "Boolean", "multiple", "Number", "showWeek", "transition", "type", "default", "reverseTransition", "VDatePickerMonth", "name", "props", "emits", "date", "setup", "_ref", "emit", "slots", "daysRef", "daysInMonth", "model", "weekNumbers", "weekdayLabels", "adapter", "rangeStart", "rangeStop", "isReverse", "value", "length", "atMax", "max", "includes", "Infinity", "val", "oldVal", "isBefore", "onRangeClick", "_value", "startOfDay", "undefined", "isSameDay", "endOfDay", "onMultipleClick", "index", "findIndex", "selection", "splice", "onClick", "_createElementVNode", "weekdays", "_createTextVNode", "map", "week", "_normalizeClass", "_createVNode", "toString", "weekDay", "item", "i", "slotProps", "class", "isSelected", "isToday", "disabled", "isDisabled", "icon", "ripple", "text", "localized", "variant", "isAd<PERSON><PERSON>", "isHidden", "isWeekEnd", "isWeekStart", "isoDate", "showAdjacentMonths", "day"], "sources": ["../../../src/components/VDatePicker/VDatePickerMonth.tsx"], "sourcesContent": ["// Styles\nimport './VDatePickerMonth.sass'\n\n// Components\nimport { VBtn } from '@/components/VBtn'\n\n// Composables\nimport { makeCalendarProps, useCalendar } from '@/composables/calendar'\nimport { createDateRange, useDate } from '@/composables/date/date'\nimport { MaybeTransition } from '@/composables/transition'\n\n// Utilities\nimport { computed, ref, shallowRef, toRef, watch } from 'vue'\nimport { genericComponent, omit, propsFactory, useRender } from '@/util'\n\n// Types\nimport type { PropType } from 'vue'\n\nexport type VDatePickerMonthSlots = {\n  day: {\n    props: {\n      onClick: () => void\n    }\n    item: any\n    i: number\n  }\n}\n\nexport const makeVDatePickerMonthProps = propsFactory({\n  color: String,\n  hideWeekdays: Boolean,\n  multiple: [Boolean, Number, String] as PropType<boolean | 'range' | number | (string & {})>,\n  showWeek: Boolean,\n  transition: {\n    type: String,\n    default: 'picker-transition',\n  },\n  reverseTransition: {\n    type: String,\n    default: 'picker-reverse-transition',\n  },\n\n  ...omit(makeCalendarProps(), ['displayValue']),\n}, 'VDatePickerMonth')\n\nexport const VDatePickerMonth = genericComponent<VDatePickerMonthSlots>()({\n  name: 'VDatePickerMonth',\n\n  props: makeVDatePickerMonthProps(),\n\n  emits: {\n    'update:modelValue': (date: unknown) => true,\n    'update:month': (date: number) => true,\n    'update:year': (date: number) => true,\n  },\n\n  setup (props, { emit, slots }) {\n    const daysRef = ref()\n\n    const { daysInMonth, model, weekNumbers, weekdayLabels } = useCalendar(props)\n    const adapter = useDate()\n\n    const rangeStart = shallowRef()\n    const rangeStop = shallowRef()\n    const isReverse = shallowRef(false)\n\n    const transition = toRef(() => {\n      return !isReverse.value ? props.transition : props.reverseTransition\n    })\n\n    if (props.multiple === 'range' && model.value.length > 0) {\n      rangeStart.value = model.value[0]\n      if (model.value.length > 1) {\n        rangeStop.value = model.value[model.value.length - 1]\n      }\n    }\n\n    const atMax = computed(() => {\n      const max = ['number', 'string'].includes(typeof props.multiple) ? Number(props.multiple) : Infinity\n\n      return model.value.length >= max\n    })\n\n    watch(daysInMonth, (val, oldVal) => {\n      if (!oldVal) return\n\n      isReverse.value = adapter.isBefore(val[0].date, oldVal[0].date)\n    })\n\n    function onRangeClick (value: unknown) {\n      const _value = adapter.startOfDay(value)\n\n      if (model.value.length === 0) {\n        rangeStart.value = undefined\n      } else if (model.value.length === 1) {\n        rangeStart.value = model.value[0]\n        rangeStop.value = undefined\n      }\n      if (!rangeStart.value) {\n        rangeStart.value = _value\n        model.value = [rangeStart.value]\n      } else if (!rangeStop.value) {\n        if (adapter.isSameDay(_value, rangeStart.value)) {\n          rangeStart.value = undefined\n          model.value = []\n          return\n        } else if (adapter.isBefore(_value, rangeStart.value)) {\n          rangeStop.value = adapter.endOfDay(rangeStart.value)\n          rangeStart.value = _value\n        } else {\n          rangeStop.value = adapter.endOfDay(_value)\n        }\n\n        model.value = createDateRange(adapter, rangeStart.value, rangeStop.value)\n      } else {\n        rangeStart.value = value\n        rangeStop.value = undefined\n        model.value = [rangeStart.value]\n      }\n    }\n\n    function onMultipleClick (value: unknown) {\n      const index = model.value.findIndex(selection => adapter.isSameDay(selection, value))\n\n      if (index === -1) {\n        model.value = [...model.value, value]\n      } else {\n        const value = [...model.value]\n        value.splice(index, 1)\n        model.value = value\n      }\n    }\n\n    function onClick (value: unknown) {\n      if (props.multiple === 'range') {\n        onRangeClick(value)\n      } else if (props.multiple) {\n        onMultipleClick(value)\n      } else {\n        model.value = [value]\n      }\n    }\n\n    useRender(() => (\n      <div\n        class=\"v-date-picker-month\"\n        style={{ '--v-date-picker-days-in-week': props.weekdays.length }}\n      >\n        { props.showWeek && (\n          <div key=\"weeks\" class=\"v-date-picker-month__weeks\">\n            { !props.hideWeekdays && (\n              <div key=\"hide-week-days\" class=\"v-date-picker-month__day\">&nbsp;</div>\n            )}\n            { weekNumbers.value.map(week => (\n              <div\n                class={[\n                  'v-date-picker-month__day',\n                  'v-date-picker-month__day--adjacent',\n                ]}\n              >{ week }</div>\n            ))}\n          </div>\n        )}\n\n        <MaybeTransition name={ transition.value }>\n          <div\n            ref={ daysRef }\n            key={ daysInMonth.value[0].date?.toString() }\n            class=\"v-date-picker-month__days\"\n          >\n            { !props.hideWeekdays && weekdayLabels.value.map(weekDay => (\n              <div\n                class={[\n                  'v-date-picker-month__day',\n                  'v-date-picker-month__weekday',\n                ]}\n              >{ weekDay }</div>\n            ))}\n\n            { daysInMonth.value.map((item, i) => {\n              const slotProps = {\n                props: {\n                  class: 'v-date-picker-month__day-btn',\n                  color: item.isSelected || item.isToday ? props.color : undefined,\n                  disabled: item.isDisabled,\n                  icon: true,\n                  ripple: false,\n                  text: item.localized,\n                  variant: item.isSelected ? 'flat' : item.isToday ? 'outlined' : 'text',\n                  onClick: () => onClick(item.date),\n                },\n                item,\n                i,\n              } as const\n\n              if (atMax.value && !item.isSelected) {\n                item.isDisabled = true\n              }\n\n              return (\n                <div\n                  class={[\n                    'v-date-picker-month__day',\n                    {\n                      'v-date-picker-month__day--adjacent': item.isAdjacent,\n                      'v-date-picker-month__day--hide-adjacent': item.isHidden,\n                      'v-date-picker-month__day--selected': item.isSelected,\n                      'v-date-picker-month__day--week-end': item.isWeekEnd,\n                      'v-date-picker-month__day--week-start': item.isWeekStart,\n                    },\n                  ]}\n                  data-v-date={ !item.isDisabled ? item.isoDate : undefined }\n                >\n                  { (props.showAdjacentMonths || !item.isAdjacent) && (\n                    slots.day?.(slotProps) ?? (<VBtn { ...slotProps.props } />)\n                  )}\n                </div>\n              )\n            })}\n          </div>\n        </MaybeTransition>\n      </div>\n    ))\n  },\n})\n\nexport type VDatePickerMonth = InstanceType<typeof VDatePickerMonth>\n"], "mappings": ";AAAA;AACA;;AAEA;AAAA,SACSA,IAAI,4BAEb;AAAA,SACSC,iBAAiB,EAAEC,WAAW;AAAA,SAC9BC,eAAe,EAAEC,OAAO;AAAA,SACxBC,eAAe,2CAExB;AACA,SAASC,QAAQ,EAAEC,GAAG,EAAEC,UAAU,EAAEC,KAAK,EAAEC,KAAK,QAAQ,KAAK;AAAA,SACpDC,gBAAgB,EAAEC,IAAI,EAAEC,YAAY,EAAEC,SAAS,+BAExD;AAaA,OAAO,MAAMC,yBAAyB,GAAGF,YAAY,CAAC;EACpDG,KAAK,EAAEC,MAAM;EACbC,YAAY,EAAEC,OAAO;EACrBC,QAAQ,EAAE,CAACD,OAAO,EAAEE,MAAM,EAAEJ,MAAM,CAAyD;EAC3FK,QAAQ,EAAEH,OAAO;EACjBI,UAAU,EAAE;IACVC,IAAI,EAAEP,MAAM;IACZQ,OAAO,EAAE;EACX,CAAC;EACDC,iBAAiB,EAAE;IACjBF,IAAI,EAAEP,MAAM;IACZQ,OAAO,EAAE;EACX,CAAC;EAED,GAAGb,IAAI,CAACX,iBAAiB,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC;AAC/C,CAAC,EAAE,kBAAkB,CAAC;AAEtB,OAAO,MAAM0B,gBAAgB,GAAGhB,gBAAgB,CAAwB,CAAC,CAAC;EACxEiB,IAAI,EAAE,kBAAkB;EAExBC,KAAK,EAAEd,yBAAyB,CAAC,CAAC;EAElCe,KAAK,EAAE;IACL,mBAAmB,EAAGC,IAAa,IAAK,IAAI;IAC5C,cAAc,EAAGA,IAAY,IAAK,IAAI;IACtC,aAAa,EAAGA,IAAY,IAAK;EACnC,CAAC;EAEDC,KAAKA,CAAEH,KAAK,EAAAI,IAAA,EAAmB;IAAA,IAAjB;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAAF,IAAA;IAC3B,MAAMG,OAAO,GAAG7B,GAAG,CAAC,CAAC;IAErB,MAAM;MAAE8B,WAAW;MAAEC,KAAK;MAAEC,WAAW;MAAEC;IAAc,CAAC,GAAGtC,WAAW,CAAC2B,KAAK,CAAC;IAC7E,MAAMY,OAAO,GAAGrC,OAAO,CAAC,CAAC;IAEzB,MAAMsC,UAAU,GAAGlC,UAAU,CAAC,CAAC;IAC/B,MAAMmC,SAAS,GAAGnC,UAAU,CAAC,CAAC;IAC9B,MAAMoC,SAAS,GAAGpC,UAAU,CAAC,KAAK,CAAC;IAEnC,MAAMe,UAAU,GAAGd,KAAK,CAAC,MAAM;MAC7B,OAAO,CAACmC,SAAS,CAACC,KAAK,GAAGhB,KAAK,CAACN,UAAU,GAAGM,KAAK,CAACH,iBAAiB;IACtE,CAAC,CAAC;IAEF,IAAIG,KAAK,CAACT,QAAQ,KAAK,OAAO,IAAIkB,KAAK,CAACO,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MACxDJ,UAAU,CAACG,KAAK,GAAGP,KAAK,CAACO,KAAK,CAAC,CAAC,CAAC;MACjC,IAAIP,KAAK,CAACO,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;QAC1BH,SAAS,CAACE,KAAK,GAAGP,KAAK,CAACO,KAAK,CAACP,KAAK,CAACO,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC;MACvD;IACF;IAEA,MAAMC,KAAK,GAAGzC,QAAQ,CAAC,MAAM;MAC3B,MAAM0C,GAAG,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAAC,OAAOpB,KAAK,CAACT,QAAQ,CAAC,GAAGC,MAAM,CAACQ,KAAK,CAACT,QAAQ,CAAC,GAAG8B,QAAQ;MAEpG,OAAOZ,KAAK,CAACO,KAAK,CAACC,MAAM,IAAIE,GAAG;IAClC,CAAC,CAAC;IAEFtC,KAAK,CAAC2B,WAAW,EAAE,CAACc,GAAG,EAAEC,MAAM,KAAK;MAClC,IAAI,CAACA,MAAM,EAAE;MAEbR,SAAS,CAACC,KAAK,GAAGJ,OAAO,CAACY,QAAQ,CAACF,GAAG,CAAC,CAAC,CAAC,CAACpB,IAAI,EAAEqB,MAAM,CAAC,CAAC,CAAC,CAACrB,IAAI,CAAC;IACjE,CAAC,CAAC;IAEF,SAASuB,YAAYA,CAAET,KAAc,EAAE;MACrC,MAAMU,MAAM,GAAGd,OAAO,CAACe,UAAU,CAACX,KAAK,CAAC;MAExC,IAAIP,KAAK,CAACO,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QAC5BJ,UAAU,CAACG,KAAK,GAAGY,SAAS;MAC9B,CAAC,MAAM,IAAInB,KAAK,CAACO,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACnCJ,UAAU,CAACG,KAAK,GAAGP,KAAK,CAACO,KAAK,CAAC,CAAC,CAAC;QACjCF,SAAS,CAACE,KAAK,GAAGY,SAAS;MAC7B;MACA,IAAI,CAACf,UAAU,CAACG,KAAK,EAAE;QACrBH,UAAU,CAACG,KAAK,GAAGU,MAAM;QACzBjB,KAAK,CAACO,KAAK,GAAG,CAACH,UAAU,CAACG,KAAK,CAAC;MAClC,CAAC,MAAM,IAAI,CAACF,SAAS,CAACE,KAAK,EAAE;QAC3B,IAAIJ,OAAO,CAACiB,SAAS,CAACH,MAAM,EAAEb,UAAU,CAACG,KAAK,CAAC,EAAE;UAC/CH,UAAU,CAACG,KAAK,GAAGY,SAAS;UAC5BnB,KAAK,CAACO,KAAK,GAAG,EAAE;UAChB;QACF,CAAC,MAAM,IAAIJ,OAAO,CAACY,QAAQ,CAACE,MAAM,EAAEb,UAAU,CAACG,KAAK,CAAC,EAAE;UACrDF,SAAS,CAACE,KAAK,GAAGJ,OAAO,CAACkB,QAAQ,CAACjB,UAAU,CAACG,KAAK,CAAC;UACpDH,UAAU,CAACG,KAAK,GAAGU,MAAM;QAC3B,CAAC,MAAM;UACLZ,SAAS,CAACE,KAAK,GAAGJ,OAAO,CAACkB,QAAQ,CAACJ,MAAM,CAAC;QAC5C;QAEAjB,KAAK,CAACO,KAAK,GAAG1C,eAAe,CAACsC,OAAO,EAAEC,UAAU,CAACG,KAAK,EAAEF,SAAS,CAACE,KAAK,CAAC;MAC3E,CAAC,MAAM;QACLH,UAAU,CAACG,KAAK,GAAGA,KAAK;QACxBF,SAAS,CAACE,KAAK,GAAGY,SAAS;QAC3BnB,KAAK,CAACO,KAAK,GAAG,CAACH,UAAU,CAACG,KAAK,CAAC;MAClC;IACF;IAEA,SAASe,eAAeA,CAAEf,KAAc,EAAE;MACxC,MAAMgB,KAAK,GAAGvB,KAAK,CAACO,KAAK,CAACiB,SAAS,CAACC,SAAS,IAAItB,OAAO,CAACiB,SAAS,CAACK,SAAS,EAAElB,KAAK,CAAC,CAAC;MAErF,IAAIgB,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBvB,KAAK,CAACO,KAAK,GAAG,CAAC,GAAGP,KAAK,CAACO,KAAK,EAAEA,KAAK,CAAC;MACvC,CAAC,MAAM;QACL,MAAMA,KAAK,GAAG,CAAC,GAAGP,KAAK,CAACO,KAAK,CAAC;QAC9BA,KAAK,CAACmB,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;QACtBvB,KAAK,CAACO,KAAK,GAAGA,KAAK;MACrB;IACF;IAEA,SAASoB,OAAOA,CAAEpB,KAAc,EAAE;MAChC,IAAIhB,KAAK,CAACT,QAAQ,KAAK,OAAO,EAAE;QAC9BkC,YAAY,CAACT,KAAK,CAAC;MACrB,CAAC,MAAM,IAAIhB,KAAK,CAACT,QAAQ,EAAE;QACzBwC,eAAe,CAACf,KAAK,CAAC;MACxB,CAAC,MAAM;QACLP,KAAK,CAACO,KAAK,GAAG,CAACA,KAAK,CAAC;MACvB;IACF;IAEA/B,SAAS,CAAC,MAAAoD,mBAAA;MAAA;MAAA,SAGC;QAAE,8BAA8B,EAAErC,KAAK,CAACsC,QAAQ,CAACrB;MAAO;IAAC,IAE9DjB,KAAK,CAACP,QAAQ,IAAA4C,mBAAA;MAAA;MAAA;IAAA,IAEV,CAACrC,KAAK,CAACX,YAAY,IAAAgD,mBAAA;MAAA;MAAA;IAAA,IAAAE,gBAAA,UAEpB,EACC7B,WAAW,CAACM,KAAK,CAACwB,GAAG,CAACC,IAAI,IAAAJ,mBAAA;MAAA,SAAAK,eAAA,CAEjB,CACL,0BAA0B,EAC1B,oCAAoC,CACrC;IAAA,IACAD,IAAI,EACR,CAAC,EAEL,EAAAE,YAAA,CAAAnE,eAAA;MAAA,QAEuBkB,UAAU,CAACsB;IAAK;MAAApB,OAAA,EAAAA,CAAA,MAAAyC,mBAAA;QAAA,OAE9B9B,OAAO;QAAA,OACPC,WAAW,CAACQ,KAAK,CAAC,CAAC,CAAC,CAACd,IAAI,EAAE0C,QAAQ,CAAC,CAAC;QAAA;MAAA,IAGzC,CAAC5C,KAAK,CAACX,YAAY,IAAIsB,aAAa,CAACK,KAAK,CAACwB,GAAG,CAACK,OAAO,IAAAR,mBAAA;QAAA,SAAAK,eAAA,CAE7C,CACL,0BAA0B,EAC1B,8BAA8B,CAC/B;MAAA,IACAG,OAAO,EACX,CAAC,EAEArC,WAAW,CAACQ,KAAK,CAACwB,GAAG,CAAC,CAACM,IAAI,EAAEC,CAAC,KAAK;QACnC,MAAMC,SAAS,GAAG;UAChBhD,KAAK,EAAE;YACLiD,KAAK,EAAE,8BAA8B;YACrC9D,KAAK,EAAE2D,IAAI,CAACI,UAAU,IAAIJ,IAAI,CAACK,OAAO,GAAGnD,KAAK,CAACb,KAAK,GAAGyC,SAAS;YAChEwB,QAAQ,EAAEN,IAAI,CAACO,UAAU;YACzBC,IAAI,EAAE,IAAI;YACVC,MAAM,EAAE,KAAK;YACbC,IAAI,EAAEV,IAAI,CAACW,SAAS;YACpBC,OAAO,EAAEZ,IAAI,CAACI,UAAU,GAAG,MAAM,GAAGJ,IAAI,CAACK,OAAO,GAAG,UAAU,GAAG,MAAM;YACtEf,OAAO,EAAEA,CAAA,KAAMA,OAAO,CAACU,IAAI,CAAC5C,IAAI;UAClC,CAAC;UACD4C,IAAI;UACJC;QACF,CAAU;QAEV,IAAI7B,KAAK,CAACF,KAAK,IAAI,CAAC8B,IAAI,CAACI,UAAU,EAAE;UACnCJ,IAAI,CAACO,UAAU,GAAG,IAAI;QACxB;QAEA,OAAAhB,mBAAA;UAAA,SAAAK,eAAA,CAEW,CACL,0BAA0B,EAC1B;YACE,oCAAoC,EAAEI,IAAI,CAACa,UAAU;YACrD,yCAAyC,EAAEb,IAAI,CAACc,QAAQ;YACxD,oCAAoC,EAAEd,IAAI,CAACI,UAAU;YACrD,oCAAoC,EAAEJ,IAAI,CAACe,SAAS;YACpD,sCAAsC,EAAEf,IAAI,CAACgB;UAC/C,CAAC,CACF;UAAA,eACa,CAAChB,IAAI,CAACO,UAAU,GAAGP,IAAI,CAACiB,OAAO,GAAGnC;QAAS,IAEvD,CAAC5B,KAAK,CAACgE,kBAAkB,IAAI,CAAClB,IAAI,CAACa,UAAU,MAC7CrD,KAAK,CAAC2D,GAAG,GAAGjB,SAAS,CAAC,IAAAL,YAAA,CAAAxE,IAAA,EAAgB6E,SAAS,CAAChD,KAAK,OAAM,CAC5D;MAGP,CAAC,CAAC;IAAA,IAIT,CAAC;EACJ;AACF,CAAC,CAAC", "ignoreList": []}