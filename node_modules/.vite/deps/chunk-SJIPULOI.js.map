{"version": 3, "sources": ["../../vuetify/src/composables/icons.tsx", "../../vuetify/src/iconsets/mdi.ts"], "sourcesContent": ["// Icons\nimport { aliases, mdi } from '@/iconsets/mdi'\n\n// Utilities\nimport { computed, inject, toValue } from 'vue'\nimport { consoleWarn, defineComponent, genericComponent, mergeDeep, propsFactory } from '@/util'\n\n// Types\nimport type { InjectionKey, MaybeRefOrGetter, PropType } from 'vue'\nimport type { JSXComponent } from '@/util'\n\nexport type IconValue =\n  | string\n  | (string | [path: string, opacity: number])[]\n  | JSXComponent\nexport const IconValue = [String, Function, Object, Array] as PropType<IconValue>\n\nexport interface IconAliases {\n  [name: string]: IconValue\n  collapse: IconValue\n  complete: IconValue\n  cancel: IconValue\n  close: IconValue\n  delete: IconValue\n  clear: IconValue\n  success: IconValue\n  info: IconValue\n  warning: IconValue\n  error: IconValue\n  prev: IconValue\n  next: IconValue\n  checkboxOn: IconValue\n  checkboxOff: IconValue\n  checkboxIndeterminate: IconValue\n  delimiter: IconValue\n  sortAsc: IconValue\n  sortDesc: IconValue\n  expand: IconValue\n  menu: IconValue\n  subgroup: IconValue\n  dropdown: IconValue\n  radioOn: IconValue\n  radioOff: IconValue\n  edit: IconValue\n  ratingEmpty: IconValue\n  ratingFull: IconValue\n  ratingHalf: IconValue\n  loading: IconValue\n  first: IconValue\n  last: IconValue\n  unfold: IconValue\n  file: IconValue\n  plus: IconValue\n  minus: IconValue\n  calendar: IconValue\n  treeviewCollapse: IconValue\n  treeviewExpand: IconValue\n  eyeDropper: IconValue\n  upload: IconValue\n  color: IconValue\n}\n\nexport interface IconProps {\n  tag: string | JSXComponent\n  icon?: IconValue\n  disabled?: Boolean\n}\n\ntype IconComponent = JSXComponent<IconProps>\n\nexport interface IconSet {\n  component: IconComponent\n}\n\nexport type InternalIconOptions = {\n  defaultSet: string\n  aliases: Partial<IconAliases>\n  sets: Record<string, IconSet>\n}\n\nexport type IconOptions = Partial<InternalIconOptions>\n\ntype IconInstance = {\n  component: IconComponent\n  icon?: IconValue\n}\n\nexport const IconSymbol: InjectionKey<InternalIconOptions> = Symbol.for('vuetify:icons')\n\nexport const makeIconProps = propsFactory({\n  icon: {\n    type: IconValue,\n  },\n  // Could not remove this and use makeTagProps, types complained because it is not required\n  tag: {\n    type: [String, Object, Function] as PropType<string | JSXComponent>,\n    required: true,\n  },\n}, 'icon')\n\nexport const VComponentIcon = genericComponent()({\n  name: 'VComponentIcon',\n\n  props: makeIconProps(),\n\n  setup (props, { slots }) {\n    return () => {\n      const Icon = props.icon as JSXComponent\n      return (\n        <props.tag>\n          { props.icon ? <Icon /> : slots.default?.() }\n        </props.tag>\n      )\n    }\n  },\n})\nexport type VComponentIcon = InstanceType<typeof VComponentIcon>\n\nexport const VSvgIcon = defineComponent({\n  name: 'VSvgIcon',\n\n  inheritAttrs: false,\n\n  props: makeIconProps(),\n\n  setup (props, { attrs }) {\n    return () => {\n      return (\n        <props.tag { ...attrs } style={ null }>\n          <svg\n            class=\"v-icon__svg\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            viewBox=\"0 0 24 24\"\n            role=\"img\"\n            aria-hidden=\"true\"\n          >\n            { Array.isArray(props.icon)\n              ? props.icon.map(path => (\n                Array.isArray(path)\n                  ? <path d={ path[0] as string } fill-opacity={ path[1] }></path>\n                  : <path d={ path as string }></path>\n              ))\n              : <path d={ props.icon as string }></path>\n            }\n          </svg>\n        </props.tag>\n      )\n    }\n  },\n})\nexport type VSvgIcon = InstanceType<typeof VSvgIcon>\n\nexport const VLigatureIcon = defineComponent({\n  name: 'VLigatureIcon',\n\n  props: makeIconProps(),\n\n  setup (props) {\n    return () => {\n      return <props.tag>{ props.icon }</props.tag>\n    }\n  },\n})\nexport type VLigatureIcon = InstanceType<typeof VLigatureIcon>\n\nexport const VClassIcon = defineComponent({\n  name: 'VClassIcon',\n\n  props: makeIconProps(),\n\n  setup (props) {\n    return () => {\n      return <props.tag class={ props.icon }></props.tag>\n    }\n  },\n})\nexport type VClassIcon = InstanceType<typeof VClassIcon>\n\nfunction genDefaults (): Record<string, IconSet> {\n  return {\n    svg: {\n      component: VSvgIcon,\n    },\n    class: {\n      component: VClassIcon,\n    },\n  }\n}\n\n// Composables\nexport function createIcons (options?: IconOptions) {\n  const sets = genDefaults()\n  const defaultSet = options?.defaultSet ?? 'mdi'\n\n  if (defaultSet === 'mdi' && !sets.mdi) {\n    sets.mdi = mdi\n  }\n\n  return mergeDeep({\n    defaultSet,\n    sets,\n    aliases: {\n      ...aliases,\n      /* eslint-disable max-len */\n      vuetify: [\n        'M8.2241 14.2009L12 21L22 3H14.4459L8.2241 14.2009Z',\n        ['M7.26303 12.4733L7.00113 12L2 3H12.5261C12.5261 3 12.5261 3 12.5261 3L7.26303 12.4733Z', 0.6],\n      ],\n      'vuetify-outline': 'svg:M7.26 12.47 12.53 3H2L7.26 12.47ZM14.45 3 8.22 14.2 12 21 22 3H14.45ZM18.6 5 12 16.88 10.51 14.2 15.62 5ZM7.26 8.35 5.4 5H9.13L7.26 8.35Z',\n      'vuetify-play': [\n        'm6.376 13.184-4.11-7.192C1.505 4.66 2.467 3 4.003 3h8.532l-.953 1.576-.006.01-.396.677c-.429.732-.214 1.507.194 2.015.404.503 1.092.878 1.869.806a3.72 3.72 0 0 1 1.005.022c.276.053.434.143.523.237.138.146.38.635-.25 2.09-.893 1.63-1.553 1.722-1.847 1.677-.213-.033-.468-.158-.756-.406a4.95 4.95 0 0 1-.8-.927c-.39-.564-1.04-.84-1.66-.846-.625-.006-1.316.27-1.693.921l-.478.826-.911 1.506Z',\n        ['M9.093 11.552c.046-.079.144-.15.32-.148a.53.53 0 0 1 .43.207c.285.414.636.847 1.046 1.2.405.35.914.662 1.516.754 1.334.205 2.502-.698 3.48-2.495l.014-.028.013-.03c.687-1.574.774-2.852-.005-3.675-.37-.391-.861-.586-1.333-.676a5.243 5.243 0 0 0-1.447-.044c-.173.016-.393-.073-.54-.257-.145-.18-.127-.316-.082-.392l.393-.672L14.287 3h5.71c1.536 0 2.499 1.659 1.737 2.992l-7.997 13.996c-.768 1.344-2.706 1.344-3.473 0l-3.037-5.314 1.377-2.278.004-.006.004-.007.481-.831Z', 0.6],\n      ],\n      /* eslint-enable max-len */\n    },\n  }, options) as InternalIconOptions\n}\n\nexport const useIcon = (props: MaybeRefOrGetter<IconValue | undefined>) => {\n  const icons = inject(IconSymbol)\n\n  if (!icons) throw new Error('Missing Vuetify Icons provide!')\n\n  const iconData = computed<IconInstance>(() => {\n    const iconAlias = toValue(props)\n\n    if (!iconAlias) return { component: VComponentIcon }\n\n    let icon: IconValue | undefined = iconAlias\n\n    if (typeof icon === 'string') {\n      icon = icon.trim()\n\n      if (icon.startsWith('$')) {\n        icon = icons.aliases?.[icon.slice(1)]\n      }\n    }\n\n    if (!icon) consoleWarn(`Could not find aliased icon \"${iconAlias}\"`)\n\n    if (Array.isArray(icon)) {\n      return {\n        component: VSvgIcon,\n        icon,\n      }\n    } else if (typeof icon !== 'string') {\n      return {\n        component: VComponentIcon,\n        icon,\n      }\n    }\n\n    const iconSetName = Object.keys(icons.sets).find(\n      setName => typeof icon === 'string' && icon.startsWith(`${setName}:`)\n    )\n\n    const iconName = iconSetName ? icon.slice(iconSetName.length + 1) : icon\n    const iconSet = icons.sets[iconSetName ?? icons.defaultSet]\n\n    return {\n      component: iconSet.component,\n      icon: iconName,\n    }\n  })\n\n  return { iconData }\n}\n", "// Composables\nimport { VClassIcon } from '@/composables/icons'\n\n// Utilities\nimport { h } from 'vue'\n\n// Types\nimport type { IconAliases, IconSet } from '@/composables/icons'\n\nconst aliases: IconAliases = {\n  collapse: 'mdi-chevron-up',\n  complete: 'mdi-check',\n  cancel: 'mdi-close-circle',\n  close: 'mdi-close',\n  delete: 'mdi-close-circle', // delete (e.g. v-chip close)\n  clear: 'mdi-close-circle',\n  success: 'mdi-check-circle',\n  info: 'mdi-information',\n  warning: 'mdi-alert-circle',\n  error: 'mdi-close-circle',\n  prev: 'mdi-chevron-left',\n  next: 'mdi-chevron-right',\n  checkboxOn: 'mdi-checkbox-marked',\n  checkboxOff: 'mdi-checkbox-blank-outline',\n  checkboxIndeterminate: 'mdi-minus-box',\n  delimiter: 'mdi-circle', // for carousel\n  sortAsc: 'mdi-arrow-up',\n  sortDesc: 'mdi-arrow-down',\n  expand: 'mdi-chevron-down',\n  menu: 'mdi-menu',\n  subgroup: 'mdi-menu-down',\n  dropdown: 'mdi-menu-down',\n  radioOn: 'mdi-radiobox-marked',\n  radioOff: 'mdi-radiobox-blank',\n  edit: 'mdi-pencil',\n  ratingEmpty: 'mdi-star-outline',\n  ratingFull: 'mdi-star',\n  ratingHalf: 'mdi-star-half-full',\n  loading: 'mdi-cached',\n  first: 'mdi-page-first',\n  last: 'mdi-page-last',\n  unfold: 'mdi-unfold-more-horizontal',\n  file: 'mdi-paperclip',\n  plus: 'mdi-plus',\n  minus: 'mdi-minus',\n  calendar: 'mdi-calendar',\n  treeviewCollapse: 'mdi-menu-down',\n  treeviewExpand: 'mdi-menu-right',\n  eyeDropper: 'mdi-eyedropper',\n  upload: 'mdi-cloud-upload',\n  color: 'mdi-palette',\n}\n\nconst mdi: IconSet = {\n  // Not using mergeProps here, functional components merge props by default (?)\n  component: (props: any) => h(VClassIcon, { ...props, class: 'mdi' }),\n}\n\nexport { aliases, mdi }\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAeO,IAAMA,YAAY,CAACC,QAAQC,UAAUC,QAAQC,KAAK;AAwElD,IAAMC,aAAgDC,OAAOC,IAAI,eAAe;AAEhF,IAAMC,gBAAgBC,aAAa;EACxCC,MAAM;IACJC,MAAMX;EACR;;EAEAY,KAAK;IACHD,MAAM,CAACV,QAAQE,QAAQD,QAAQ;IAC/BW,UAAU;EACZ;AACF,GAAG,MAAM;AAEF,IAAMC,iBAAiBC,iBAAiB,EAAE;EAC/CC,MAAM;EAENC,OAAOT,cAAc;EAErBU,MAAOD,OAAKE,MAAa;AAAA,QAAX;MAAEC;IAAM,IAACD;AACrB,WAAO,MAAM;AACX,YAAME,OAAOJ,MAAMP;AACnB,aAAAY,YAAAL,MAAAL,KAAA,MAAA;QAAAW,SAAAA,MAAA;;AAAA,kBAEMN,MAAMP,OAAIY,YAAAD,MAAA,MAAA,IAAA,KAAcD,WAAMG,YAANH,8BAAiB;;MAAA,CAAA;IAGjD;EACF;AACF,CAAC;AAGM,IAAMI,WAAWC,gBAAgB;EACtCT,MAAM;EAENU,cAAc;EAEdT,OAAOT,cAAc;EAErBU,MAAOD,OAAKU,OAAa;AAAA,QAAX;MAAEC;IAAM,IAACD;AACrB,WAAO,MAAM;AACX,aAAAL,YAAAL,MAAAL,KAAAiB,WACkBD,OAAK;QAAA,SAAW;MAAI,CAAA,GAAA;QAAAL,SAAAA,MAAA,CAAAO,gBAAA,OAAA;UAAA,SAAA;UAAA,SAAA;UAAA,WAAA;UAAA,QAAA;UAAA,eAAA;QAAA,GAAA,CAQ9B1B,MAAM2B,QAAQd,MAAMP,IAAI,IACtBO,MAAMP,KAAKsB,IAAIC,UACf7B,MAAM2B,QAAQE,IAAI,IAACH,gBAAA,QAAA;UAAA,KACLG,KAAK,CAAC;UAAC,gBAA4BA,KAAK,CAAC;QAAC,GAAA,IAAA,IAAAH,gBAAA,QAAA;UAAA,KAC1CG;QAAI,GAAA,IAAA,CACnB,IAACH,gBAAA,QAAA;UAAA,KACUb,MAAMP;QAAI,GAAA,IAAA,CAAoB,CAAA,CAAA;MAAA,CAAA;IAKpD;EACF;AACF,CAAC;AAGM,IAAMwB,gBAAgBT,gBAAgB;EAC3CT,MAAM;EAENC,OAAOT,cAAc;EAErBU,MAAOD,OAAO;AACZ,WAAO,MAAM;AACX,aAAAK,YAAAL,MAAAL,KAAA,MAAA;QAAAW,SAAAA,MAAA,CAAoBN,MAAMP,IAAI;MAAA,CAAA;IAChC;EACF;AACF,CAAC;AAGM,IAAMyB,aAAaV,gBAAgB;EACxCT,MAAM;EAENC,OAAOT,cAAc;EAErBU,MAAOD,OAAO;AACZ,WAAO,MAAM;AACX,aAAAK,YAAAL,MAAAL,KAAA;QAAA,SAAAwB,eAA0BnB,MAAMP,IAAI;MAAA,GAAA,IAAA;IACtC;EACF;AACF,CAAC;AAGD,SAAS2B,cAAwC;AAC/C,SAAO;IACLC,KAAK;MACHC,WAAWf;IACb;IACAgB,OAAO;MACLD,WAAWJ;IACb;EACF;AACF;AAGO,SAASM,YAAaC,SAAuB;AAClD,QAAMC,OAAON,YAAY;AACzB,QAAMO,cAAaF,mCAASE,eAAc;AAE1C,MAAIA,eAAe,SAAS,CAACD,KAAKE,KAAK;AACrCF,SAAKE,MAAMA;EACb;AAEA,SAAOC,UAAU;IACfF;IACAD;IACAI,SAAS;MACP,GAAGA;;MAEHC,SAAS,CACP,sDACA,CAAC,0FAA0F,GAAG,CAAC;MAEjG,mBAAmB;MACnB,gBAAgB,CACd,wYACA,CAAC,sdAAsd,GAAG,CAAC;;IAG/d;EACF,GAAGN,OAAO;AACZ;AAEO,IAAMO,UAAWhC,WAAmD;AACzE,QAAMiC,QAAQC,OAAO9C,UAAU;AAE/B,MAAI,CAAC6C,MAAO,OAAM,IAAIE,MAAM,gCAAgC;AAE5D,QAAMC,WAAWC,SAAuB,MAAM;;AAC5C,UAAMC,YAAYC,QAAQvC,KAAK;AAE/B,QAAI,CAACsC,UAAW,QAAO;MAAEhB,WAAWzB;IAAe;AAEnD,QAAIJ,OAA8B6C;AAElC,QAAI,OAAO7C,SAAS,UAAU;AAC5BA,aAAOA,KAAK+C,KAAK;AAEjB,UAAI/C,KAAKgD,WAAW,GAAG,GAAG;AACxBhD,gBAAOwC,WAAMH,YAANG,mBAAgBxC,KAAKiD,MAAM,CAAC;MACrC;IACF;AAEA,QAAI,CAACjD,KAAMkD,aAAY,gCAAgCL,SAAS,GAAG;AAEnE,QAAInD,MAAM2B,QAAQrB,IAAI,GAAG;AACvB,aAAO;QACL6B,WAAWf;QACXd;MACF;IACF,WAAW,OAAOA,SAAS,UAAU;AACnC,aAAO;QACL6B,WAAWzB;QACXJ;MACF;IACF;AAEA,UAAMmD,cAAc1D,OAAO2D,KAAKZ,MAAMP,IAAI,EAAEoB,KAC1CC,aAAW,OAAOtD,SAAS,YAAYA,KAAKgD,WAAW,GAAGM,OAAO,GAAG,CACtE;AAEA,UAAMC,WAAWJ,cAAcnD,KAAKiD,MAAME,YAAYK,SAAS,CAAC,IAAIxD;AACpE,UAAMyD,UAAUjB,MAAMP,KAAKkB,eAAeX,MAAMN,UAAU;AAE1D,WAAO;MACLL,WAAW4B,QAAQ5B;MACnB7B,MAAMuD;IACR;EACF,CAAC;AAED,SAAO;IAAEZ;EAAS;AACpB;;;ACjQA,IAAMe,UAAuB;EAC3BC,UAAU;EACVC,UAAU;EACVC,QAAQ;EACRC,OAAO;EACPC,QAAQ;;EACRC,OAAO;EACPC,SAAS;EACTC,MAAM;EACNC,SAAS;EACTC,OAAO;EACPC,MAAM;EACNC,MAAM;EACNC,YAAY;EACZC,aAAa;EACbC,uBAAuB;EACvBC,WAAW;;EACXC,SAAS;EACTC,UAAU;EACVC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,UAAU;EACVC,SAAS;EACTC,UAAU;EACVC,MAAM;EACNC,aAAa;EACbC,YAAY;EACZC,YAAY;EACZC,SAAS;EACTC,OAAO;EACPC,MAAM;EACNC,QAAQ;EACRC,MAAM;EACNC,MAAM;EACNC,OAAO;EACPC,UAAU;EACVC,kBAAkB;EAClBC,gBAAgB;EAChBC,YAAY;EACZC,QAAQ;EACRC,OAAO;AACT;AAEA,IAAMC,MAAe;;EAEnBC,WAAYC,WAAeC,EAAEC,YAAY;IAAE,GAAGF;IAAOG,OAAO;EAAM,CAAC;AACrE;", "names": ["IconValue", "String", "Function", "Object", "Array", "IconSymbol", "Symbol", "for", "makeIconProps", "propsFactory", "icon", "type", "tag", "required", "VComponentIcon", "genericComponent", "name", "props", "setup", "_ref", "slots", "Icon", "_createVNode", "default", "VSvgIcon", "defineComponent", "inheritAttrs", "_ref2", "attrs", "_mergeProps", "_createElementVNode", "isArray", "map", "path", "VLigatureIcon", "VClassIcon", "_normalizeClass", "gen<PERSON><PERSON><PERSON><PERSON>", "svg", "component", "class", "createIcons", "options", "sets", "defaultSet", "mdi", "mergeDeep", "aliases", "vuetify", "useIcon", "icons", "inject", "Error", "iconData", "computed", "iconAlias", "toValue", "trim", "startsWith", "slice", "console<PERSON>arn", "iconSetName", "keys", "find", "setName", "iconName", "length", "iconSet", "aliases", "collapse", "complete", "cancel", "close", "delete", "clear", "success", "info", "warning", "error", "prev", "next", "checkboxOn", "checkboxOff", "checkboxIndeterminate", "delimiter", "sortAsc", "sortDesc", "expand", "menu", "subgroup", "dropdown", "radioOn", "radioOff", "edit", "ratingEmpty", "ratingFull", "ratingHalf", "loading", "first", "last", "unfold", "file", "plus", "minus", "calendar", "treeviewCollapse", "treeviewExpand", "eyeDropper", "upload", "color", "mdi", "component", "props", "h", "VClassIcon", "class"]}