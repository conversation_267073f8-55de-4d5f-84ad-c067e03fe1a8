{"version": 3, "sources": ["../../vuetify/src/util/globals.ts", "../../vuetify/src/util/helpers.ts", "../../vuetify/src/util/getCurrentInstance.ts", "../../vuetify/src/util/injectSelf.ts", "../../vuetify/src/composables/defaults.ts", "../../vuetify/src/util/console.ts", "../../vuetify/src/util/propsFactory.ts", "../../vuetify/src/util/defineComponent.tsx", "../../vuetify/src/util/anchor.ts", "../../vuetify/src/util/box.ts", "../../vuetify/src/util/animation.ts", "../../vuetify/src/util/bindProps.ts", "../../vuetify/src/util/color/APCA.ts", "../../vuetify/src/util/color/transformCIELAB.ts", "../../vuetify/src/util/color/transformSRGB.ts", "../../vuetify/src/util/colorUtils.ts", "../../vuetify/src/composables/component.ts", "../../vuetify/src/util/createSimpleFunctional.ts", "../../vuetify/src/util/dom.ts", "../../vuetify/src/util/easing.ts", "../../vuetify/src/util/events.ts", "../../vuetify/src/util/getScrollParent.ts", "../../vuetify/src/util/isFixedPosition.ts", "../../vuetify/src/util/useRender.ts"], "sourcesContent": ["export const IN_BROWSER = typeof window !== 'undefined'\nexport const SUPPORTS_INTERSECTION = IN_BROWSER && 'IntersectionObserver' in window\nexport const SUPPORTS_TOUCH = IN_BROWSER && ('ontouchstart' in window || window.navigator.maxTouchPoints > 0)\nexport const SUPPORTS_EYE_DROPPER = IN_BROWSER && 'EyeDropper' in window\n", "// Utilities\nimport {\n  camelize,\n  capitalize,\n  Comment,\n  Fragment,\n  isVNode,\n  reactive,\n  shallowRef,\n  toRef,\n  unref,\n  watchEffect,\n} from 'vue'\nimport { IN_BROWSER } from '@/util/globals'\n\n// Types\nimport type {\n  ComponentInternalInstance,\n  ComponentPublicInstance,\n  ComputedGetter,\n  InjectionKey,\n  PropType,\n  Ref,\n  ToRef,\n  VNode,\n  VNodeArrayChildren,\n  VNodeChild,\n} from 'vue'\n\nexport function getNestedValue (obj: any, path: (string | number)[], fallback?: any): any {\n  const last = path.length - 1\n\n  if (last < 0) return obj === undefined ? fallback : obj\n\n  for (let i = 0; i < last; i++) {\n    if (obj == null) {\n      return fallback\n    }\n    obj = obj[path[i]]\n  }\n\n  if (obj == null) return fallback\n\n  return obj[path[last]] === undefined ? fallback : obj[path[last]]\n}\n\nexport function deepEqual (a: any, b: any): boolean {\n  if (a === b) return true\n\n  if (\n    a instanceof Date &&\n    b instanceof Date &&\n    a.getTime() !== b.getTime()\n  ) {\n    // If the values are Date, compare them as timestamps\n    return false\n  }\n\n  if (a !== Object(a) || b !== Object(b)) {\n    // If the values aren't objects, they were already checked for equality\n    return false\n  }\n\n  const props = Object.keys(a)\n\n  if (props.length !== Object.keys(b).length) {\n    // Different number of props, don't bother to check\n    return false\n  }\n\n  return props.every(p => deepEqual(a[p], b[p]))\n}\n\nexport function getObjectValueByPath (obj: any, path?: string | null, fallback?: any): any {\n  // credit: http://stackoverflow.com/questions/6491463/accessing-nested-javascript-objects-with-string-key#comment55278413_6491621\n  if (obj == null || !path || typeof path !== 'string') return fallback\n  if (obj[path] !== undefined) return obj[path]\n  path = path.replace(/\\[(\\w+)\\]/g, '.$1') // convert indexes to properties\n  path = path.replace(/^\\./, '') // strip a leading dot\n  return getNestedValue(obj, path.split('.'), fallback)\n}\n\nexport type SelectItemKey<T = Record<string, any>> =\n  | boolean | null | undefined // Ignored\n  | string // Lookup by key, can use dot notation for nested objects\n  | readonly (string | number)[] // Nested lookup by key, each array item is a key in the next level\n  | ((item: T, fallback?: any) => any)\n\nexport function getPropertyFromItem (\n  item: any,\n  property: SelectItemKey,\n  fallback?: any\n): any {\n  if (property === true) return item === undefined ? fallback : item\n\n  if (property == null || typeof property === 'boolean') return fallback\n\n  if (item !== Object(item)) {\n    if (typeof property !== 'function') return fallback\n\n    const value = property(item, fallback)\n\n    return typeof value === 'undefined' ? fallback : value\n  }\n\n  if (typeof property === 'string') return getObjectValueByPath(item, property, fallback)\n\n  if (Array.isArray(property)) return getNestedValue(item, property, fallback)\n\n  if (typeof property !== 'function') return fallback\n\n  const value = property(item, fallback)\n\n  return typeof value === 'undefined' ? fallback : value\n}\n\nexport function createRange (length: number, start = 0): number[] {\n  return Array.from({ length }, (v, k) => start + k)\n}\n\nexport function getZIndex (el?: Element | null): number {\n  if (!el || el.nodeType !== Node.ELEMENT_NODE) return 0\n\n  const index = Number(window.getComputedStyle(el).getPropertyValue('z-index'))\n\n  if (!index) return getZIndex(el.parentNode as Element)\n  return index\n}\n\nexport function convertToUnit (str: number, unit?: string): string\nexport function convertToUnit (str: string | number | null | undefined, unit?: string): string | undefined\nexport function convertToUnit (str: string | number | null | undefined, unit = 'px'): string | undefined {\n  if (str == null || str === '') {\n    return undefined\n  }\n  const num = Number(str)\n  if (isNaN(num)) {\n    return String(str)\n  } else if (!isFinite(num)) {\n    return undefined\n  } else {\n    return `${num}${unit}`\n  }\n}\n\nexport function isObject (obj: any): obj is Record<string, any> {\n  return obj !== null && typeof obj === 'object' && !Array.isArray(obj)\n}\n\nexport function isPlainObject (obj: any): obj is Record<string, any> {\n  let proto\n  return obj !== null && typeof obj === 'object' && (\n    (proto = Object.getPrototypeOf(obj)) === Object.prototype ||\n    proto === null\n  )\n}\n\nexport function refElement (obj?: ComponentPublicInstance<any> | HTMLElement): HTMLElement | undefined {\n  if (obj && '$el' in obj) {\n    const el = obj.$el as HTMLElement\n    if (el?.nodeType === Node.TEXT_NODE) {\n      // Multi-root component, use the first element\n      return el.nextElementSibling as HTMLElement\n    }\n    return el\n  }\n  return obj as HTMLElement\n}\n\n// KeyboardEvent.keyCode aliases\nexport const keyCodes = Object.freeze({\n  enter: 13,\n  tab: 9,\n  delete: 46,\n  esc: 27,\n  space: 32,\n  up: 38,\n  down: 40,\n  left: 37,\n  right: 39,\n  end: 35,\n  home: 36,\n  del: 46,\n  backspace: 8,\n  insert: 45,\n  pageup: 33,\n  pagedown: 34,\n  shift: 16,\n})\n\nexport const keyValues: Record<string, string> = Object.freeze({\n  enter: 'Enter',\n  tab: 'Tab',\n  delete: 'Delete',\n  esc: 'Escape',\n  space: 'Space',\n  up: 'ArrowUp',\n  down: 'ArrowDown',\n  left: 'ArrowLeft',\n  right: 'ArrowRight',\n  end: 'End',\n  home: 'Home',\n  del: 'Delete',\n  backspace: 'Backspace',\n  insert: 'Insert',\n  pageup: 'PageUp',\n  pagedown: 'PageDown',\n  shift: 'Shift',\n})\n\nexport function keys<O extends {}> (o: O) {\n  return Object.keys(o) as (keyof O)[]\n}\n\nexport function has<T extends string> (obj: object, key: T[]): obj is Record<T, unknown> {\n  return key.every(k => obj.hasOwnProperty(k))\n}\n\ntype MaybePick<\n  T extends object,\n  U extends Extract<keyof T, string>\n> = Record<string, unknown> extends T ? Partial<Pick<T, U>> : Pick<T, U>\n\n// Array of keys\nexport function pick<\n  T extends object,\n  U extends Extract<keyof T, string>\n> (obj: T, paths: U[]): MaybePick<T, U> {\n  const found: any = {}\n\n  for (const key of paths) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      found[key] = obj[key]\n    }\n  }\n\n  return found\n}\n\n// Array of keys\nexport function pickWithRest<\n  T extends object,\n  U extends Extract<keyof T, string>,\n  E extends Extract<keyof T, string>\n> (obj: T, paths: U[], exclude?: E[]): [yes: MaybePick<T, Exclude<U, E>>, no: Omit<T, Exclude<U, E>>]\n// Array of keys or RegExp to test keys against\nexport function pickWithRest<\n  T extends object,\n  U extends Extract<keyof T, string>,\n  E extends Extract<keyof T, string>\n> (obj: T, paths: (U | RegExp)[], exclude?: E[]): [yes: Partial<T>, no: Partial<T>]\nexport function pickWithRest<\n  T extends object,\n  U extends Extract<keyof T, string>,\n  E extends Extract<keyof T, string>\n> (obj: T, paths: (U | RegExp)[], exclude?: E[]): [yes: Partial<T>, no: Partial<T>] {\n  const found = Object.create(null)\n  const rest = Object.create(null)\n\n  for (const key in obj) {\n    if (\n      paths.some(path => path instanceof RegExp\n        ? path.test(key)\n        : path === key\n      ) && !exclude?.some(path => path === key)\n    ) {\n      found[key] = obj[key]\n    } else {\n      rest[key] = obj[key]\n    }\n  }\n\n  return [found, rest]\n}\n\nexport function omit<\n  T extends object,\n  U extends Extract<keyof T, string>\n> (obj: T, exclude: U[]): Omit<T, U> {\n  const clone = { ...obj }\n\n  exclude.forEach(prop => delete clone[prop])\n\n  return clone\n}\n\nconst onRE = /^on[^a-z]/\nexport const isOn = (key: string) => onRE.test(key)\n\nconst bubblingEvents = [\n  'onAfterscriptexecute',\n  'onAnimationcancel',\n  'onAnimationend',\n  'onAnimationiteration',\n  'onAnimationstart',\n  'onAuxclick',\n  'onBeforeinput',\n  'onBeforescriptexecute',\n  'onChange',\n  'onClick',\n  'onCompositionend',\n  'onCompositionstart',\n  'onCompositionupdate',\n  'onContextmenu',\n  'onCopy',\n  'onCut',\n  'onDblclick',\n  'onFocusin',\n  'onFocusout',\n  'onFullscreenchange',\n  'onFullscreenerror',\n  'onGesturechange',\n  'onGestureend',\n  'onGesturestart',\n  'onGotpointercapture',\n  'onInput',\n  'onKeydown',\n  'onKeypress',\n  'onKeyup',\n  'onLostpointercapture',\n  'onMousedown',\n  'onMousemove',\n  'onMouseout',\n  'onMouseover',\n  'onMouseup',\n  'onMousewheel',\n  'onPaste',\n  'onPointercancel',\n  'onPointerdown',\n  'onPointerenter',\n  'onPointerleave',\n  'onPointermove',\n  'onPointerout',\n  'onPointerover',\n  'onPointerup',\n  'onReset',\n  'onSelect',\n  'onSubmit',\n  'onTouchcancel',\n  'onTouchend',\n  'onTouchmove',\n  'onTouchstart',\n  'onTransitioncancel',\n  'onTransitionend',\n  'onTransitionrun',\n  'onTransitionstart',\n  'onWheel',\n]\n\nconst compositionIgnoreKeys = [\n  'ArrowUp',\n  'ArrowDown',\n  'ArrowRight',\n  'ArrowLeft',\n  'Enter',\n  'Escape',\n  'Tab',\n  ' ',\n]\n\nexport function isComposingIgnoreKey (e: KeyboardEvent): boolean {\n  return e.isComposing && compositionIgnoreKeys.includes(e.key)\n}\n\n/**\n * Filter attributes that should be applied to\n * the root element of an input component. Remaining\n * attributes should be passed to the <input> element inside.\n */\nexport function filterInputAttrs (attrs: Record<string, unknown>) {\n  const [events, props] = pickWithRest(attrs, [onRE])\n  const inputEvents = omit(events, bubblingEvents)\n  const [rootAttrs, inputAttrs] = pickWithRest(props, ['class', 'style', 'id', /^data-/])\n  Object.assign(rootAttrs, events)\n  Object.assign(inputAttrs, inputEvents)\n  return [rootAttrs, inputAttrs]\n}\n\n/**\n * Returns the set difference of B and A, i.e. the set of elements in B but not in A\n */\nexport function arrayDiff (a: any[], b: any[]): any[] {\n  const diff: any[] = []\n  for (let i = 0; i < b.length; i++) {\n    if (!a.includes(b[i])) diff.push(b[i])\n  }\n  return diff\n}\n\ntype IfAny<T, Y, N> = 0 extends (1 & T) ? Y : N;\nexport function wrapInArray<T> (\n  v: T | null | undefined\n): T extends readonly any[]\n    ? IfAny<T, T[], T>\n    : NonNullable<T>[] {\n  return v == null\n    ? [] as any\n    : Array.isArray(v)\n      ? v as any : [v] as any\n}\n\nexport function defaultFilter (value: any, search: string | null, item: any) {\n  return value != null &&\n    search != null &&\n    typeof value !== 'boolean' &&\n    value.toString().toLocaleLowerCase().indexOf(search.toLocaleLowerCase()) !== -1\n}\n\nexport function debounce (fn: Function, delay: MaybeRef<number>) {\n  let timeoutId = 0 as any\n  const wrap = (...args: any[]) => {\n    clearTimeout(timeoutId)\n    timeoutId = setTimeout(() => fn(...args), unref(delay))\n  }\n  wrap.clear = () => {\n    clearTimeout(timeoutId)\n  }\n  wrap.immediate = fn\n  return wrap\n}\n\nexport function throttle<T extends (...args: any[]) => any> (fn: T, limit: number) {\n  let throttling = false\n  return (...args: Parameters<T>): void | ReturnType<T> => {\n    if (!throttling) {\n      throttling = true\n      setTimeout(() => throttling = false, limit)\n      return fn(...args)\n    }\n  }\n}\n\nexport function clamp (value: number, min = 0, max = 1) {\n  return Math.max(min, Math.min(max, value))\n}\n\nexport function getDecimals (value: number) {\n  const trimmedStr = value.toString().trim()\n  return trimmedStr.includes('.')\n    ? (trimmedStr.length - trimmedStr.indexOf('.') - 1)\n    : 0\n}\n\nexport function padEnd (str: string, length: number, char = '0') {\n  return str + char.repeat(Math.max(0, length - str.length))\n}\n\nexport function padStart (str: string, length: number, char = '0') {\n  return char.repeat(Math.max(0, length - str.length)) + str\n}\n\nexport function chunk (str: string, size = 1) {\n  const chunked: string[] = []\n  let index = 0\n  while (index < str.length) {\n    chunked.push(str.substr(index, size))\n    index += size\n  }\n  return chunked\n}\n\nexport function chunkArray (array: any[], size = 1) {\n  return Array.from({ length: Math.ceil(array.length / size) }, (v, i) =>\n    array.slice(i * size, i * size + size)\n  )\n}\n\nexport function humanReadableFileSize (bytes: number, base: 1000 | 1024 = 1000): string {\n  if (bytes < base) {\n    return `${bytes} B`\n  }\n\n  const prefix = base === 1024 ? ['Ki', 'Mi', 'Gi'] : ['k', 'M', 'G']\n  let unit = -1\n  while (Math.abs(bytes) >= base && unit < prefix.length - 1) {\n    bytes /= base\n    ++unit\n  }\n  return `${bytes.toFixed(1)} ${prefix[unit]}B`\n}\n\nexport function mergeDeep (\n  source: Record<string, any> = {},\n  target: Record<string, any> = {},\n  arrayFn?: (a: unknown[], b: unknown[]) => unknown[],\n) {\n  const out: Record<string, any> = {}\n\n  for (const key in source) {\n    out[key] = source[key]\n  }\n\n  for (const key in target) {\n    const sourceProperty = source[key]\n    const targetProperty = target[key]\n\n    // Only continue deep merging if\n    // both properties are plain objects\n    if (isPlainObject(sourceProperty) && isPlainObject(targetProperty)) {\n      out[key] = mergeDeep(sourceProperty, targetProperty, arrayFn)\n\n      continue\n    }\n\n    if (arrayFn && Array.isArray(sourceProperty) && Array.isArray(targetProperty)) {\n      out[key] = arrayFn(sourceProperty, targetProperty)\n\n      continue\n    }\n\n    out[key] = targetProperty\n  }\n\n  return out\n}\n\nexport function flattenFragments (nodes: VNode[]): VNode[] {\n  return nodes.map(node => {\n    if (node.type === Fragment) {\n      return flattenFragments(node.children as VNode[])\n    } else {\n      return node\n    }\n  }).flat()\n}\n\nexport function toKebabCase (str = '') {\n  if (toKebabCase.cache.has(str)) return toKebabCase.cache.get(str)!\n  const kebab = str\n    .replace(/[^a-z]/gi, '-')\n    .replace(/\\B([A-Z])/g, '-$1')\n    .toLowerCase()\n  toKebabCase.cache.set(str, kebab)\n  return kebab\n}\ntoKebabCase.cache = new Map<string, string>()\n\nexport type MaybeRef<T> = T | Ref<T>\n\nexport function findChildrenWithProvide (\n  key: InjectionKey<any> | symbol,\n  vnode?: VNodeChild,\n): ComponentInternalInstance[] {\n  if (!vnode || typeof vnode !== 'object') return []\n\n  if (Array.isArray(vnode)) {\n    return vnode.map(child => findChildrenWithProvide(key, child)).flat(1)\n  } else if (vnode.suspense) {\n    return findChildrenWithProvide(key, vnode.ssContent!)\n  } else if (Array.isArray(vnode.children)) {\n    return vnode.children.map(child => findChildrenWithProvide(key, child)).flat(1)\n  } else if (vnode.component) {\n    if (Object.getOwnPropertySymbols(vnode.component.provides).includes(key as symbol)) {\n      return [vnode.component]\n    } else if (vnode.component.subTree) {\n      return findChildrenWithProvide(key, vnode.component.subTree).flat(1)\n    }\n  }\n\n  return []\n}\n\nexport class CircularBuffer<T = never> {\n  readonly #arr: Array<T> = []\n  #pointer = 0\n\n  constructor (public readonly size: number) {}\n\n  get isFull () {\n    return this.#arr.length === this.size\n  }\n\n  push (val: T) {\n    this.#arr[this.#pointer] = val\n    this.#pointer = (this.#pointer + 1) % this.size\n  }\n\n  values (): T[] {\n    return this.#arr.slice(this.#pointer).concat(this.#arr.slice(0, this.#pointer))\n  }\n\n  clear () {\n    this.#arr.length = 0\n    this.#pointer = 0\n  }\n}\n\nexport type UnionToIntersection<U> =\n  (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never\n\nexport function getEventCoordinates (e: MouseEvent | TouchEvent) {\n  if ('touches' in e) {\n    return { clientX: e.touches[0].clientX, clientY: e.touches[0].clientY }\n  }\n\n  return { clientX: e.clientX, clientY: e.clientY }\n}\n\n// Only allow a single return type\ntype NotAUnion<T> = [T] extends [infer U] ? _NotAUnion<U, U> : never\ntype _NotAUnion<T, U> = U extends any ? [T] extends [U] ? unknown : never : never\n\ntype ToReadonlyRefs<T> = { [K in keyof T]: Readonly<ToRef<T[K]>> }\n\n/**\n * Convert a computed ref to a record of refs.\n * The getter function must always return an object with the same keys.\n */\nexport function destructComputed<T extends object> (getter: ComputedGetter<T & NotAUnion<T>>): ToReadonlyRefs<T>\nexport function destructComputed<T extends object> (getter: ComputedGetter<T>) {\n  const refs = reactive({}) as T\n  watchEffect(() => {\n    const base = getter()\n    for (const key in base) {\n      refs[key] = base[key]\n    }\n  }, { flush: 'sync' })\n  const obj = {} as ToReadonlyRefs<T>\n  for (const key in refs) {\n    obj[key] = toRef(() => refs[key]) as any\n  }\n  return obj\n}\n\n/** Array.includes but value can be any type */\nexport function includes (arr: readonly any[], val: any) {\n  return arr.includes(val)\n}\n\nexport function eventName (propName: string) {\n  return propName[2].toLowerCase() + propName.slice(3)\n}\n\n// TODO: this should be an array but vue's types don't accept arrays: vuejs/core#8025\nexport type EventProp<T extends any[] = any[], F = (...args: T) => void> = F\nexport const EventProp = <T extends any[] = any[]>() => [Function, Array] as PropType<EventProp<T>>\n\nexport function hasEvent (props: Record<string, any>, name: string) {\n  name = 'on' + capitalize(name)\n  return !!(props[name] || props[`${name}Once`] || props[`${name}Capture`] || props[`${name}OnceCapture`] || props[`${name}CaptureOnce`])\n}\n\nexport function callEvent<T extends any[]> (handler: EventProp<T> | EventProp<T>[] | undefined, ...args: T) {\n  if (Array.isArray(handler)) {\n    for (const h of handler) {\n      h(...args)\n    }\n  } else if (typeof handler === 'function') {\n    handler(...args)\n  }\n}\n\nexport function focusableChildren (el: Element, filterByTabIndex = true) {\n  const targets = ['button', '[href]', 'input:not([type=\"hidden\"])', 'select', 'textarea', '[tabindex]']\n    .map(s => `${s}${filterByTabIndex ? ':not([tabindex=\"-1\"])' : ''}:not([disabled])`)\n    .join(', ')\n  return [...el.querySelectorAll(targets)] as HTMLElement[]\n}\n\nexport function getNextElement (elements: HTMLElement[], location?: 'next' | 'prev', condition?: (el: HTMLElement) => boolean) {\n  let _el\n  let idx = elements.indexOf(document.activeElement as HTMLElement)\n  const inc = location === 'next' ? 1 : -1\n  do {\n    idx += inc\n    _el = elements[idx]\n  } while ((!_el || _el.offsetParent == null || !(condition?.(_el) ?? true)) && idx < elements.length && idx >= 0)\n  return _el\n}\n\nexport function focusChild (el: Element, location?: 'next' | 'prev' | 'first' | 'last' | number) {\n  const focusable = focusableChildren(el)\n\n  if (location == null) {\n    if (el === document.activeElement || !el.contains(document.activeElement)) {\n      focusable[0]?.focus()\n    }\n  } else if (location === 'first') {\n    focusable[0]?.focus()\n  } else if (location === 'last') {\n    focusable.at(-1)?.focus()\n  } else if (typeof location === 'number') {\n    focusable[location]?.focus()\n  } else {\n    const _el = getNextElement(focusable, location)\n    if (_el) _el.focus()\n    else focusChild(el, location === 'next' ? 'first' : 'last')\n  }\n}\n\nexport function isEmpty (val: any): boolean {\n  return val === null || val === undefined || (typeof val === 'string' && val.trim() === '')\n}\n\nexport function noop () {}\n\n/** Returns null if the selector is not supported or we can't check */\nexport function matchesSelector (el: Element | undefined, selector: string): boolean | null {\n  const supportsSelector = IN_BROWSER &&\n    typeof CSS !== 'undefined' &&\n    typeof CSS.supports !== 'undefined' &&\n    CSS.supports(`selector(${selector})`)\n\n  if (!supportsSelector) return null\n\n  try {\n    return !!el && el.matches(selector)\n  } catch (err) {\n    return null\n  }\n}\n\nexport function ensureValidVNode (vnodes: VNodeArrayChildren): VNodeArrayChildren | null {\n  return vnodes.some(child => {\n    if (!isVNode(child)) return true\n    if (child.type === Comment) return false\n    return child.type !== Fragment ||\n      ensureValidVNode(child.children as VNodeArrayChildren)\n  })\n    ? vnodes\n    : null\n}\n\nexport function defer (timeout: number, cb: () => void) {\n  if (!IN_BROWSER || timeout === 0) {\n    cb()\n\n    return () => {}\n  }\n\n  const timeoutId = window.setTimeout(cb, timeout)\n\n  return () => window.clearTimeout(timeoutId)\n}\n\nexport function isClickInsideElement (event: MouseEvent, targetDiv: HTMLElement) {\n  const mouseX = event.clientX\n  const mouseY = event.clientY\n\n  const divRect = targetDiv.getBoundingClientRect()\n  const divLeft = divRect.left\n  const divTop = divRect.top\n  const divRight = divRect.right\n  const divBottom = divRect.bottom\n\n  return mouseX >= divLeft && mouseX <= divRight && mouseY >= divTop && mouseY <= divBottom\n}\n\nexport type TemplateRef = {\n  (target: Element | ComponentPublicInstance | null): void\n  value: HTMLElement | ComponentPublicInstance | null | undefined\n  readonly el: HTMLElement | undefined\n}\nexport function templateRef () {\n  const el = shallowRef<HTMLElement | ComponentPublicInstance | null>()\n  const fn = (target: HTMLElement | ComponentPublicInstance | null) => {\n    el.value = target\n  }\n  Object.defineProperty(fn, 'value', {\n    enumerable: true,\n    get: () => el.value,\n    set: val => el.value = val,\n  })\n  Object.defineProperty(fn, 'el', {\n    enumerable: true,\n    get: () => refElement(el.value),\n  })\n\n  return fn as TemplateRef\n}\n\nexport function checkPrintable (e: KeyboardEvent) {\n  const isPrintableChar = e.key.length === 1\n  const noModifier = !e.ctrlKey && !e.metaKey && !e.altKey\n  return isPrintableChar && noModifier\n}\n\nexport type Primitive = string | number | boolean | symbol | bigint\nexport function isPrimitive (value: unknown): value is Primitive {\n  return typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean' || typeof value === 'bigint'\n}\n\nexport function extractNumber (text: string, decimalDigitsLimit: number | null) {\n  const cleanText = text.split('')\n    .filter(x => /[\\d\\-.]/.test(x))\n    .filter((x, i, all) => (i === 0 && /[-]/.test(x)) || // sign allowed at the start\n        (x === '.' && i === all.indexOf('.')) || // decimal separator allowed only once\n        /\\d/.test(x))\n    .join('')\n\n  if (decimalDigitsLimit === 0) {\n    return cleanText.split('.')[0]\n  }\n\n  if (decimalDigitsLimit !== null && /\\.\\d/.test(cleanText)) {\n    const parts = cleanText.split('.')\n    return [\n      parts[0],\n      parts[1].substring(0, decimalDigitsLimit),\n    ].join('.')\n  }\n\n  return cleanText\n}\n\nexport function camelizeProps<T extends Record<string, unknown>> (props: T | null): T {\n  const out = {} as T\n  for (const prop in props) {\n    out[camelize(prop) as keyof T] = props[prop]\n  }\n  return out\n}\n\nexport function onlyDefinedProps (props: Record<string, any>) {\n  const booleanAttributes = ['checked', 'disabled']\n  return Object.fromEntries(Object.entries(props)\n    .filter(([key, v]) => booleanAttributes.includes(key) ? !!v : v !== undefined))\n}\n", "// Utilities\nimport { getCurrentInstance as _getCurrentInstance } from 'vue'\nimport { toKebabCase } from '@/util/helpers'\n\nexport function getCurrentInstance (name: string, message?: string) {\n  const vm = _getCurrentInstance()\n\n  if (!vm) {\n    throw new Error(`[Vuetify] ${name} ${message || 'must be called from inside a setup function'}`)\n  }\n\n  return vm\n}\n\nexport function getCurrentInstanceName (name = 'composables') {\n  const vm = getCurrentInstance(name).type\n\n  return toKebabCase(vm?.aliasName || vm?.name)\n}\n", "// Utilities\nimport { getCurrentInstance } from '@/util/getCurrentInstance'\n\n// Types\nimport type { ComponentInternalInstance, InjectionKey } from 'vue'\n\nexport function injectSelf<T>(key: InjectionKey<T> | string, vm?: ComponentInternalInstance): T | undefined\nexport function injectSelf (key: InjectionKey<any> | string, vm = getCurrentInstance('injectSelf')) {\n  const { provides } = vm\n\n  if (provides && (key as string | symbol) in provides) {\n    // TS doesn't allow symbol as index type\n    return provides[key as string]\n  }\n  return undefined\n}\n", "// Utilities\nimport { computed, inject, provide, ref, shallowRef, unref, watchEffect } from 'vue'\nimport { getCurrentInstance } from '@/util/getCurrentInstance'\nimport { mergeDeep, toKebabCase } from '@/util/helpers'\nimport { injectSelf } from '@/util/injectSelf'\n\n// Types\nimport type { ComputedRef, InjectionKey, Ref, VNode } from 'vue'\nimport type { MaybeRef } from '@/util'\n\nexport type DefaultsInstance = undefined | {\n  [key: string]: undefined | Record<string, unknown>\n  global?: Record<string, unknown>\n}\n\nexport type DefaultsOptions = Partial<DefaultsInstance>\n\nexport const DefaultsSymbol: InjectionKey<Ref<DefaultsInstance>> = Symbol.for('vuetify:defaults')\n\nexport function createDefaults (options?: DefaultsInstance): Ref<DefaultsInstance> {\n  return ref(options)\n}\n\nexport function injectDefaults () {\n  const defaults = inject(DefaultsSymbol)\n\n  if (!defaults) throw new Error('[Vuetify] Could not find defaults instance')\n\n  return defaults\n}\n\nexport function provideDefaults (\n  defaults?: MaybeRef<DefaultsInstance | undefined>,\n  options?: {\n    disabled?: MaybeRef<boolean | undefined>\n    reset?: MaybeRef<number | string | undefined>\n    root?: MaybeRef<boolean | string | undefined>\n    scoped?: MaybeRef<boolean | undefined>\n  }\n) {\n  const injectedDefaults = injectDefaults()\n  const providedDefaults = ref(defaults)\n\n  const newDefaults = computed(() => {\n    const disabled = unref(options?.disabled)\n\n    if (disabled) return injectedDefaults.value\n\n    const scoped = unref(options?.scoped)\n    const reset = unref(options?.reset)\n    const root = unref(options?.root)\n\n    if (providedDefaults.value == null && !(scoped || reset || root)) return injectedDefaults.value\n\n    let properties = mergeDeep(providedDefaults.value, { prev: injectedDefaults.value })\n\n    if (scoped) return properties\n\n    if (reset || root) {\n      const len = Number(reset || Infinity)\n\n      for (let i = 0; i <= len; i++) {\n        if (!properties || !('prev' in properties)) {\n          break\n        }\n\n        properties = properties.prev\n      }\n\n      if (properties && typeof root === 'string' && root in properties) {\n        properties = mergeDeep(mergeDeep(properties, { prev: properties }), properties[root])\n      }\n\n      return properties\n    }\n\n    return properties.prev\n      ? mergeDeep(properties.prev, properties)\n      : properties\n  }) as ComputedRef<DefaultsInstance>\n\n  provide(DefaultsSymbol, newDefaults)\n\n  return newDefaults\n}\n\nfunction propIsDefined (vnode: VNode, prop: string) {\n  return vnode.props && (typeof vnode.props[prop] !== 'undefined' ||\n    typeof vnode.props[toKebabCase(prop)] !== 'undefined')\n}\n\nexport function internalUseDefaults (\n  props: Record<string, any> = {},\n  name?: string,\n  defaults = injectDefaults()\n) {\n  const vm = getCurrentInstance('useDefaults')\n\n  name = name ?? vm.type.name ?? vm.type.__name\n  if (!name) {\n    throw new Error('[Vuetify] Could not determine component name')\n  }\n\n  const componentDefaults = computed(() => defaults.value?.[props._as ?? name])\n  const _props = new Proxy(props, {\n    get (target, prop: string) {\n      const propValue = Reflect.get(target, prop)\n      if (prop === 'class' || prop === 'style') {\n        return [componentDefaults.value?.[prop], propValue].filter(v => v != null)\n      }\n      if (propIsDefined(vm.vnode, prop)) return propValue\n      const _componentDefault = componentDefaults.value?.[prop]\n      if (_componentDefault !== undefined) return _componentDefault\n      const _globalDefault = defaults.value?.global?.[prop]\n      if (_globalDefault !== undefined) return _globalDefault\n      return propValue\n    },\n  })\n\n  const _subcomponentDefaults = shallowRef()\n  watchEffect(() => {\n    if (componentDefaults.value) {\n      const subComponents = Object.entries(componentDefaults.value)\n        .filter(([key]) => key.startsWith(key[0].toUpperCase()))\n      _subcomponentDefaults.value = subComponents.length ? Object.fromEntries(subComponents) : undefined\n    } else {\n      _subcomponentDefaults.value = undefined\n    }\n  })\n\n  function provideSubDefaults () {\n    const injected = injectSelf(DefaultsSymbol, vm)\n    provide(DefaultsSymbol, computed(() => {\n      return _subcomponentDefaults.value ? mergeDeep(\n        injected?.value ?? {},\n        _subcomponentDefaults.value\n      ) : injected?.value\n    }))\n  }\n\n  return { props: _props, provideSubDefaults }\n}\n\nexport function useDefaults<T extends Record<string, any>> (props: T, name?: string): T\nexport function useDefaults (props?: undefined, name?: string): Record<string, any>\nexport function useDefaults (\n  props: Record<string, any> = {},\n  name?: string,\n) {\n  const { props: _props, provideSubDefaults } = internalUseDefaults(props, name)\n  provideSubDefaults()\n  return _props\n}\n", "/* eslint-disable no-console */\n\n// Utilities\nimport { warn } from 'vue'\n\nexport function consoleWarn (message: string): void {\n  warn(`Vuetify: ${message}`)\n}\n\nexport function consoleError (message: string): void {\n  warn(`Vuetify error: ${message}`)\n}\n\nexport function deprecate (original: string, replacement: string | string[]) {\n  replacement = Array.isArray(replacement)\n    ? replacement.slice(0, -1).map(s => `'${s}'`).join(', ') + ` or '${replacement.at(-1)}'`\n    : `'${replacement}'`\n  warn(`[Vuetify UPGRADE] '${original}' is deprecated, use ${replacement} instead.`)\n}\nexport function breaking (original: string, replacement: string) {\n  // warn(`[Vuetify BREAKING] '${original}' has been removed, use '${replacement}' instead. For more information, see the upgrade guide https://github.com/vuetifyjs/vuetify/releases/tag/v2.0.0#user-content-upgrade-guide`)\n}\nexport function removed (original: string) {\n  // warn(`[Vuetify REMOVED] '${original}' has been removed. You can safely omit it.`)\n}\n", "// Types\nimport type { IfAny } from '@vue/shared' // eslint-disable-line vue/prefer-import-from-vue\nimport type { ComponentObjectPropsOptions, Prop, PropType } from 'vue'\n\n/**\n * Creates a factory function for props definitions.\n * This is used to define props in a composable then override\n * default values in an implementing component.\n *\n * @example Simplified signature\n * (props: Props) => (defaults?: Record<keyof props, any>) => Props\n *\n * @example Usage\n * const makeProps = propsFactory({\n *   foo: String,\n * })\n *\n * defineComponent({\n *   props: {\n *     ...makeProps({\n *       foo: 'a',\n *     }),\n *   },\n *   setup (props) {\n *     // would be \"string | undefined\", now \"string\" because a default has been provided\n *     props.foo\n *   },\n * }\n */\n\nexport function propsFactory<\n  PropsOptions extends ComponentObjectPropsOptions\n> (props: PropsOptions, source: string) {\n  return <Defaults extends PartialKeys<PropsOptions> = {}>(\n    defaults?: Defaults\n  ): AppendDefault<PropsOptions, Defaults> => {\n    return Object.keys(props).reduce<any>((obj, prop) => {\n      const isObjectDefinition = typeof props[prop] === 'object' && props[prop] != null && !Array.isArray(props[prop])\n      const definition = isObjectDefinition ? props[prop] : { type: props[prop] }\n\n      if (defaults && prop in defaults) {\n        obj[prop] = {\n          ...definition,\n          default: defaults[prop],\n        }\n      } else {\n        obj[prop] = definition\n      }\n\n      if (source && !obj[prop].source) {\n        obj[prop].source = source\n      }\n\n      return obj\n    }, {})\n  }\n}\n\ntype AppendDefault<T extends ComponentObjectPropsOptions, D extends PartialKeys<T>> = {\n  [P in keyof T]-?: unknown extends D[P]\n    ? T[P]\n    : T[P] extends Record<string, unknown>\n      ? Omit<T[P], 'type' | 'default'> & {\n        type: PropType<MergeTypeDefault<T[P], D[P]>>\n        default: MergeDefault<T[P], D[P]>\n      }\n      : {\n        type: PropType<MergeTypeDefault<T[P], D[P]>>\n        default: MergeDefault<T[P], D[P]>\n      }\n}\n\ntype MergeTypeDefault<T, D, P = InferPropType<T>> = unknown extends D\n  ? P\n  : (P | D)\ntype MergeDefault<T, D, P = InferPropType<T>> = unknown extends D\n  ? P\n  : (NonNullable<P> | D)\n\n/**\n * Like `Partial<T>` but doesn't care what the value is\n */\ntype PartialKeys<T> = { [P in keyof T]?: unknown }\n\n// Copied from Vue\ntype InferPropType<T> = [T] extends [null]\n  ? any // null & true would fail to infer\n  : [T] extends [{ type: null | true }]\n    // As TS issue https://github.com/Microsoft/TypeScript/issues/14829\n    // somehow `ObjectConstructor` when inferred from { (): T } becomes `any`\n    // `BooleanConstructor` when inferred from PropConstructor(with PropMethod) becomes `Boolean`\n    ? any\n    : [T] extends [ObjectConstructor | { type: ObjectConstructor }]\n      ? Record<string, any>\n      : [T] extends [BooleanConstructor | { type: BooleanConstructor }]\n        ? boolean\n        : [T] extends [DateConstructor | { type: DateConstructor }]\n          ? Date\n          : [T] extends [(infer U)[] | { type: (infer U)[] }]\n            ? U extends DateConstructor\n              ? Date | InferPropType<U>\n              : InferPropType<U>\n            : [T] extends [Prop<infer V, infer D>]\n              ? unknown extends V\n                ? IfAny<V, V, D>\n                : V\n              : T\n", "// Composables\nimport { injectDefaults, internalUseDefaults } from '@/composables/defaults'\n\n// Utilities\nimport {\n  defineComponent as _defineComponent, // eslint-disable-line no-restricted-imports\n} from 'vue'\nimport { consoleWarn } from '@/util/console'\nimport { pick } from '@/util/helpers'\nimport { propsFactory } from '@/util/propsFactory'\n\n// Types\nimport type {\n  AllowedComponentProps,\n  Component,\n  ComponentCustomProps,\n  ComponentInjectOptions,\n  ComponentObjectPropsOptions,\n  ComponentOptions,\n  ComponentOptionsMixin,\n  ComponentOptionsWithObjectProps,\n  ComponentOptionsWithoutProps,\n  ComponentPropsOptions,\n  ComponentPublicInstance,\n  ComputedOptions,\n  DefineComponent,\n  EmitsOptions,\n  ExtractDefaultPropTypes,\n  ExtractPropTypes,\n  FunctionalComponent,\n  MethodOptions,\n  ObjectEmitsOptions,\n  SlotsType,\n  VNode,\n  VNodeChild,\n  VNodeProps,\n} from 'vue'\n\n// No props\nexport function defineComponent<\n  Props = {},\n  RawBindings = {},\n  D = {},\n  C extends ComputedOptions = {},\n  M extends MethodOptions = {},\n  Mixin extends ComponentOptionsMixin = ComponentOptionsMixin,\n  Extends extends ComponentOptionsMixin = ComponentOptionsMixin,\n  E extends EmitsOptions = {},\n  EE extends string = string,\n  I extends {} = {},\n  II extends string = string,\n  S extends SlotsType = {},\n>(\n  options: ComponentOptionsWithoutProps<\n    Props,\n    RawBindings,\n    D,\n    C,\n    M,\n    Mixin,\n    Extends,\n    E,\n    EE,\n    I,\n    II,\n    S\n  >\n): DefineComponent<Props, RawBindings, D, C, M, Mixin, Extends, E, EE>\n\n// Object Props\nexport function defineComponent<\n  PropsOptions extends Readonly<ComponentPropsOptions>,\n  RawBindings,\n  D,\n  C extends ComputedOptions = {},\n  M extends MethodOptions = {},\n  Mixin extends ComponentOptionsMixin = ComponentOptionsMixin,\n  Extends extends ComponentOptionsMixin = ComponentOptionsMixin,\n  E extends EmitsOptions = {},\n  EE extends string = string,\n  I extends {} = {},\n  II extends string = string,\n  S extends SlotsType = {},\n>(\n  options: ComponentOptionsWithObjectProps<\n    PropsOptions,\n    RawBindings,\n    D,\n    C,\n    M,\n    Mixin,\n    Extends,\n    E,\n    EE,\n    I,\n    II,\n    S\n  >\n): DefineComponent<PropsOptions, RawBindings, D, C, M, Mixin, Extends, E, EE> & FilterPropsOptions<PropsOptions>\n\n// Implementation\nexport function defineComponent (options: ComponentOptions) {\n  options._setup = options._setup ?? options.setup\n\n  if (!options.name) {\n    consoleWarn('The component is missing an explicit name, unable to generate default prop value')\n\n    return options\n  }\n\n  if (options._setup) {\n    options.props = propsFactory(options.props ?? {}, options.name)()\n    const propKeys = Object.keys(options.props).filter(key => key !== 'class' && key !== 'style')\n    options.filterProps = function filterProps (props: Record<string, any>) {\n      return pick(props, propKeys)\n    }\n\n    options.props._as = String\n    options.setup = function setup (props: Record<string, any>, ctx) {\n      const defaults = injectDefaults()\n\n      // Skip props proxy if defaults are not provided\n      if (!defaults.value) return options._setup(props, ctx)\n\n      const { props: _props, provideSubDefaults } = internalUseDefaults(props, props._as ?? options.name, defaults)\n\n      const setupBindings = options._setup(_props, ctx)\n\n      provideSubDefaults()\n\n      return setupBindings\n    }\n  }\n\n  return options\n}\n\ntype ToListeners<T extends string | number | symbol> = { [K in T]: K extends `on${infer U}` ? Uncapitalize<U> : K }[T]\n\nexport type SlotsToProps<\n  U extends RawSlots,\n  T = MakeInternalSlots<U>\n> = {\n  $children?: (\n    | VNodeChild\n    | (T extends { default: infer V } ? V : {})\n    | { [K in keyof T]?: T[K] }\n  )\n  'v-slots'?: { [K in keyof T]?: T[K] | false }\n} & {\n  [K in keyof T as `v-slot:${K & string}`]?: T[K] | false\n}\n\ntype RawSlots = Record<string, unknown>\ntype Slot<T> = [T] extends [never] ? () => VNodeChild : (arg: T) => VNodeChild\ntype VueSlot<T> = [T] extends [never] ? () => VNode[] : (arg: T) => VNode[]\ntype MakeInternalSlots<T extends RawSlots> = {\n  [K in keyof T]: Slot<T[K]>\n}\ntype MakeSlots<T extends RawSlots> = {\n  [K in keyof T]: VueSlot<T[K]>\n}\n\nexport type GenericProps<Props, Slots extends Record<string, unknown>> = {\n  $props: Props & SlotsToProps<Slots>\n  $slots: MakeSlots<Slots>\n}\n\ntype DefineComponentWithGenericProps<T extends (new (props: Record<string, any>, slots: RawSlots) => {\n  $props?: Record<string, any>\n})> = <\n  PropsOptions extends Readonly<ComponentObjectPropsOptions>,\n  RawBindings,\n  D,\n  C extends ComputedOptions = {},\n  M extends MethodOptions = {},\n  Mixin extends ComponentOptionsMixin = ComponentOptionsMixin,\n  Extends extends ComponentOptionsMixin = ComponentOptionsMixin,\n  E extends EmitsOptions = Record<string, any>,\n  EE extends string = string,\n  I extends ComponentInjectOptions = {},\n  II extends string = string,\n  // Slots extends RawSlots = ConstructorParameters<T> extends [any, infer SS extends RawSlots | undefined] ? Exclude<SS, undefined> : {},\n  Slots extends RawSlots = ConstructorParameters<T>[1],\n  S extends SlotsType = SlotsType<Partial<MakeSlots<Slots>>>,\n  III = InstanceType<T>,\n  P = III extends Record<'$props', any>\n    ? Omit<PropsOptions, keyof III['$props']>\n    : PropsOptions,\n  EEE extends EmitsOptions = E extends any[]\n    ? E\n    : III extends Record<'$props', any>\n      ? Omit<E, ToListeners<keyof III['$props']>>\n      : E,\n  Base = DefineComponent<\n    P,\n    RawBindings,\n    D,\n    C,\n    M,\n    Mixin,\n    Extends,\n    EEE,\n    EE,\n    PublicProps,\n    ExtractPropTypes<P> & ({} extends E ? {} : EmitsToProps<EEE>),\n    ExtractDefaultPropTypes<P>,\n    S\n  >\n>(\n  options: ComponentOptionsWithObjectProps<PropsOptions, RawBindings, D, C, M, Mixin, Extends, E, EE, I, II, S>\n) => Base & T & FilterPropsOptions<PropsOptions>\n\ntype DefineComponentWithSlots<Slots extends RawSlots> = <\n  PropsOptions extends Readonly<ComponentPropsOptions>,\n  RawBindings,\n  D,\n  C extends ComputedOptions = {},\n  M extends MethodOptions = {},\n  Mixin extends ComponentOptionsMixin = ComponentOptionsMixin,\n  Extends extends ComponentOptionsMixin = ComponentOptionsMixin,\n  E extends EmitsOptions = Record<string, any>,\n  EE extends string = string,\n  I extends ComponentInjectOptions = {},\n  II extends string = string,\n  S extends SlotsType = SlotsType<Partial<MakeSlots<Slots>>>,\n>(\n  options: ComponentOptionsWithObjectProps<PropsOptions, RawBindings, D, C, M, Mixin, Extends, E, EE, I, II, S>\n) => DefineComponent<\n  ExtractPropTypes<PropsOptions> & SlotsToProps<Slots>,\n  RawBindings,\n  D,\n  C,\n  M,\n  Mixin,\n  Extends,\n  E,\n  EE,\n  PublicProps,\n  ExtractPropTypes<PropsOptions> & SlotsToProps<Slots> & ({} extends E ? {} : EmitsToProps<E>),\n  ExtractDefaultPropTypes<PropsOptions>,\n  S\n> & FilterPropsOptions<PropsOptions>\n\n// No argument - simple default slot\nexport function genericComponent (exposeDefaults?: boolean): DefineComponentWithSlots<{ default: never }>\n\n// Generic constructor argument - generic props and slots\nexport function genericComponent<T extends (new (props: Record<string, any>, slots: any) => {\n  $props?: Record<string, any>\n})> (exposeDefaults?: boolean): DefineComponentWithGenericProps<T>\n\n// Slots argument - simple slots\nexport function genericComponent<\n  Slots extends RawSlots\n> (exposeDefaults?: boolean): DefineComponentWithSlots<Slots>\n\n// Implementation\nexport function genericComponent (exposeDefaults = true) {\n  return (options: any) => ((exposeDefaults ? defineComponent : _defineComponent) as any)(options)\n}\n\nexport function defineFunctionalComponent<\n  T extends FunctionalComponent<Props>,\n  PropsOptions = ComponentObjectPropsOptions,\n  Defaults = ExtractDefaultPropTypes<PropsOptions>,\n  Props = Readonly<ExtractPropTypes<PropsOptions>>,\n> (props: PropsOptions, render: T): FunctionalComponent<Partial<Defaults> & Omit<Props, keyof Defaults>> {\n  render.props = props as any\n  return render as any\n}\n\ntype EmitsToProps<T extends EmitsOptions> = T extends string[]\n  ? {\n    [K in string & `on${Capitalize<T[number]>}`]?: (...args: any[]) => any\n  }\n  : T extends ObjectEmitsOptions\n    ? {\n      [K in string &\n        `on${Capitalize<string & keyof T>}`]?: K extends `on${infer C}`\n        ? T[Uncapitalize<C>] extends null\n          ? (...args: any[]) => any\n          : (\n            ...args: T[Uncapitalize<C>] extends (...args: infer P) => any\n              ? P\n              : never\n          ) => any\n        : never\n    }\n    : {}\n\ntype PublicProps =\n  & VNodeProps\n  & AllowedComponentProps\n  & ComponentCustomProps\n\n// Adds a filterProps method to the component options\nexport interface FilterPropsOptions<PropsOptions extends Readonly<ComponentPropsOptions>, Props = ExtractPropTypes<PropsOptions>> {\n  filterProps<\n    T extends Partial<Props>,\n    U extends Exclude<keyof Props, Exclude<keyof Props, keyof T>>\n  > (props: T): Partial<Pick<T, U>>\n}\n\n// https://github.com/vuejs/core/pull/10557\nexport type ComponentInstance<T> = T extends { new (): ComponentPublicInstance<any, any, any> }\n  ? InstanceType<T>\n  : T extends FunctionalComponent<infer Props, infer Emits>\n    ? ComponentPublicInstance<Props, {}, {}, {}, {}, ShortEmitsToObject<Emits>>\n    : T extends Component<\n          infer Props,\n          infer RawBindings,\n          infer D,\n          infer C,\n          infer M\n        >\n      ? // NOTE we override Props/RawBindings/D to make sure is not `unknown`\n      ComponentPublicInstance<\n          unknown extends Props ? {} : Props,\n          unknown extends RawBindings ? {} : RawBindings,\n          unknown extends D ? {} : D,\n          C,\n          M\n        >\n      : never // not a vue Component\n\ntype ShortEmitsToObject<E> = E extends Record<string, any[]> ? {\n  [K in keyof E]: (...args: E[K]) => any;\n} : E;\n\nexport type JSXComponent<Props = any> =\n  | { new (): ComponentPublicInstance<Props> }\n  | FunctionalComponent<Props>\n", "// Utilities\nimport { includes } from '@/util/helpers'\n\nconst block = ['top', 'bottom'] as const\nconst inline = ['start', 'end', 'left', 'right'] as const\ntype Tblock = typeof block[number]\ntype Tinline = typeof inline[number]\nexport type Anchor =\n  | Tblock\n  | Tinline\n  | 'center'\n  | 'center center'\n  | `${Tblock} ${Tinline | 'center'}`\n  | `${Tinline} ${Tblock | 'center'}`\nexport type ParsedAnchor =\n  | { side: 'center', align: 'center' }\n  | { side: Tblock, align: 'left' | 'right' | 'center' }\n  | { side: 'left' | 'right', align: Tblock | 'center' }\n\n/** Parse a raw anchor string into an object */\nexport function parseAnchor (anchor: Anchor, isRtl: boolean) {\n  let [side, align] = anchor.split(' ') as [Tblock | Tinline | 'center', Tblock | Tinline | 'center' | undefined]\n  if (!align) {\n    align =\n      includes(block, side) ? 'start'\n      : includes(inline, side) ? 'top'\n      : 'center'\n  }\n\n  return {\n    side: toPhysical(side, isRtl),\n    align: toPhysical(align, isRtl),\n  } as ParsedAnchor\n}\n\nexport function toPhysical (str: 'center' | Tblock | Tinline, isRtl: boolean) {\n  if (str === 'start') return isRtl ? 'right' : 'left'\n  if (str === 'end') return isRtl ? 'left' : 'right'\n  return str\n}\n\nexport function flipSide (anchor: ParsedAnchor) {\n  return {\n    side: {\n      center: 'center',\n      top: 'bottom',\n      bottom: 'top',\n      left: 'right',\n      right: 'left',\n    }[anchor.side],\n    align: anchor.align,\n  } as ParsedAnchor\n}\n\nexport function flipAlign (anchor: ParsedAnchor) {\n  return {\n    side: anchor.side,\n    align: {\n      center: 'center',\n      top: 'bottom',\n      bottom: 'top',\n      left: 'right',\n      right: 'left',\n    }[anchor.align],\n  } as ParsedAnchor\n}\n\nexport function flipCorner (anchor: ParsedAnchor) {\n  return {\n    side: anchor.align,\n    align: anchor.side,\n  } as ParsedAnchor\n}\n\nexport function getAxis (anchor: ParsedAnchor) {\n  return includes(block, anchor.side) ? 'y' : 'x'\n}\n", "export class Box {\n  x: number\n  y: number\n  width: number\n  height: number\n\n  constructor ({ x, y, width, height }: {\n    x: number\n    y: number\n    width: number\n    height: number\n  }) {\n    this.x = x\n    this.y = y\n    this.width = width\n    this.height = height\n  }\n\n  get top () { return this.y }\n  get bottom () { return this.y + this.height }\n  get left () { return this.x }\n  get right () { return this.x + this.width }\n}\n\nexport function getOverflow (a: Box, b: Box) {\n  return {\n    x: {\n      before: Math.max(0, b.left - a.left),\n      after: Math.max(0, a.right - b.right),\n    },\n    y: {\n      before: Math.max(0, b.top - a.top),\n      after: Math.max(0, a.bottom - b.bottom),\n    },\n  }\n}\n\nexport function getTargetBox (target: HTMLElement | [x: number, y: number]): Box {\n  if (Array.isArray(target)) {\n    return new Box({\n      x: target[0],\n      y: target[1],\n      width: 0,\n      height: 0,\n    })\n  } else {\n    return target.getBoundingClientRect()\n  }\n}\n\nexport function getElementBox (el: HTMLElement) {\n  if (el === document.documentElement) {\n    if (!visualViewport) {\n      return new Box({\n        x: 0,\n        y: 0,\n        width: document.documentElement.clientWidth,\n        height: document.documentElement.clientHeight,\n      })\n    } else {\n      return new Box({\n        x: visualViewport.scale > 1 ? 0 : visualViewport.offsetLeft,\n        y: visualViewport.scale > 1 ? 0 : visualViewport.offsetTop,\n        width: visualViewport.width * visualViewport.scale,\n        height: visualViewport.height * visualViewport.scale,\n      })\n    }\n  } else {\n    const rect = el.getBoundingClientRect()\n    return new Box({\n      x: rect.x,\n      y: rect.y,\n      width: el.clientWidth,\n      height: el.clientHeight,\n    })\n  }\n}\n", "// Utilities\nimport { Box } from '@/util/box'\n\n/** @see https://stackoverflow.com/a/57876601/2074736 */\nexport function nullifyTransforms (el: HTMLElement): Box {\n  const rect = el.getBoundingClientRect()\n  const style = getComputedStyle(el)\n  const tx = style.transform\n\n  if (tx) {\n    let ta, sx, sy, dx, dy\n    if (tx.startsWith('matrix3d(')) {\n      ta = tx.slice(9, -1).split(/, /)\n      sx = Number(ta[0])\n      sy = Number(ta[5])\n      dx = Number(ta[12])\n      dy = Number(ta[13])\n    } else if (tx.startsWith('matrix(')) {\n      ta = tx.slice(7, -1).split(/, /)\n      sx = Number(ta[0])\n      sy = Number(ta[3])\n      dx = Number(ta[4])\n      dy = Number(ta[5])\n    } else {\n      return new Box(rect)\n    }\n\n    const to = style.transformOrigin\n    const x = rect.x - dx - (1 - sx) * parseFloat(to)\n    const y = rect.y - dy - (1 - sy) * parseFloat(to.slice(to.indexOf(' ') + 1))\n    const w = sx ? rect.width / sx : el.offsetWidth + 1\n    const h = sy ? rect.height / sy : el.offsetHeight + 1\n\n    return new Box({ x, y, width: w, height: h })\n  } else {\n    return new Box(rect)\n  }\n}\n\nexport function animate (\n  el: Element,\n  keyframes: Keyframe[] | PropertyIndexedKeyframes | null,\n  options?: number | KeyframeAnimationOptions\n) {\n  if (typeof el.animate === 'undefined') return { finished: Promise.resolve() }\n\n  let animation: Animation\n  try {\n    animation = el.animate(keyframes, options)\n  } catch (err) {\n    return { finished: Promise.resolve() }\n  }\n\n  if (typeof animation.finished === 'undefined') {\n    (animation as any).finished = new Promise(resolve => {\n      animation.onfinish = () => {\n        resolve(animation)\n      }\n    })\n  }\n\n  return animation\n}\n", "// Utilities\nimport { eventName, isOn } from '@/util/helpers'\n\nconst handlers = new WeakMap<HTMLElement, Set<[string, () => void]>>()\n\nexport function bindProps (el: HTMLElement, props: Record<string, any>) {\n  Object.keys(props).forEach(k => {\n    if (isOn(k)) {\n      const name = eventName(k)\n      const handler = handlers.get(el)\n      if (props[k] == null) {\n        handler?.forEach(v => {\n          const [n, fn] = v\n          if (n === name) {\n            el.removeEventListener(name, fn)\n            handler.delete(v)\n          }\n        })\n      } else if (!handler || ![...handler]?.some(v => v[0] === name && v[1] === props[k])) {\n        el.addEventListener(name, props[k])\n        const _handler = handler || new Set()\n        _handler.add([name, props[k]])\n        if (!handlers.has(el)) handlers.set(el, _handler)\n      }\n    } else {\n      if (props[k] == null) {\n        el.removeAttribute(k)\n      } else {\n        el.setAttribute(k, props[k])\n      }\n    }\n  })\n}\n\nexport function unbindProps (el: HTMLElement, props: Record<string, any>) {\n  Object.keys(props).forEach(k => {\n    if (isOn(k)) {\n      const name = eventName(k)\n      const handler = handlers.get(el)\n      handler?.forEach(v => {\n        const [n, fn] = v\n        if (n === name) {\n          el.removeEventListener(name, fn)\n          handler.delete(v)\n        }\n      })\n    } else {\n      el.removeAttribute(k)\n    }\n  })\n}\n", "/**\n * WCAG 3.0 APCA perceptual contrast algorithm from https://github.com/Myndex/SAPC-APCA\n * @licence https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document\n * @see https://www.w3.org/WAI/GL/task-forces/silver/wiki/Visual_Contrast_of_Text_Subgroup\n */\n// Types\nimport type { RGB } from '@/util'\n\n// MAGICAL NUMBERS\n\n// sRGB Conversion to Relative Luminance (Y)\n\n// Transfer Curve (aka \"Gamma\") for sRGB linearization\n// Simple power curve vs piecewise described in docs\n// Essentially, 2.4 best models actual display\n// characteristics in combination with the total method\nconst mainTRC = 2.4\n\nconst Rco = 0.2126729 // sRGB Red Coefficient (from matrix)\nconst Gco = 0.7151522 // sRGB Green Coefficient (from matrix)\nconst Bco = 0.0721750 // sRGB Blue Coefficient (from matrix)\n\n// For Finding Raw SAPC Contrast from Relative Luminance (Y)\n\n// Constants for SAPC Power Curve Exponents\n// One pair for normal text, and one for reverse\n// These are the \"beating heart\" of SAPC\nconst normBG = 0.55\nconst normTXT = 0.58\nconst revTXT = 0.57\nconst revBG = 0.62\n\n// For Clamping and Scaling Values\n\nconst blkThrs = 0.03 // Level that triggers the soft black clamp\nconst blkClmp = 1.45 // Exponent for the soft black clamp curve\nconst deltaYmin = 0.0005 // Lint trap\nconst scaleBoW = 1.25 // Scaling for dark text on light\nconst scaleWoB = 1.25 // Scaling for light text on dark\nconst loConThresh = 0.078 // Threshold for new simple offset scale\nconst loConFactor = 12.82051282051282 // = 1/0.078,\nconst loConOffset = 0.06 // The simple offset\nconst loClip = 0.001 // Output clip (lint trap #2)\n\nexport function APCAcontrast (text: RGB, background: RGB) {\n  // Linearize sRGB\n  const Rtxt = (text.r / 255) ** mainTRC\n  const Gtxt = (text.g / 255) ** mainTRC\n  const Btxt = (text.b / 255) ** mainTRC\n\n  const Rbg = (background.r / 255) ** mainTRC\n  const Gbg = (background.g / 255) ** mainTRC\n  const Bbg = (background.b / 255) ** mainTRC\n\n  // Apply the standard coefficients and sum to Y\n  let Ytxt = (Rtxt * Rco) + (Gtxt * Gco) + (Btxt * Bco)\n  let Ybg = (Rbg * Rco) + (Gbg * Gco) + (Bbg * Bco)\n\n  // Soft clamp Y when near black.\n  // Now clamping all colors to prevent crossover errors\n  if (Ytxt <= blkThrs) Ytxt += (blkThrs - Ytxt) ** blkClmp\n  if (Ybg <= blkThrs) Ybg += (blkThrs - Ybg) ** blkClmp\n\n  // Return 0 Early for extremely low ∆Y (lint trap #1)\n  if (Math.abs(Ybg - Ytxt) < deltaYmin) return 0.0\n\n  // SAPC CONTRAST\n\n  let outputContrast: number // For weighted final values\n  if (Ybg > Ytxt) {\n    // For normal polarity, black text on white\n    // Calculate the SAPC contrast value and scale\n\n    const SAPC = ((Ybg ** normBG) - (Ytxt ** normTXT)) * scaleBoW\n\n    // NEW! SAPC SmoothScale™\n    // Low Contrast Smooth Scale Rollout to prevent polarity reversal\n    // and also a low clip for very low contrasts (lint trap #2)\n    // much of this is for very low contrasts, less than 10\n    // therefore for most reversing needs, only loConOffset is important\n    outputContrast =\n      (SAPC < loClip) ? 0.0\n      : (SAPC < loConThresh) ? SAPC - SAPC * loConFactor * loConOffset\n      : SAPC - loConOffset\n  } else {\n    // For reverse polarity, light text on dark\n    // WoB should always return negative value.\n\n    const SAPC = ((Ybg ** revBG) - (Ytxt ** revTXT)) * scaleWoB\n\n    outputContrast =\n      (SAPC > -loClip) ? 0.0\n      : (SAPC > -loConThresh) ? SAPC - SAPC * loConFactor * loConOffset\n      : SAPC + loConOffset\n  }\n\n  return outputContrast * 100\n}\n", "// Types\nimport type { LAB, XYZ } from '../colorUtils'\n\nconst delta = 0.20689655172413793 // 6÷29\n\nconst cielabForwardTransform = (t: number): number => (\n  t > delta ** 3\n    ? Math.cbrt(t)\n    : (t / (3 * delta ** 2)) + 4 / 29\n)\n\nconst cielabReverseTransform = (t: number): number => (\n  t > delta\n    ? t ** 3\n    : (3 * delta ** 2) * (t - 4 / 29)\n)\n\nexport function fromXYZ (xyz: XYZ): LAB {\n  const transform = cielabForwardTransform\n  const transformedY = transform(xyz[1])\n\n  return [\n    116 * transformedY - 16,\n    500 * (transform(xyz[0] / 0.95047) - transformedY),\n    200 * (transformedY - transform(xyz[2] / 1.08883)),\n  ]\n}\n\nexport function toXYZ (lab: LAB): XYZ {\n  const transform = cielabReverseTransform\n  const Ln = (lab[0] + 16) / 116\n  return [\n    transform(Ln + lab[1] / 500) * 0.95047,\n    transform(Ln),\n    transform(Ln - lab[2] / 200) * 1.08883,\n  ]\n}\n", "// Utilities\nimport { clamp } from '@/util/helpers'\n\n// Types\nimport type { RGB, XYZ } from '../colorUtils'\n\n// For converting XYZ to sRGB\nconst srgbForwardMatrix = [\n  [3.2406, -1.5372, -0.4986],\n  [-0.9689, 1.8758, 0.0415],\n  [0.0557, -0.2040, 1.0570],\n]\n\n// Forward gamma adjust\nconst srgbForwardTransform = (C: number): number => (\n  C <= 0.0031308\n    ? C * 12.92\n    : 1.055 * C ** (1 / 2.4) - 0.055\n)\n\n// For converting sRGB to XYZ\nconst srgbReverseMatrix = [\n  [0.4124, 0.3576, 0.1805],\n  [0.2126, 0.7152, 0.0722],\n  [0.0193, 0.1192, 0.9505],\n]\n\n// Reverse gamma adjust\nconst srgbReverseTransform = (C: number): number => (\n  C <= 0.04045\n    ? C / 12.92\n    : ((C + 0.055) / 1.055) ** 2.4\n)\n\nexport function fromXYZ (xyz: XYZ): RGB {\n  const rgb = Array(3)\n  const transform = srgbForwardTransform\n  const matrix = srgbForwardMatrix\n\n  // Matrix transform, then gamma adjustment\n  for (let i = 0; i < 3; ++i) {\n    // Rescale back to [0, 255]\n    rgb[i] = Math.round(clamp(transform(\n      matrix[i][0] * xyz[0] +\n      matrix[i][1] * xyz[1] +\n      matrix[i][2] * xyz[2]\n    )) * 255)\n  }\n\n  return {\n    r: rgb[0],\n    g: rgb[1],\n    b: rgb[2],\n  }\n}\n\nexport function toXYZ ({ r, g, b }: RGB): XYZ {\n  const xyz: XYZ = [0, 0, 0]\n  const transform = srgbReverseTransform\n  const matrix = srgbReverseMatrix\n\n  // Rescale from [0, 255] to [0, 1] then adjust sRGB gamma to linear RGB\n  r = transform(r / 255)\n  g = transform(g / 255)\n  b = transform(b / 255)\n\n  // Matrix color space transform\n  for (let i = 0; i < 3; ++i) {\n    xyz[i] = matrix[i][0] * r + matrix[i][1] * g + matrix[i][2] * b\n  }\n\n  return xyz\n}\n", "// Utilities\nimport { APCAcontrast } from './color/APCA'\nimport { consoleWarn } from './console'\nimport { chunk, has, padEnd } from './helpers'\nimport * as CIELAB from '@/util/color/transformCIELAB'\nimport * as sRGB from '@/util/color/transformSRGB'\n\n// Types\nimport type { Colors } from '@/composables/theme'\n\nexport type XYZ = [number, number, number]\nexport type LAB = [number, number, number]\nexport type HSV = { h: number, s: number, v: number, a?: number }\nexport type RGB = { r: number, g: number, b: number, a?: number }\nexport type HSL = { h: number, s: number, l: number, a?: number }\nexport type Hex = string & { __hexBrand: never }\nexport type Color = string | number | HSV | RGB | HSL\n\nexport function isCssColor (color?: string | null | false): boolean {\n  return !!color && /^(#|var\\(--|(rgb|hsl)a?\\()/.test(color)\n}\n\nexport function isParsableColor (color: string): boolean {\n  return isCssColor(color) && !/^((rgb|hsl)a?\\()?var\\(--/.test(color)\n}\n\nconst cssColorRe = /^(?<fn>(?:rgb|hsl)a?)\\((?<values>.+)\\)/\nconst mappers = {\n  rgb: (r: number, g: number, b: number, a?: number) => ({ r, g, b, a }),\n  rgba: (r: number, g: number, b: number, a?: number) => ({ r, g, b, a }),\n  hsl: (h: number, s: number, l: number, a?: number) => HSLtoRGB({ h, s, l, a }),\n  hsla: (h: number, s: number, l: number, a?: number) => HSLtoRGB({ h, s, l, a }),\n  hsv: (h: number, s: number, v: number, a?: number) => HSVtoRGB({ h, s, v, a }),\n  hsva: (h: number, s: number, v: number, a?: number) => HSVtoRGB({ h, s, v, a }),\n}\n\nexport function parseColor (color: Color): RGB {\n  if (typeof color === 'number') {\n    if (isNaN(color) || color < 0 || color > 0xFFFFFF) { // int can't have opacity\n      consoleWarn(`'${color}' is not a valid hex color`)\n    }\n\n    return {\n      r: (color & 0xFF0000) >> 16,\n      g: (color & 0xFF00) >> 8,\n      b: (color & 0xFF),\n    }\n  } else if (typeof color === 'string' && cssColorRe.test(color)) {\n    const { groups } = color.match(cssColorRe)!\n    const { fn, values } = groups as { fn: keyof typeof mappers, values: string }\n    const realValues = values.split(/,\\s*|\\s*\\/\\s*|\\s+/)\n      .map((v, i) => {\n        if (\n          v.endsWith('%') ||\n          // unitless slv are %\n          (i > 0 && i < 3 && ['hsl', 'hsla', 'hsv', 'hsva'].includes(fn))\n        ) {\n          return parseFloat(v) / 100\n        } else {\n          return parseFloat(v)\n        }\n      }) as [number, number, number, number?]\n\n    return mappers[fn](...realValues)\n  } else if (typeof color === 'string') {\n    let hex = color.startsWith('#') ? color.slice(1) : color\n\n    if ([3, 4].includes(hex.length)) {\n      hex = hex.split('').map(char => char + char).join('')\n    } else if (![6, 8].includes(hex.length)) {\n      consoleWarn(`'${color}' is not a valid hex(a) color`)\n    }\n\n    const int = parseInt(hex, 16)\n    if (isNaN(int) || int < 0 || int > 0xFFFFFFFF) {\n      consoleWarn(`'${color}' is not a valid hex(a) color`)\n    }\n\n    return HexToRGB(hex as Hex)\n  } else if (typeof color === 'object') {\n    if (has(color, ['r', 'g', 'b'])) {\n      return color\n    } else if (has(color, ['h', 's', 'l'])) {\n      return HSVtoRGB(HSLtoHSV(color))\n    } else if (has(color, ['h', 's', 'v'])) {\n      return HSVtoRGB(color)\n    }\n  }\n\n  throw new TypeError(`Invalid color: ${color == null ? color : (String(color) || (color as any).constructor.name)}\\nExpected #hex, #hexa, rgb(), rgba(), hsl(), hsla(), object or number`)\n}\n\nexport function RGBToInt (color: RGB) {\n  return (color.r << 16) + (color.g << 8) + color.b\n}\n\nexport function classToHex (\n  color: string,\n  colors: Record<string, Record<string, string>>,\n  currentTheme: Partial<Colors>,\n): string {\n  const [colorName, colorModifier] = color\n    .toString().trim().replace('-', '').split(' ', 2) as (string | undefined)[]\n\n  let hexColor = ''\n  if (colorName && colorName in colors) {\n    if (colorModifier && colorModifier in colors[colorName]) {\n      hexColor = colors[colorName][colorModifier]\n    } else if ('base' in colors[colorName]) {\n      hexColor = colors[colorName].base\n    }\n  } else if (colorName && colorName in currentTheme) {\n    hexColor = currentTheme[colorName] as string\n  }\n\n  return hexColor\n}\n\n/** Converts HSVA to RGBA. Based on formula from https://en.wikipedia.org/wiki/HSL_and_HSV */\nexport function HSVtoRGB (hsva: HSV): RGB {\n  const { h, s, v, a } = hsva\n  const f = (n: number) => {\n    const k = (n + (h / 60)) % 6\n    return v - v * s * Math.max(Math.min(k, 4 - k, 1), 0)\n  }\n\n  const rgb = [f(5), f(3), f(1)].map(v => Math.round(v * 255))\n\n  return { r: rgb[0], g: rgb[1], b: rgb[2], a }\n}\n\nexport function HSLtoRGB (hsla: HSL): RGB {\n  return HSVtoRGB(HSLtoHSV(hsla))\n}\n\n/** Converts RGBA to HSVA. Based on formula from https://en.wikipedia.org/wiki/HSL_and_HSV */\nexport function RGBtoHSV (rgba: RGB): HSV {\n  if (!rgba) return { h: 0, s: 1, v: 1, a: 1 }\n\n  const r = rgba.r / 255\n  const g = rgba.g / 255\n  const b = rgba.b / 255\n  const max = Math.max(r, g, b)\n  const min = Math.min(r, g, b)\n\n  let h = 0\n\n  if (max !== min) {\n    if (max === r) {\n      h = 60 * (0 + ((g - b) / (max - min)))\n    } else if (max === g) {\n      h = 60 * (2 + ((b - r) / (max - min)))\n    } else if (max === b) {\n      h = 60 * (4 + ((r - g) / (max - min)))\n    }\n  }\n\n  if (h < 0) h = h + 360\n\n  const s = max === 0 ? 0 : (max - min) / max\n  const hsv = [h, s, max]\n\n  return { h: hsv[0], s: hsv[1], v: hsv[2], a: rgba.a }\n}\n\nexport function HSVtoHSL (hsva: HSV): HSL {\n  const { h, s, v, a } = hsva\n\n  const l = v - (v * s / 2)\n\n  const sprime = l === 1 || l === 0 ? 0 : (v - l) / Math.min(l, 1 - l)\n\n  return { h, s: sprime, l, a }\n}\n\nexport function HSLtoHSV (hsl: HSL): HSV {\n  const { h, s, l, a } = hsl\n\n  const v = l + s * Math.min(l, 1 - l)\n\n  const sprime = v === 0 ? 0 : 2 - (2 * l / v)\n\n  return { h, s: sprime, v, a }\n}\n\nexport function RGBtoCSS ({ r, g, b, a }: RGB): string {\n  return a === undefined ? `rgb(${r}, ${g}, ${b})` : `rgba(${r}, ${g}, ${b}, ${a})`\n}\n\nexport function HSVtoCSS (hsva: HSV): string {\n  return RGBtoCSS(HSVtoRGB(hsva))\n}\n\nfunction toHex (v: number) {\n  const h = Math.round(v).toString(16)\n  return ('00'.substr(0, 2 - h.length) + h).toUpperCase()\n}\n\nexport function RGBtoHex ({ r, g, b, a }: RGB): Hex {\n  return `#${[\n    toHex(r),\n    toHex(g),\n    toHex(b),\n    a !== undefined ? toHex(Math.round(a * 255)) : '',\n  ].join('')}` as Hex\n}\n\nexport function HexToRGB (hex: Hex): RGB {\n  hex = parseHex(hex)\n  let [r, g, b, a] = chunk(hex, 2).map((c: string) => parseInt(c, 16))\n  a = a === undefined ? a : (a / 255)\n\n  return { r, g, b, a }\n}\n\nexport function HexToHSV (hex: Hex): HSV {\n  const rgb = HexToRGB(hex)\n  return RGBtoHSV(rgb)\n}\n\nexport function HSVtoHex (hsva: HSV): Hex {\n  return RGBtoHex(HSVtoRGB(hsva))\n}\n\nexport function parseHex (hex: string): Hex {\n  if (hex.startsWith('#')) {\n    hex = hex.slice(1)\n  }\n\n  hex = hex.replace(/([^0-9a-f])/gi, 'F')\n\n  if (hex.length === 3 || hex.length === 4) {\n    hex = hex.split('').map(x => x + x).join('')\n  }\n\n  if (hex.length !== 6) {\n    hex = padEnd(padEnd(hex, 6), 8, 'F')\n  }\n\n  return hex as Hex\n}\n\nexport function parseGradient (\n  gradient: string,\n  colors: Record<string, Record<string, string>>,\n  currentTheme: Partial<Colors>,\n) {\n  return gradient.replace(/([a-z]+(\\s[a-z]+-[1-5])?)(?=$|,)/gi, x => {\n    return classToHex(x, colors, currentTheme) || x\n  }).replace(/(rgba\\()#[0-9a-f]+(?=,)/gi, x => {\n    return 'rgba(' + Object.values(HexToRGB(parseHex(x.replace(/rgba\\(/, '')))).slice(0, 3).join(',')\n  })\n}\n\nexport function lighten (value: RGB, amount: number): RGB {\n  const lab = CIELAB.fromXYZ(sRGB.toXYZ(value))\n  lab[0] = lab[0] + amount * 10\n\n  return sRGB.fromXYZ(CIELAB.toXYZ(lab))\n}\n\nexport function darken (value: RGB, amount: number): RGB {\n  const lab = CIELAB.fromXYZ(sRGB.toXYZ(value))\n  lab[0] = lab[0] - amount * 10\n\n  return sRGB.fromXYZ(CIELAB.toXYZ(lab))\n}\n\n/**\n * Calculate the relative luminance of a given color\n * @see https://www.w3.org/TR/WCAG20/#relativeluminancedef\n */\nexport function getLuma (color: Color) {\n  const rgb = parseColor(color)\n\n  return sRGB.toXYZ(rgb)[1]\n}\n\n/**\n * Returns the contrast ratio (1-21) between two colors.\n * @see https://www.w3.org/TR/WCAG20/#contrast-ratiodef\n */\nexport function getContrast (first: Color, second: Color) {\n  const l1 = getLuma(first)\n  const l2 = getLuma(second)\n\n  const light = Math.max(l1, l2)\n  const dark = Math.min(l1, l2)\n\n  return (light + 0.05) / (dark + 0.05)\n}\n\nexport function getForeground (color: Color) {\n  const blackContrast = Math.abs(APCAcontrast(parseColor(0), parseColor(color)))\n  const whiteContrast = Math.abs(APCAcontrast(parseColor(0xffffff), parseColor(color)))\n\n  // TODO: warn about poor color selections\n  // const contrastAsText = Math.abs(APCAcontrast(colorVal, colorToInt(theme.colors.background)))\n  // const minContrast = Math.max(blackContrast, whiteContrast)\n  // if (minContrast < 60) {\n  //   consoleInfo(`${key} theme color ${color} has poor contrast (${minContrast.toFixed()}%)`)\n  // } else if (contrastAsText < 60 && !['background', 'surface'].includes(color)) {\n  //   consoleInfo(`${key} theme color ${color} has poor contrast as text (${contrastAsText.toFixed()}%)`)\n  // }\n\n  // Prefer white text if both have an acceptable contrast ratio\n  return whiteContrast > Math.min(blackContrast, 50) ? '#fff' : '#000'\n}\n", "// Utilities\nimport { propsFactory } from '@/util/propsFactory'\n\n// Types\nimport type { PropType, StyleValue } from 'vue'\n\nexport type ClassValue = any\n\nexport interface ComponentProps {\n  class: ClassValue\n  style: StyleValue | undefined\n}\n\n// Composables\nexport const makeComponentProps = propsFactory({\n  class: [String, Array, Object] as PropType<ClassValue>,\n  style: {\n    type: [String, Array, Object] as PropType<StyleValue>,\n    default: null,\n  },\n}, 'component')\n", "// Composables\nimport { makeComponentProps } from '@/composables/component'\n\n// Utilities\nimport { camelize, capitalize, h } from 'vue'\nimport { genericComponent } from './defineComponent'\n\nexport function createSimpleFunctional (\n  klass: string,\n  tag = 'div',\n  name?: string\n) {\n  return genericComponent()({\n    name: name ?? capitalize(camelize(klass.replace(/__/g, '-'))),\n\n    props: {\n      tag: {\n        type: String,\n        default: tag,\n      },\n\n      ...makeComponentProps(),\n    },\n\n    setup (props, { slots }) {\n      return () => {\n        return h(props.tag, {\n          class: [klass, props.class],\n          style: props.style,\n        }, slots.default?.())\n      }\n    },\n  })\n}\n", "/**\n * Returns:\n *  - 'null' if the node is not attached to the DOM\n *  - the root node (HTMLDocument | ShadowRoot) otherwise\n */\nexport function attachedRoot (node: Node): null | HTMLDocument | ShadowRoot {\n  /* istanbul ignore next */\n  if (typeof node.getRootNode !== 'function') {\n    // Shadow DOM not supported (IE11), lets find the root of this node\n    while (node.parentNode) node = node.parentNode\n\n    // The root parent is the document if the node is attached to the DOM\n    if (node !== document) return null\n\n    return document\n  }\n\n  const root = node.getRootNode()\n\n  // The composed root node is the document if the node is attached to the DOM\n  if (root !== document && root.getRootNode({ composed: true }) !== document) return null\n\n  return root as HTMLDocument | ShadowRoot\n}\n", "export const standardEasing = 'cubic-bezier(0.4, 0, 0.2, 1)'\nexport const deceleratedEasing = 'cubic-bezier(0.0, 0, 0.2, 1)' // Entering\nexport const acceleratedEasing = 'cubic-bezier(0.4, 0, 1, 1)' // Leaving\n", "// Utilities\nimport { isOn } from '@/util/helpers'\n\ntype EventHandler = (event: Event) => any\n\nexport function getPrefixedEventHandlers<T extends `:${string}`> (\n  attrs: Record<string, any>,\n  suffix: T,\n  getData: EventHandler\n): Record<`${string}${T}`, EventHandler> {\n  return Object.keys(attrs)\n    .filter(key => isOn(key) && key.endsWith(suffix))\n    .reduce((acc: any, key) => {\n      acc[key.slice(0, -suffix.length)] = (event: Event) => attrs[key](event, getData(event))\n      return acc\n    }, {} as Record<`${string}${T}`, EventHandler>)\n}\n", "export function getScrollParent (el?: HTMLElement, includeHidden = false) {\n  while (el) {\n    if (includeHidden ? isPotentiallyScrollable(el) : hasScrollbar(el)) return el\n    el = el.parentElement!\n  }\n\n  return document.scrollingElement as HTMLElement\n}\n\nexport function getScrollParents (el?: Element | null, stopAt?: Element | null) {\n  const elements: HTMLElement[] = []\n\n  if (stopAt && el && !stopAt.contains(el)) return elements\n\n  while (el) {\n    if (hasScrollbar(el)) elements.push(el as HTMLElement)\n    if (el === stopAt) break\n    el = el.parentElement!\n  }\n\n  return elements\n}\n\nexport function hasScrollbar (el?: Element | null) {\n  if (!el || el.nodeType !== Node.ELEMENT_NODE) return false\n\n  const style = window.getComputedStyle(el)\n  return style.overflowY === 'scroll' || (style.overflowY === 'auto' && el.scrollHeight > el.clientHeight)\n}\n\nfunction isPotentiallyScrollable (el?: Element | null) {\n  if (!el || el.nodeType !== Node.ELEMENT_NODE) return false\n\n  const style = window.getComputedStyle(el)\n  return ['scroll', 'auto'].includes(style.overflowY)\n}\n", "export function isFixedPosition (el?: HTMLElement) {\n  while (el) {\n    if (window.getComputedStyle(el).position === 'fixed') {\n      return true\n    }\n    el = el.offsetParent as HTMLElement\n  }\n  return false\n}\n", "// Utilities\nimport { getCurrentInstance } from './getCurrentInstance'\n\n// Types\nimport type { VNode } from 'vue'\n\nexport function useRender (render: () => VNode): void {\n  const vm = getCurrentInstance('useRender') as any\n  vm.render = render\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAO,IAAMA,aAAa,OAAOC,WAAW;AACrC,IAAMC,wBAAwBF,cAAc,0BAA0BC;AACtE,IAAME,iBAAiBH,eAAe,kBAAkBC,UAAUA,OAAOG,UAAUC,iBAAiB;AACpG,IAAMC,uBAAuBN,cAAc,gBAAgBC;;;;;;;;;;;;;;;;;;;AC0B3D,SAASM,eAAgBC,KAAUC,MAA2BC,UAAqB;AACxF,QAAMC,OAAOF,KAAKG,SAAS;AAE3B,MAAID,OAAO,EAAG,QAAOH,QAAQK,SAAYH,WAAWF;AAEpD,WAASM,IAAI,GAAGA,IAAIH,MAAMG,KAAK;AAC7B,QAAIN,OAAO,MAAM;AACf,aAAOE;IACT;AACAF,UAAMA,IAAIC,KAAKK,CAAC,CAAC;EACnB;AAEA,MAAIN,OAAO,KAAM,QAAOE;AAExB,SAAOF,IAAIC,KAAKE,IAAI,CAAC,MAAME,SAAYH,WAAWF,IAAIC,KAAKE,IAAI,CAAC;AAClE;AAEO,SAASI,UAAWC,GAAQC,GAAiB;AAClD,MAAID,MAAMC,EAAG,QAAO;AAEpB,MACED,aAAaE,QACbD,aAAaC,QACbF,EAAEG,QAAQ,MAAMF,EAAEE,QAAQ,GAC1B;AAEA,WAAO;EACT;AAEA,MAAIH,MAAMI,OAAOJ,CAAC,KAAKC,MAAMG,OAAOH,CAAC,GAAG;AAEtC,WAAO;EACT;AAEA,QAAMI,QAAQD,OAAOE,KAAKN,CAAC;AAE3B,MAAIK,MAAMT,WAAWQ,OAAOE,KAAKL,CAAC,EAAEL,QAAQ;AAE1C,WAAO;EACT;AAEA,SAAOS,MAAME,MAAMC,OAAKT,UAAUC,EAAEQ,CAAC,GAAGP,EAAEO,CAAC,CAAC,CAAC;AAC/C;AAEO,SAASC,qBAAsBjB,KAAUC,MAAsBC,UAAqB;AAEzF,MAAIF,OAAO,QAAQ,CAACC,QAAQ,OAAOA,SAAS,SAAU,QAAOC;AAC7D,MAAIF,IAAIC,IAAI,MAAMI,OAAW,QAAOL,IAAIC,IAAI;AAC5CA,SAAOA,KAAKiB,QAAQ,cAAc,KAAK;AACvCjB,SAAOA,KAAKiB,QAAQ,OAAO,EAAE;AAC7B,SAAOnB,eAAeC,KAAKC,KAAKkB,MAAM,GAAG,GAAGjB,QAAQ;AACtD;AAQO,SAASkB,oBACdC,MACAC,UACApB,UACK;AACL,MAAIoB,aAAa,KAAM,QAAOD,SAAShB,SAAYH,WAAWmB;AAE9D,MAAIC,YAAY,QAAQ,OAAOA,aAAa,UAAW,QAAOpB;AAE9D,MAAImB,SAAST,OAAOS,IAAI,GAAG;AACzB,QAAI,OAAOC,aAAa,WAAY,QAAOpB;AAE3C,UAAMqB,SAAQD,SAASD,MAAMnB,QAAQ;AAErC,WAAO,OAAOqB,WAAU,cAAcrB,WAAWqB;EACnD;AAEA,MAAI,OAAOD,aAAa,SAAU,QAAOL,qBAAqBI,MAAMC,UAAUpB,QAAQ;AAEtF,MAAIsB,MAAMC,QAAQH,QAAQ,EAAG,QAAOvB,eAAesB,MAAMC,UAAUpB,QAAQ;AAE3E,MAAI,OAAOoB,aAAa,WAAY,QAAOpB;AAE3C,QAAMqB,QAAQD,SAASD,MAAMnB,QAAQ;AAErC,SAAO,OAAOqB,UAAU,cAAcrB,WAAWqB;AACnD;AAEO,SAASG,YAAatB,QAAqC;AAAA,MAArBuB,QAAKC,UAAAxB,SAAA,KAAAwB,UAAA,CAAA,MAAAvB,SAAAuB,UAAA,CAAA,IAAG;AACnD,SAAOJ,MAAMK,KAAK;IAAEzB;EAAO,GAAG,CAAC0B,GAAGC,MAAMJ,QAAQI,CAAC;AACnD;AAaO,SAASC,cAAeC,KAA0E;AAAA,MAAjCC,OAAIC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AAC7E,MAAIF,OAAO,QAAQA,QAAQ,IAAI;AAC7B,WAAOI;EACT;AACA,QAAMC,MAAMC,OAAON,GAAG;AACtB,MAAIO,MAAMF,GAAG,GAAG;AACd,WAAOG,OAAOR,GAAG;EACnB,WAAW,CAACS,SAASJ,GAAG,GAAG;AACzB,WAAOD;EACT,OAAO;AACL,WAAO,GAAGC,GAAG,GAAGJ,IAAI;EACtB;AACF;AAEO,SAASS,SAAUC,KAAsC;AAC9D,SAAOA,QAAQ,QAAQ,OAAOA,QAAQ,YAAY,CAACC,MAAMC,QAAQF,GAAG;AACtE;AAEO,SAASG,cAAeH,KAAsC;AACnE,MAAII;AACJ,SAAOJ,QAAQ,QAAQ,OAAOA,QAAQ,cACnCI,QAAQC,OAAOC,eAAeN,GAAG,OAAOK,OAAOE,aAChDH,UAAU;AAEd;AAEO,SAASI,WAAYR,KAA2E;AACrG,MAAIA,OAAO,SAASA,KAAK;AACvB,UAAMS,KAAKT,IAAIU;AACf,SAAID,yBAAIE,cAAaC,KAAKC,WAAW;AAEnC,aAAOJ,GAAGK;IACZ;AACA,WAAOL;EACT;AACA,SAAOT;AACT;AAGO,IAAMe,WAAWV,OAAOW,OAAO;EACpCC,OAAO;EACPC,KAAK;EACLC,QAAQ;EACRC,KAAK;EACLC,OAAO;EACPC,IAAI;EACJC,MAAM;EACNC,MAAM;EACNC,OAAO;EACPC,KAAK;EACLC,MAAM;EACNC,KAAK;EACLC,WAAW;EACXC,QAAQ;EACRC,QAAQ;EACRC,UAAU;EACVC,OAAO;AACT,CAAC;AAEM,IAAMC,YAAoC7B,OAAOW,OAAO;EAC7DC,OAAO;EACPC,KAAK;EACLC,QAAQ;EACRC,KAAK;EACLC,OAAO;EACPC,IAAI;EACJC,MAAM;EACNC,MAAM;EACNC,OAAO;EACPC,KAAK;EACLC,MAAM;EACNC,KAAK;EACLC,WAAW;EACXC,QAAQ;EACRC,QAAQ;EACRC,UAAU;EACVC,OAAO;AACT,CAAC;AAEM,SAASE,KAAoBC,GAAM;AACxC,SAAO/B,OAAO8B,KAAKC,CAAC;AACtB;AAEO,SAASC,IAAuBrC,KAAasC,KAAqC;AACvF,SAAOA,IAAIC,MAAMC,OAAKxC,IAAIyC,eAAeD,CAAC,CAAC;AAC7C;AAQO,SAASE,KAGb1C,KAAQ2C,OAA6B;AACtC,QAAMC,QAAa,CAAC;AAEpB,aAAWN,OAAOK,OAAO;AACvB,QAAItC,OAAOE,UAAUkC,eAAeI,KAAK7C,KAAKsC,GAAG,GAAG;AAClDM,YAAMN,GAAG,IAAItC,IAAIsC,GAAG;IACtB;EACF;AAEA,SAAOM;AACT;AAcO,SAASE,aAIb9C,KAAQ2C,OAAuBI,SAAkD;AAClF,QAAMH,QAAQvC,uBAAO2C,OAAO,IAAI;AAChC,QAAMC,OAAO5C,uBAAO2C,OAAO,IAAI;AAE/B,aAAWV,OAAOtC,KAAK;AACrB,QACE2C,MAAMO,KAAKC,UAAQA,gBAAgBC,SAC/BD,KAAKE,KAAKf,GAAG,IACba,SAASb,GACb,KAAK,EAACS,mCAASG,KAAKC,UAAQA,SAASb,OACrC;AACAM,YAAMN,GAAG,IAAItC,IAAIsC,GAAG;IACtB,OAAO;AACLW,WAAKX,GAAG,IAAItC,IAAIsC,GAAG;IACrB;EACF;AAEA,SAAO,CAACM,OAAOK,IAAI;AACrB;AAEO,SAASK,KAGbtD,KAAQ+C,SAA0B;AACnC,QAAMQ,QAAQ;IAAE,GAAGvD;EAAI;AAEvB+C,UAAQS,QAAQC,UAAQ,OAAOF,MAAME,IAAI,CAAC;AAE1C,SAAOF;AACT;AAEA,IAAMG,OAAO;AACN,IAAMC,OAAQrB,SAAgBoB,KAAKL,KAAKf,GAAG;AAElD,IAAMsB,iBAAiB,CACrB,wBACA,qBACA,kBACA,wBACA,oBACA,cACA,iBACA,yBACA,YACA,WACA,oBACA,sBACA,uBACA,iBACA,UACA,SACA,cACA,aACA,cACA,sBACA,qBACA,mBACA,gBACA,kBACA,uBACA,WACA,aACA,cACA,WACA,wBACA,eACA,eACA,cACA,eACA,aACA,gBACA,WACA,mBACA,iBACA,kBACA,kBACA,iBACA,gBACA,iBACA,eACA,WACA,YACA,YACA,iBACA,cACA,eACA,gBACA,sBACA,mBACA,mBACA,qBACA,SAAS;AAGX,IAAMC,wBAAwB,CAC5B,WACA,aACA,cACA,aACA,SACA,UACA,OACA,GAAG;AAGE,SAASC,qBAAsBC,GAA2B;AAC/D,SAAOA,EAAEC,eAAeH,sBAAsBI,SAASF,EAAEzB,GAAG;AAC9D;AAOO,SAAS4B,iBAAkBC,OAAgC;AAChE,QAAM,CAACC,QAAQC,KAAK,IAAIvB,aAAaqB,OAAO,CAACT,IAAI,CAAC;AAClD,QAAMY,cAAchB,KAAKc,QAAQR,cAAc;AAC/C,QAAM,CAACW,WAAWC,UAAU,IAAI1B,aAAauB,OAAO,CAAC,SAAS,SAAS,MAAM,QAAQ,CAAC;AACtFhE,SAAOoE,OAAOF,WAAWH,MAAM;AAC/B/D,SAAOoE,OAAOD,YAAYF,WAAW;AACrC,SAAO,CAACC,WAAWC,UAAU;AAC/B;AAcO,SAASE,YACdC,GAGqB;AACrB,SAAOA,KAAK,OACR,CAAA,IACAC,MAAMC,QAAQF,CAAC,IACbA,IAAW,CAACA,CAAC;AACrB;AASO,SAASG,SAAUC,IAAcC,OAAyB;AAC/D,MAAIC,YAAY;AAChB,QAAMC,OAAO,WAAoB;AAAA,aAAAC,OAAAC,UAAAC,QAAhBC,OAAI,IAAAC,MAAAJ,IAAA,GAAAK,OAAA,GAAAA,OAAAL,MAAAK,QAAA;AAAJF,WAAIE,IAAA,IAAAJ,UAAAI,IAAA;IAAA;AACnBC,iBAAaR,SAAS;AACtBA,gBAAYS,WAAW,MAAMX,GAAG,GAAGO,IAAI,GAAGK,MAAMX,KAAK,CAAC;EACxD;AACAE,OAAKU,QAAQ,MAAM;AACjBH,iBAAaR,SAAS;EACxB;AACAC,OAAKW,YAAYd;AACjB,SAAOG;AACT;AAaO,SAASY,MAAOC,OAAiC;AAAA,MAAlBC,MAAGC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AAAC,MAAEG,MAAGH,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AACnD,SAAOI,KAAKD,IAAIJ,KAAKK,KAAKL,IAAII,KAAKL,KAAK,CAAC;AAC3C;AAEO,SAASO,YAAaP,OAAe;AAC1C,QAAMQ,aAAaR,MAAMS,SAAS,EAAEC,KAAK;AACzC,SAAOF,WAAWG,SAAS,GAAG,IACzBH,WAAWL,SAASK,WAAWI,QAAQ,GAAG,IAAI,IAC/C;AACN;AAEO,SAASC,OAAQC,KAAaX,QAA4B;AAAA,MAAZY,OAAIb,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AAC1D,SAAOY,MAAMC,KAAKC,OAAOV,KAAKD,IAAI,GAAGF,SAASW,IAAIX,MAAM,CAAC;AAC3D;AAEO,SAASc,SAAUH,KAAaX,QAA4B;AAAA,MAAZY,OAAIb,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AAC5D,SAAOa,KAAKC,OAAOV,KAAKD,IAAI,GAAGF,SAASW,IAAIX,MAAM,CAAC,IAAIW;AACzD;AAEO,SAASI,MAAOJ,KAAuB;AAAA,MAAVK,OAAIjB,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AACzC,QAAMkB,UAAoB,CAAA;AAC1B,MAAIC,QAAQ;AACZ,SAAOA,QAAQP,IAAIX,QAAQ;AACzBiB,YAAQE,KAAKR,IAAIS,OAAOF,OAAOF,IAAI,CAAC;AACpCE,aAASF;EACX;AACA,SAAOC;AACT;AAQO,SAASI,sBAAuBC,OAAiD;AAAA,MAAlCC,OAAiBC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AACxE,MAAIF,QAAQC,MAAM;AAChB,WAAO,GAAGD,KAAK;EACjB;AAEA,QAAMK,SAASJ,SAAS,OAAO,CAAC,MAAM,MAAM,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG;AAClE,MAAIK,OAAO;AACX,SAAOC,KAAKC,IAAIR,KAAK,KAAKC,QAAQK,OAAOD,OAAOF,SAAS,GAAG;AAC1DH,aAASC;AACT,MAAEK;EACJ;AACA,SAAO,GAAGN,MAAMS,QAAQ,CAAC,CAAC,IAAIJ,OAAOC,IAAI,CAAC;AAC5C;AAEO,SAASI,YAId;AAAA,MAHAC,SAA2BT,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAC;AAAC,MAChCU,SAA2BV,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAC;AAAC,MAChCW,UAAmDX,UAAAC,SAAA,IAAAD,UAAA,CAAA,IAAAE;AAEnD,QAAMU,MAA2B,CAAC;AAElC,aAAWC,OAAOJ,QAAQ;AACxBG,QAAIC,GAAG,IAAIJ,OAAOI,GAAG;EACvB;AAEA,aAAWA,OAAOH,QAAQ;AACxB,UAAMI,iBAAiBL,OAAOI,GAAG;AACjC,UAAME,iBAAiBL,OAAOG,GAAG;AAIjC,QAAIG,cAAcF,cAAc,KAAKE,cAAcD,cAAc,GAAG;AAClEH,UAAIC,GAAG,IAAIL,UAAUM,gBAAgBC,gBAAgBJ,OAAO;AAE5D;IACF;AAEA,QAAIA,WAAWM,MAAMC,QAAQJ,cAAc,KAAKG,MAAMC,QAAQH,cAAc,GAAG;AAC7EH,UAAIC,GAAG,IAAIF,QAAQG,gBAAgBC,cAAc;AAEjD;IACF;AAEAH,QAAIC,GAAG,IAAIE;EACb;AAEA,SAAOH;AACT;AAEO,SAASO,iBAAkBC,OAAyB;AACzD,SAAOA,MAAMC,IAAIC,UAAQ;AACvB,QAAIA,KAAKC,SAASC,UAAU;AAC1B,aAAOL,iBAAiBG,KAAKG,QAAmB;IAClD,OAAO;AACL,aAAOH;IACT;EACF,CAAC,EAAEI,KAAK;AACV;AAEO,SAASC,cAAuB;AAAA,MAAVC,MAAG5B,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AACjC,MAAI2B,YAAYE,MAAMC,IAAIF,GAAG,EAAG,QAAOD,YAAYE,MAAME,IAAIH,GAAG;AAChE,QAAMI,QAAQJ,IACXK,QAAQ,YAAY,GAAG,EACvBA,QAAQ,cAAc,KAAK,EAC3BC,YAAY;AACfP,cAAYE,MAAMM,IAAIP,KAAKI,KAAK;AAChC,SAAOA;AACT;AACAL,YAAYE,QAAQ,oBAAIO,IAAoB;AAIrC,SAASC,wBACdxB,KACAyB,OAC6B;AAC7B,MAAI,CAACA,SAAS,OAAOA,UAAU,SAAU,QAAO,CAAA;AAEhD,MAAIrB,MAAMC,QAAQoB,KAAK,GAAG;AACxB,WAAOA,MAAMjB,IAAIkB,WAASF,wBAAwBxB,KAAK0B,KAAK,CAAC,EAAEb,KAAK,CAAC;EACvE,WAAWY,MAAME,UAAU;AACzB,WAAOH,wBAAwBxB,KAAKyB,MAAMG,SAAU;EACtD,WAAWxB,MAAMC,QAAQoB,MAAMb,QAAQ,GAAG;AACxC,WAAOa,MAAMb,SAASJ,IAAIkB,WAASF,wBAAwBxB,KAAK0B,KAAK,CAAC,EAAEb,KAAK,CAAC;EAChF,WAAWY,MAAMI,WAAW;AAC1B,QAAIC,OAAOC,sBAAsBN,MAAMI,UAAUG,QAAQ,EAAEC,SAASjC,GAAa,GAAG;AAClF,aAAO,CAACyB,MAAMI,SAAS;IACzB,WAAWJ,MAAMI,UAAUK,SAAS;AAClC,aAAOV,wBAAwBxB,KAAKyB,MAAMI,UAAUK,OAAO,EAAErB,KAAK,CAAC;IACrE;EACF;AAEA,SAAO,CAAA;AACT;AAAC,IAAAsB,OAAA,oBAAAC,QAAA;AAAA,IAAAC,WAAA,oBAAAD,QAAA;AAEM,IAAME,iBAAN,MAAgC;EAIrCC,YAA6BC,MAAc;AAH3CC,+BAAA,MAASN,MAAiB,CAAA,CAAE;AAC5BM,+BAAA,MAAAJ,UAAW,CAAC;AAAA,SAEiBG,OAAAA;EAAe;EAE5C,IAAIE,SAAU;AACZ,WAAOC,sBAAKR,MAAL,IAAQ,EAAE/C,WAAW,KAAKoD;EACnC;EAEAI,KAAMC,KAAQ;AACZF,0BAAKR,MAAL,IAAQ,EAAEQ,sBAAKN,UAAL,IAAY,CAAC,IAAIQ;AAC3BC,0BAAKT,UAAL,OAAiBM,sBAAKN,UAAL,IAAY,IAAI,KAAK,KAAKG,IAA/B;EACd;EAEAO,SAAe;AACb,WAAOJ,sBAAKR,MAAL,IAAQ,EAAEa,MAAML,sBAAKN,UAAL,IAAY,CAAC,EAAEY,OAAON,sBAAKR,MAAL,IAAQ,EAAEa,MAAM,GAAGL,sBAAKN,UAAL,IAAY,CAAC,CAAC;EAChF;EAEAa,QAAS;AACPP,0BAAKR,MAAL,IAAQ,EAAE/C,SAAS;AACnB0D,0BAAKT,UAAL,MAAgB,CAAJ;EACd;AACF;AAKO,SAASc,oBAAqBC,GAA4B;AAC/D,MAAI,aAAaA,GAAG;AAClB,WAAO;MAAEC,SAASD,EAAEE,QAAQ,CAAC,EAAED;MAASE,SAASH,EAAEE,QAAQ,CAAC,EAAEC;IAAQ;EACxE;AAEA,SAAO;IAAEF,SAASD,EAAEC;IAASE,SAASH,EAAEG;EAAQ;AAClD;AAaO,SAASC,iBAAoCC,QAA2B;AAC7E,QAAMC,OAAOC,SAAS,CAAC,CAAC;AACxBC,cAAY,MAAM;AAChB,UAAM1E,OAAOuE,OAAO;AACpB,eAAWzD,OAAOd,MAAM;AACtBwE,WAAK1D,GAAG,IAAId,KAAKc,GAAG;IACtB;EACF,GAAG;IAAE6D,OAAO;EAAO,CAAC;AACpB,QAAMC,MAAM,CAAC;AACb,aAAW9D,OAAO0D,MAAM;AACtBI,QAAI9D,GAAG,IAAI+D,MAAM,MAAML,KAAK1D,GAAG,CAAC;EAClC;AACA,SAAO8D;AACT;AAGO,SAAS7B,SAAU+B,KAAqBnB,KAAU;AACvD,SAAOmB,IAAI/B,SAASY,GAAG;AACzB;AAEO,SAASoB,UAAWC,UAAkB;AAC3C,SAAOA,SAAS,CAAC,EAAE7C,YAAY,IAAI6C,SAASlB,MAAM,CAAC;AACrD;AAIO,IAAMmB,YAAYA,MAA+B,CAACC,UAAUhE,KAAK;AAEjE,SAASiE,SAAUC,OAA4BC,MAAc;AAClEA,SAAO,OAAOC,WAAWD,IAAI;AAC7B,SAAO,CAAC,EAAED,MAAMC,IAAI,KAAKD,MAAM,GAAGC,IAAI,MAAM,KAAKD,MAAM,GAAGC,IAAI,SAAS,KAAKD,MAAM,GAAGC,IAAI,aAAa,KAAKD,MAAM,GAAGC,IAAI,aAAa;AACvI;AAEO,SAASE,UAA4BC,SAAgE;AAAA,WAAAC,QAAAxF,UAAAC,QAATwF,OAAI,IAAAxE,MAAAuE,QAAA,IAAAA,QAAA,IAAA,CAAA,GAAAE,QAAA,GAAAA,QAAAF,OAAAE,SAAA;AAAJD,SAAIC,QAAA,CAAA,IAAA1F,UAAA0F,KAAA;EAAA;AACrG,MAAIzE,MAAMC,QAAQqE,OAAO,GAAG;AAC1B,eAAWI,MAAKJ,SAAS;AACvBI,MAAAA,GAAE,GAAGF,IAAI;IACX;EACF,WAAW,OAAOF,YAAY,YAAY;AACxCA,YAAQ,GAAGE,IAAI;EACjB;AACF;AAEO,SAASG,kBAAmBC,IAAsC;AAAA,MAAzBC,mBAAgB9F,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AACjE,QAAM+F,UAAU,CAAC,UAAU,UAAU,8BAA8B,UAAU,YAAY,YAAY,EAClG1E,IAAI2E,OAAK,GAAGA,CAAC,GAAGF,mBAAmB,0BAA0B,EAAE,kBAAkB,EACjFG,KAAK,IAAI;AACZ,SAAO,CAAC,GAAGJ,GAAGK,iBAAiBH,OAAO,CAAC;AACzC;AAEO,SAASI,eAAgBC,UAAyBC,UAA4BC,WAA0C;AAC7H,MAAIC;AACJ,MAAIC,MAAMJ,SAASK,QAAQC,SAASC,aAA4B;AAChE,QAAMC,MAAMP,aAAa,SAAS,IAAI;AACtC,KAAG;AACDG,WAAOI;AACPL,UAAMH,SAASI,GAAG;EACpB,UAAU,CAACD,OAAOA,IAAIM,gBAAgB,QAAQ,GAAEP,uCAAYC,SAAQ,UAAUC,MAAMJ,SAASnG,UAAUuG,OAAO;AAC9G,SAAOD;AACT;AAEO,SAASO,WAAYjB,IAAaQ,UAAwD;;AAC/F,QAAMU,YAAYnB,kBAAkBC,EAAE;AAEtC,MAAIQ,YAAY,MAAM;AACpB,QAAIR,OAAOa,SAASC,iBAAiB,CAACd,GAAGmB,SAASN,SAASC,aAAa,GAAG;AACzEI,sBAAU,CAAC,MAAXA,mBAAcE;IAChB;EACF,WAAWZ,aAAa,SAAS;AAC/BU,oBAAU,CAAC,MAAXA,mBAAcE;EAChB,WAAWZ,aAAa,QAAQ;AAC9BU,oBAAUG,GAAG,EAAE,MAAfH,mBAAkBE;EACpB,WAAW,OAAOZ,aAAa,UAAU;AACvCU,oBAAUV,QAAQ,MAAlBU,mBAAqBE;EACvB,OAAO;AACL,UAAMV,MAAMJ,eAAeY,WAAWV,QAAQ;AAC9C,QAAIE,IAAKA,KAAIU,MAAM;QACdH,YAAWjB,IAAIQ,aAAa,SAAS,UAAU,MAAM;EAC5D;AACF;AAEO,SAASc,QAASzD,KAAmB;AAC1C,SAAOA,QAAQ,QAAQA,QAAQxD,UAAc,OAAOwD,QAAQ,YAAYA,IAAI0D,KAAK,MAAM;AACzF;AAEO,SAASC,OAAQ;AAAC;AAGlB,SAASC,gBAAiBzB,IAAyB0B,UAAkC;AAC1F,QAAMC,mBAAmBC,cACvB,OAAOC,QAAQ,eACf,OAAOA,IAAIC,aAAa,eACxBD,IAAIC,SAAS,YAAYJ,QAAQ,GAAG;AAEtC,MAAI,CAACC,iBAAkB,QAAO;AAE9B,MAAI;AACF,WAAO,CAAC,CAAC3B,MAAMA,GAAG+B,QAAQL,QAAQ;EACpC,SAASM,KAAK;AACZ,WAAO;EACT;AACF;AAEO,SAASC,iBAAkBC,QAAuD;AACvF,SAAOA,OAAOC,KAAKzF,WAAS;AAC1B,QAAI,CAAC0F,QAAQ1F,KAAK,EAAG,QAAO;AAC5B,QAAIA,MAAMhB,SAAS2G,QAAS,QAAO;AACnC,WAAO3F,MAAMhB,SAASC,YACpBsG,iBAAiBvF,MAAMd,QAA8B;EACzD,CAAC,IACGsG,SACA;AACN;AAEO,SAASI,MAAOC,SAAiBC,IAAgB;AACtD,MAAI,CAACZ,cAAcW,YAAY,GAAG;AAChCC,OAAG;AAEH,WAAO,MAAM;IAAC;EAChB;AAEA,QAAMC,YAAYC,OAAOC,WAAWH,IAAID,OAAO;AAE/C,SAAO,MAAMG,OAAOE,aAAaH,SAAS;AAC5C;AAEO,SAASI,qBAAsBC,OAAmBC,WAAwB;AAC/E,QAAMC,SAASF,MAAMzE;AACrB,QAAM4E,SAASH,MAAMvE;AAErB,QAAM2E,UAAUH,UAAUI,sBAAsB;AAChD,QAAMC,UAAUF,QAAQG;AACxB,QAAMC,SAASJ,QAAQK;AACvB,QAAMC,WAAWN,QAAQO;AACzB,QAAMC,YAAYR,QAAQS;AAE1B,SAAOX,UAAUI,WAAWJ,UAAUQ,YAAYP,UAAUK,UAAUL,UAAUS;AAClF;AAOO,SAASE,cAAe;AAC7B,QAAM5D,KAAK6D,WAAyD;AACpE,QAAMC,KAAMjJ,YAAyD;AACnEmF,OAAG+D,QAAQlJ;EACb;AACAiC,SAAOkH,eAAeF,IAAI,SAAS;IACjCG,YAAY;IACZ/H,KAAKA,MAAM8D,GAAG+D;IACdzH,KAAKuB,SAAOmC,GAAG+D,QAAQlG;EACzB,CAAC;AACDf,SAAOkH,eAAeF,IAAI,MAAM;IAC9BG,YAAY;IACZ/H,KAAKA,MAAMgI,WAAWlE,GAAG+D,KAAK;EAChC,CAAC;AAED,SAAOD;AACT;AAEO,SAASK,eAAgB/F,GAAkB;AAChD,QAAMgG,kBAAkBhG,EAAEpD,IAAIZ,WAAW;AACzC,QAAMiK,aAAa,CAACjG,EAAEkG,WAAW,CAAClG,EAAEmG,WAAW,CAACnG,EAAEoG;AAClD,SAAOJ,mBAAmBC;AAC5B;AAGO,SAASI,YAAaV,OAAoC;AAC/D,SAAO,OAAOA,UAAU,YAAY,OAAOA,UAAU,YAAY,OAAOA,UAAU,aAAa,OAAOA,UAAU;AAClH;AAEO,SAASW,cAAeC,MAAcC,oBAAmC;AAC9E,QAAMC,YAAYF,KAAKG,MAAM,EAAE,EAC5BC,OAAOC,OAAK,UAAUC,KAAKD,CAAC,CAAC,EAC7BD,OAAO,CAACC,GAAGE,GAAGC,QAASD,MAAM,KAAK,MAAMD,KAAKD,CAAC;EAC1CA,MAAM,OAAOE,MAAMC,IAAIvE,QAAQ,GAAG;EACnC,KAAKqE,KAAKD,CAAC,CAAC,EACf5E,KAAK,EAAE;AAEV,MAAIwE,uBAAuB,GAAG;AAC5B,WAAOC,UAAUC,MAAM,GAAG,EAAE,CAAC;EAC/B;AAEA,MAAIF,uBAAuB,QAAQ,OAAOK,KAAKJ,SAAS,GAAG;AACzD,UAAMO,QAAQP,UAAUC,MAAM,GAAG;AACjC,WAAO,CACLM,MAAM,CAAC,GACPA,MAAM,CAAC,EAAEC,UAAU,GAAGT,kBAAkB,CAAC,EACzCxE,KAAK,GAAG;EACZ;AAEA,SAAOyE;AACT;AAEO,SAASS,cAAkDhG,OAAoB;AACpF,QAAMvE,MAAM,CAAC;AACb,aAAWwK,QAAQjG,OAAO;AACxBvE,QAAIyK,SAASD,IAAI,CAAC,IAAejG,MAAMiG,IAAI;EAC7C;AACA,SAAOxK;AACT;AAEO,SAAS0K,iBAAkBnG,OAA4B;AAC5D,QAAMoG,oBAAoB,CAAC,WAAW,UAAU;AAChD,SAAO5I,OAAO6I,YAAY7I,OAAO8I,QAAQtG,KAAK,EAC3CyF,OAAOc,UAAA;AAAA,QAAC,CAAC7K,KAAK8K,CAAC,IAACD;AAAA,WAAKH,kBAAkBzI,SAASjC,GAAG,IAAI,CAAC,CAAC8K,IAAIA,MAAMzL;EAAS,CAAA,CAAC;AAClF;;;AC7yBO,SAAS0L,oBAAoBC,MAAcC,SAAkB;AAClE,QAAMC,KAAKC,mBAAoB;AAE/B,MAAI,CAACD,IAAI;AACP,UAAM,IAAIE,MAAM,aAAaJ,IAAI,IAAIC,WAAW,6CAA6C,EAAE;EACjG;AAEA,SAAOC;AACT;AAEO,SAASG,yBAA8C;AAAA,MAAtBL,OAAIM,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AAC7C,QAAMJ,KAAKH,oBAAmBC,IAAI,EAAES;AAEpC,SAAOC,aAAYR,yBAAIS,eAAaT,yBAAIF,KAAI;AAC9C;;;ACXO,SAASY,WAAYC,KAAwE;AAAA,MAAvCC,KAAEC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAGG,oBAAmB,YAAY;AAC/F,QAAM;IAAEC;EAAS,IAAIL;AAErB,MAAIK,YAAaN,OAA2BM,UAAU;AAEpD,WAAOA,SAASN,GAAG;EACrB;AACA,SAAOI;AACT;;;ACEO,IAAMG,iBAAsDC,OAAOC,IAAI,kBAAkB;AAEzF,SAASC,eAAgBC,SAAmD;AACjF,SAAOC,IAAID,OAAO;AACpB;AAEO,SAASE,iBAAkB;AAChC,QAAMC,WAAWC,OAAOR,cAAc;AAEtC,MAAI,CAACO,SAAU,OAAM,IAAIE,MAAM,4CAA4C;AAE3E,SAAOF;AACT;AAEO,SAASG,gBACdH,UACAH,SAMA;AACA,QAAMO,mBAAmBL,eAAe;AACxC,QAAMM,mBAAmBP,IAAIE,QAAQ;AAErC,QAAMM,cAAcC,SAAS,MAAM;AACjC,UAAMC,WAAWC,MAAMZ,mCAASW,QAAQ;AAExC,QAAIA,SAAU,QAAOJ,iBAAiBM;AAEtC,UAAMC,SAASF,MAAMZ,mCAASc,MAAM;AACpC,UAAMC,QAAQH,MAAMZ,mCAASe,KAAK;AAClC,UAAMC,OAAOJ,MAAMZ,mCAASgB,IAAI;AAEhC,QAAIR,iBAAiBK,SAAS,QAAQ,EAAEC,UAAUC,SAASC,MAAO,QAAOT,iBAAiBM;AAE1F,QAAII,aAAaC,UAAUV,iBAAiBK,OAAO;MAAEM,MAAMZ,iBAAiBM;IAAM,CAAC;AAEnF,QAAIC,OAAQ,QAAOG;AAEnB,QAAIF,SAASC,MAAM;AACjB,YAAMI,MAAMC,OAAON,SAASO,QAAQ;AAEpC,eAASC,IAAI,GAAGA,KAAKH,KAAKG,KAAK;AAC7B,YAAI,CAACN,cAAc,EAAE,UAAUA,aAAa;AAC1C;QACF;AAEAA,qBAAaA,WAAWE;MAC1B;AAEA,UAAIF,cAAc,OAAOD,SAAS,YAAYA,QAAQC,YAAY;AAChEA,qBAAaC,UAAUA,UAAUD,YAAY;UAAEE,MAAMF;QAAW,CAAC,GAAGA,WAAWD,IAAI,CAAC;MACtF;AAEA,aAAOC;IACT;AAEA,WAAOA,WAAWE,OACdD,UAAUD,WAAWE,MAAMF,UAAU,IACrCA;EACN,CAAC;AAEDO,UAAQ5B,gBAAgBa,WAAW;AAEnC,SAAOA;AACT;AAEA,SAASgB,cAAeC,OAAcC,MAAc;AAClD,SAAOD,MAAME,UAAU,OAAOF,MAAME,MAAMD,IAAI,MAAM,eAClD,OAAOD,MAAME,MAAMC,YAAYF,IAAI,CAAC,MAAM;AAC9C;AAEO,SAASG,sBAId;AAAA,MAHAF,QAA0BG,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAC;AAAC,MAC/BG,OAAaH,UAAAC,SAAA,IAAAD,UAAA,CAAA,IAAAE;AAAA,MACb9B,WAAQ4B,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG7B,eAAe;AAE1B,QAAMiC,KAAKC,oBAAmB,aAAa;AAE3CF,SAAOA,QAAQC,GAAGE,KAAKH,QAAQC,GAAGE,KAAKC;AACvC,MAAI,CAACJ,MAAM;AACT,UAAM,IAAI7B,MAAM,8CAA8C;EAChE;AAEA,QAAMkC,oBAAoB7B,SAAS,MAAA;AAvGrC;AAuG2CP,0BAASU,UAATV,mBAAiByB,MAAMY,OAAON;GAAK;AAC5E,QAAMO,SAAS,IAAIC,MAAMd,OAAO;IAC9Be,IAAKC,QAAQjB,MAAc;AAzG/B;AA0GM,YAAMkB,YAAYC,QAAQH,IAAIC,QAAQjB,IAAI;AAC1C,UAAIA,SAAS,WAAWA,SAAS,SAAS;AACxC,eAAO,EAACY,uBAAkB1B,UAAlB0B,mBAA0BZ,OAAOkB,SAAS,EAAEE,OAAOC,OAAKA,KAAK,IAAI;MAC3E;AACA,UAAIvB,cAAcU,GAAGT,OAAOC,IAAI,EAAG,QAAOkB;AAC1C,YAAMI,qBAAoBV,uBAAkB1B,UAAlB0B,mBAA0BZ;AACpD,UAAIsB,sBAAsBhB,OAAW,QAAOgB;AAC5C,YAAMC,kBAAiB/C,oBAASU,UAATV,mBAAgBgD,WAAhBhD,mBAAyBwB;AAChD,UAAIuB,mBAAmBjB,OAAW,QAAOiB;AACzC,aAAOL;IACT;EACF,CAAC;AAED,QAAMO,wBAAwBC,WAAW;AACzCC,cAAY,MAAM;AAChB,QAAIf,kBAAkB1B,OAAO;AAC3B,YAAM0C,gBAAgBC,OAAOC,QAAQlB,kBAAkB1B,KAAK,EACzDkC,OAAOW,UAAA;AAAA,YAAC,CAACC,GAAG,IAACD;AAAA,eAAKC,IAAIC,WAAWD,IAAI,CAAC,EAAEE,YAAY,CAAC;MAAC,CAAA;AACzDT,4BAAsBvC,QAAQ0C,cAAcvB,SAASwB,OAAOM,YAAYP,aAAa,IAAItB;IAC3F,OAAO;AACLmB,4BAAsBvC,QAAQoB;IAChC;EACF,CAAC;AAED,WAAS8B,qBAAsB;AAC7B,UAAMC,WAAWC,WAAWrE,gBAAgBuC,EAAE;AAC9CX,YAAQ5B,gBAAgBc,SAAS,MAAM;AACrC,aAAO0C,sBAAsBvC,QAAQK,WACnC8C,qCAAUnD,UAAS,CAAC,GACpBuC,sBAAsBvC,KACxB,IAAImD,qCAAUnD;IAChB,CAAC,CAAC;EACJ;AAEA,SAAO;IAAEe,OAAOa;IAAQsB;EAAmB;AAC7C;AAIO,SAASG,cAGd;AAAA,MAFAtC,QAA0BG,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG,CAAC;AAAC,MAC/BG,OAAaH,UAAAC,SAAA,IAAAD,UAAA,CAAA,IAAAE;AAEb,QAAM;IAAEL,OAAOa;IAAQsB;EAAmB,IAAIjC,oBAAoBF,OAAOM,IAAI;AAC7E6B,qBAAmB;AACnB,SAAOtB;AACT;;;ACnJO,SAAS0B,YAAaC,SAAuB;AAClDC,OAAK,YAAYD,OAAO,EAAE;AAC5B;AAEO,SAASE,aAAcF,SAAuB;AACnDC,OAAK,kBAAkBD,OAAO,EAAE;AAClC;AAEO,SAASG,UAAWC,UAAkBC,aAAgC;AAC3EA,gBAAcC,MAAMC,QAAQF,WAAW,IACnCA,YAAYG,MAAM,GAAG,EAAE,EAAEC,IAAIC,OAAK,IAAIA,CAAC,GAAG,EAAEC,KAAK,IAAI,IAAI,QAAQN,YAAYO,GAAG,EAAE,CAAC,MACnF,IAAIP,WAAW;AACnBJ,OAAK,sBAAsBG,QAAQ,wBAAwBC,WAAW,WAAW;AACnF;;;ACYO,SAASQ,aAEbC,OAAqBC,QAAgB;AACtC,SACEC,cAC0C;AAC1C,WAAOC,OAAOC,KAAKJ,KAAK,EAAEK,OAAY,CAACC,KAAKC,SAAS;AACnD,YAAMC,qBAAqB,OAAOR,MAAMO,IAAI,MAAM,YAAYP,MAAMO,IAAI,KAAK,QAAQ,CAACE,MAAMC,QAAQV,MAAMO,IAAI,CAAC;AAC/G,YAAMI,aAAaH,qBAAqBR,MAAMO,IAAI,IAAI;QAAEK,MAAMZ,MAAMO,IAAI;MAAE;AAE1E,UAAIL,YAAYK,QAAQL,UAAU;AAChCI,YAAIC,IAAI,IAAI;UACV,GAAGI;UACHE,SAASX,SAASK,IAAI;QACxB;MACF,OAAO;AACLD,YAAIC,IAAI,IAAII;MACd;AAEA,UAAIV,UAAU,CAACK,IAAIC,IAAI,EAAEN,QAAQ;AAC/BK,YAAIC,IAAI,EAAEN,SAASA;MACrB;AAEA,aAAOK;IACT,GAAG,CAAC,CAAC;EACP;AACF;;;AC6CO,SAASQ,iBAAiBC,SAA2B;AAC1DA,UAAQC,SAASD,QAAQC,UAAUD,QAAQE;AAE3C,MAAI,CAACF,QAAQG,MAAM;AACjBC,gBAAY,kFAAkF;AAE9F,WAAOJ;EACT;AAEA,MAAIA,QAAQC,QAAQ;AAClBD,YAAQK,QAAQC,aAAaN,QAAQK,SAAS,CAAC,GAAGL,QAAQG,IAAI,EAAE;AAChE,UAAMI,WAAWC,OAAOC,KAAKT,QAAQK,KAAK,EAAEK,OAAOC,SAAOA,QAAQ,WAAWA,QAAQ,OAAO;AAC5FX,YAAQY,cAAc,SAASA,YAAaP,OAA4B;AACtE,aAAOQ,KAAKR,OAAOE,QAAQ;IAC7B;AAEAP,YAAQK,MAAMS,MAAMC;AACpBf,YAAQE,QAAQ,SAASA,MAAOG,OAA4BW,KAAK;AAC/D,YAAMC,WAAWC,eAAe;AAGhC,UAAI,CAACD,SAASE,MAAO,QAAOnB,QAAQC,OAAOI,OAAOW,GAAG;AAErD,YAAM;QAAEX,OAAOe;QAAQC;MAAmB,IAAIC,oBAAoBjB,OAAOA,MAAMS,OAAOd,QAAQG,MAAMc,QAAQ;AAE5G,YAAMM,gBAAgBvB,QAAQC,OAAOmB,QAAQJ,GAAG;AAEhDK,yBAAmB;AAEnB,aAAOE;IACT;EACF;AAEA,SAAOvB;AACT;AA2HO,SAASwB,mBAAyC;AAAA,MAAvBC,iBAAcC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AACjD,SAAQ1B,cAAmByB,iBAAiB1B,mBAAkB8B,iBAA0B7B,OAAO;AACjG;AAEO,SAAS8B,0BAKbzB,OAAqB0B,QAAiF;AACvGA,SAAO1B,QAAQA;AACf,SAAO0B;AACT;;;AC3QA,IAAMC,QAAQ,CAAC,OAAO,QAAQ;AAC9B,IAAMC,SAAS,CAAC,SAAS,OAAO,QAAQ,OAAO;AAgBxC,SAASC,YAAaC,QAAgBC,OAAgB;AAC3D,MAAI,CAACC,MAAMC,KAAK,IAAIH,OAAOI,MAAM,GAAG;AACpC,MAAI,CAACD,OAAO;AACVA,YACEE,SAASR,OAAOK,IAAI,IAAI,UACtBG,SAASP,QAAQI,IAAI,IAAI,QACzB;EACN;AAEA,SAAO;IACLA,MAAMI,WAAWJ,MAAMD,KAAK;IAC5BE,OAAOG,WAAWH,OAAOF,KAAK;EAChC;AACF;AAEO,SAASK,WAAYC,KAAkCN,OAAgB;AAC5E,MAAIM,QAAQ,QAAS,QAAON,QAAQ,UAAU;AAC9C,MAAIM,QAAQ,MAAO,QAAON,QAAQ,SAAS;AAC3C,SAAOM;AACT;AAEO,SAASC,SAAUR,QAAsB;AAC9C,SAAO;IACLE,MAAM;MACJO,QAAQ;MACRC,KAAK;MACLC,QAAQ;MACRC,MAAM;MACNC,OAAO;IACT,EAAEb,OAAOE,IAAI;IACbC,OAAOH,OAAOG;EAChB;AACF;AAEO,SAASW,UAAWd,QAAsB;AAC/C,SAAO;IACLE,MAAMF,OAAOE;IACbC,OAAO;MACLM,QAAQ;MACRC,KAAK;MACLC,QAAQ;MACRC,MAAM;MACNC,OAAO;IACT,EAAEb,OAAOG,KAAK;EAChB;AACF;AAEO,SAASY,WAAYf,QAAsB;AAChD,SAAO;IACLE,MAAMF,OAAOG;IACbA,OAAOH,OAAOE;EAChB;AACF;AAEO,SAASc,QAAShB,QAAsB;AAC7C,SAAOK,SAASR,OAAOG,OAAOE,IAAI,IAAI,MAAM;AAC9C;;;AC5EO,IAAMe,MAAN,MAAU;EAMfC,YAAWC,MAKR;AAAA,QALU;MAAEC;MAAGC;MAAGC;MAAOC;IAK5B,IAACJ;AACC,SAAKC,IAAIA;AACT,SAAKC,IAAIA;AACT,SAAKC,QAAQA;AACb,SAAKC,SAASA;EAChB;EAEA,IAAIC,MAAO;AAAE,WAAO,KAAKH;EAAE;EAC3B,IAAII,SAAU;AAAE,WAAO,KAAKJ,IAAI,KAAKE;EAAO;EAC5C,IAAIG,OAAQ;AAAE,WAAO,KAAKN;EAAE;EAC5B,IAAIO,QAAS;AAAE,WAAO,KAAKP,IAAI,KAAKE;EAAM;AAC5C;AAEO,SAASM,YAAaC,GAAQC,GAAQ;AAC3C,SAAO;IACLV,GAAG;MACDW,QAAQC,KAAKC,IAAI,GAAGH,EAAEJ,OAAOG,EAAEH,IAAI;MACnCQ,OAAOF,KAAKC,IAAI,GAAGJ,EAAEF,QAAQG,EAAEH,KAAK;IACtC;IACAN,GAAG;MACDU,QAAQC,KAAKC,IAAI,GAAGH,EAAEN,MAAMK,EAAEL,GAAG;MACjCU,OAAOF,KAAKC,IAAI,GAAGJ,EAAEJ,SAASK,EAAEL,MAAM;IACxC;EACF;AACF;AAEO,SAASU,aAAcC,QAAmD;AAC/E,MAAIC,MAAMC,QAAQF,MAAM,GAAG;AACzB,WAAO,IAAInB,IAAI;MACbG,GAAGgB,OAAO,CAAC;MACXf,GAAGe,OAAO,CAAC;MACXd,OAAO;MACPC,QAAQ;IACV,CAAC;EACH,OAAO;AACL,WAAOa,OAAOG,sBAAsB;EACtC;AACF;AAEO,SAASC,cAAeC,IAAiB;AAC9C,MAAIA,OAAOC,SAASC,iBAAiB;AACnC,QAAI,CAACC,gBAAgB;AACnB,aAAO,IAAI3B,IAAI;QACbG,GAAG;QACHC,GAAG;QACHC,OAAOoB,SAASC,gBAAgBE;QAChCtB,QAAQmB,SAASC,gBAAgBG;MACnC,CAAC;IACH,OAAO;AACL,aAAO,IAAI7B,IAAI;QACbG,GAAGwB,eAAeG,QAAQ,IAAI,IAAIH,eAAeI;QACjD3B,GAAGuB,eAAeG,QAAQ,IAAI,IAAIH,eAAeK;QACjD3B,OAAOsB,eAAetB,QAAQsB,eAAeG;QAC7CxB,QAAQqB,eAAerB,SAASqB,eAAeG;MACjD,CAAC;IACH;EACF,OAAO;AACL,UAAMG,OAAOT,GAAGF,sBAAsB;AACtC,WAAO,IAAItB,IAAI;MACbG,GAAG8B,KAAK9B;MACRC,GAAG6B,KAAK7B;MACRC,OAAOmB,GAAGI;MACVtB,QAAQkB,GAAGK;IACb,CAAC;EACH;AACF;;;ACxEO,SAASK,kBAAmBC,IAAsB;AACvD,QAAMC,OAAOD,GAAGE,sBAAsB;AACtC,QAAMC,QAAQC,iBAAiBJ,EAAE;AACjC,QAAMK,KAAKF,MAAMG;AAEjB,MAAID,IAAI;AACN,QAAIE,IAAIC,IAAIC,IAAIC,IAAIC;AACpB,QAAIN,GAAGO,WAAW,WAAW,GAAG;AAC9BL,WAAKF,GAAGQ,MAAM,GAAG,EAAE,EAAEC,MAAM,IAAI;AAC/BN,WAAKO,OAAOR,GAAG,CAAC,CAAC;AACjBE,WAAKM,OAAOR,GAAG,CAAC,CAAC;AACjBG,WAAKK,OAAOR,GAAG,EAAE,CAAC;AAClBI,WAAKI,OAAOR,GAAG,EAAE,CAAC;IACpB,WAAWF,GAAGO,WAAW,SAAS,GAAG;AACnCL,WAAKF,GAAGQ,MAAM,GAAG,EAAE,EAAEC,MAAM,IAAI;AAC/BN,WAAKO,OAAOR,GAAG,CAAC,CAAC;AACjBE,WAAKM,OAAOR,GAAG,CAAC,CAAC;AACjBG,WAAKK,OAAOR,GAAG,CAAC,CAAC;AACjBI,WAAKI,OAAOR,GAAG,CAAC,CAAC;IACnB,OAAO;AACL,aAAO,IAAIS,IAAIf,IAAI;IACrB;AAEA,UAAMgB,KAAKd,MAAMe;AACjB,UAAMC,IAAIlB,KAAKkB,IAAIT,MAAM,IAAIF,MAAMY,WAAWH,EAAE;AAChD,UAAMI,IAAIpB,KAAKoB,IAAIV,MAAM,IAAIF,MAAMW,WAAWH,GAAGJ,MAAMI,GAAGK,QAAQ,GAAG,IAAI,CAAC,CAAC;AAC3E,UAAMC,IAAIf,KAAKP,KAAKuB,QAAQhB,KAAKR,GAAGyB,cAAc;AAClD,UAAMC,KAAIjB,KAAKR,KAAK0B,SAASlB,KAAKT,GAAG4B,eAAe;AAEpD,WAAO,IAAIZ,IAAI;MAAEG;MAAGE;MAAGG,OAAOD;MAAGI,QAAQD;IAAE,CAAC;EAC9C,OAAO;AACL,WAAO,IAAIV,IAAIf,IAAI;EACrB;AACF;AAEO,SAAS4B,QACd7B,IACA8B,WACAC,SACA;AACA,MAAI,OAAO/B,GAAG6B,YAAY,YAAa,QAAO;IAAEG,UAAUC,QAAQC,QAAQ;EAAE;AAE5E,MAAIC;AACJ,MAAI;AACFA,gBAAYnC,GAAG6B,QAAQC,WAAWC,OAAO;EAC3C,SAASK,KAAK;AACZ,WAAO;MAAEJ,UAAUC,QAAQC,QAAQ;IAAE;EACvC;AAEA,MAAI,OAAOC,UAAUH,aAAa,aAAa;AAC5CG,cAAkBH,WAAW,IAAIC,QAAQC,aAAW;AACnDC,gBAAUE,WAAW,MAAM;AACzBH,gBAAQC,SAAS;MACnB;IACF,CAAC;EACH;AAEA,SAAOA;AACT;;;AC3DA,IAAMG,WAAW,oBAAIC,QAAgD;AAE9D,SAASC,UAAWC,IAAiBC,OAA4B;AACtEC,SAAOC,KAAKF,KAAK,EAAEG,QAAQC,OAAK;AANlC;AAOI,QAAIC,KAAKD,CAAC,GAAG;AACX,YAAME,OAAOC,UAAUH,CAAC;AACxB,YAAMI,UAAUZ,SAASa,IAAIV,EAAE;AAC/B,UAAIC,MAAMI,CAAC,KAAK,MAAM;AACpBI,2CAASL,QAAQO,OAAK;AACpB,gBAAM,CAACC,GAAGC,EAAE,IAAIF;AAChB,cAAIC,MAAML,MAAM;AACdP,eAAGc,oBAAoBP,MAAMM,EAAE;AAC/BJ,oBAAQM,OAAOJ,CAAC;UAClB;QACF;MACF,WAAW,CAACF,WAAW,GAAC,MAAC,GAAGA,OAAO,MAAX,mBAAcO,KAAKL,OAAKA,EAAE,CAAC,MAAMJ,QAAQI,EAAE,CAAC,MAAMV,MAAMI,CAAC,KAAI;AACnFL,WAAGiB,iBAAiBV,MAAMN,MAAMI,CAAC,CAAC;AAClC,cAAMa,WAAWT,WAAW,oBAAIU,IAAI;AACpCD,iBAASE,IAAI,CAACb,MAAMN,MAAMI,CAAC,CAAC,CAAC;AAC7B,YAAI,CAACR,SAASwB,IAAIrB,EAAE,EAAGH,UAASyB,IAAItB,IAAIkB,QAAQ;MAClD;IACF,OAAO;AACL,UAAIjB,MAAMI,CAAC,KAAK,MAAM;AACpBL,WAAGuB,gBAAgBlB,CAAC;MACtB,OAAO;AACLL,WAAGwB,aAAanB,GAAGJ,MAAMI,CAAC,CAAC;MAC7B;IACF;EACF,CAAC;AACH;AAEO,SAASoB,YAAazB,IAAiBC,OAA4B;AACxEC,SAAOC,KAAKF,KAAK,EAAEG,QAAQC,OAAK;AAC9B,QAAIC,KAAKD,CAAC,GAAG;AACX,YAAME,OAAOC,UAAUH,CAAC;AACxB,YAAMI,UAAUZ,SAASa,IAAIV,EAAE;AAC/BS,yCAASL,QAAQO,OAAK;AACpB,cAAM,CAACC,GAAGC,EAAE,IAAIF;AAChB,YAAIC,MAAML,MAAM;AACdP,aAAGc,oBAAoBP,MAAMM,EAAE;AAC/BJ,kBAAQM,OAAOJ,CAAC;QAClB;MACF;IACF,OAAO;AACLX,SAAGuB,gBAAgBlB,CAAC;IACtB;EACF,CAAC;AACH;;;AClCA,IAAMqB,UAAU;AAEhB,IAAMC,MAAM;AACZ,IAAMC,MAAM;AACZ,IAAMC,MAAM;AAOZ,IAAMC,SAAS;AACf,IAAMC,UAAU;AAChB,IAAMC,SAAS;AACf,IAAMC,QAAQ;AAId,IAAMC,UAAU;AAChB,IAAMC,UAAU;AAChB,IAAMC,YAAY;AAClB,IAAMC,WAAW;AACjB,IAAMC,WAAW;AACjB,IAAMC,cAAc;AACpB,IAAMC,cAAc;AACpB,IAAMC,cAAc;AACpB,IAAMC,SAAS;AAER,SAASC,aAAcC,MAAWC,YAAiB;AAExD,QAAMC,QAAQF,KAAKG,IAAI,QAAQrB;AAC/B,QAAMsB,QAAQJ,KAAKK,IAAI,QAAQvB;AAC/B,QAAMwB,QAAQN,KAAKO,IAAI,QAAQzB;AAE/B,QAAM0B,OAAOP,WAAWE,IAAI,QAAQrB;AACpC,QAAM2B,OAAOR,WAAWI,IAAI,QAAQvB;AACpC,QAAM4B,OAAOT,WAAWM,IAAI,QAAQzB;AAGpC,MAAI6B,OAAQT,OAAOnB,MAAQqB,OAAOpB,MAAQsB,OAAOrB;AACjD,MAAI2B,MAAOJ,MAAMzB,MAAQ0B,MAAMzB,MAAQ0B,MAAMzB;AAI7C,MAAI0B,QAAQrB,QAASqB,UAASrB,UAAUqB,SAASpB;AACjD,MAAIqB,OAAOtB,QAASsB,SAAQtB,UAAUsB,QAAQrB;AAG9C,MAAIsB,KAAKC,IAAIF,MAAMD,IAAI,IAAInB,UAAW,QAAO;AAI7C,MAAIuB;AACJ,MAAIH,MAAMD,MAAM;AAId,UAAMK,QAASJ,OAAO1B,SAAWyB,QAAQxB,WAAYM;AAOrDsB,qBACGC,OAAOlB,SAAU,IACfkB,OAAOrB,cAAeqB,OAAOA,OAAOpB,cAAcC,cACnDmB,OAAOnB;EACb,OAAO;AAIL,UAAMmB,QAASJ,OAAOvB,QAAUsB,QAAQvB,UAAWM;AAEnDqB,qBACGC,OAAO,CAAClB,SAAU,IAChBkB,OAAO,CAACrB,cAAeqB,OAAOA,OAAOpB,cAAcC,cACpDmB,OAAOnB;EACb;AAEA,SAAOkB,iBAAiB;AAC1B;;;AC9FA,IAAME,QAAQ;AAEd,IAAMC,yBAA0BC,OAC9BA,IAAIF,SAAS,IACTG,KAAKC,KAAKF,CAAC,IACVA,KAAK,IAAIF,SAAS,KAAM,IAAI;AAGnC,IAAMK,yBAA0BH,OAC9BA,IAAIF,QACAE,KAAK,IACJ,IAAIF,SAAS,KAAME,IAAI,IAAI;AAG3B,SAASI,QAASC,KAAe;AACtC,QAAMC,YAAYP;AAClB,QAAMQ,eAAeD,UAAUD,IAAI,CAAC,CAAC;AAErC,SAAO,CACL,MAAME,eAAe,IACrB,OAAOD,UAAUD,IAAI,CAAC,IAAI,OAAO,IAAIE,eACrC,OAAOA,eAAeD,UAAUD,IAAI,CAAC,IAAI,OAAO,EAAE;AAEtD;AAEO,SAASG,MAAOC,KAAe;AACpC,QAAMH,YAAYH;AAClB,QAAMO,MAAMD,IAAI,CAAC,IAAI,MAAM;AAC3B,SAAO,CACLH,UAAUI,KAAKD,IAAI,CAAC,IAAI,GAAG,IAAI,SAC/BH,UAAUI,EAAE,GACZJ,UAAUI,KAAKD,IAAI,CAAC,IAAI,GAAG,IAAI,OAAO;AAE1C;;;AC7BA,IAAME,oBAAoB,CACxB,CAAC,QAAQ,SAAS,OAAO,GACzB,CAAC,SAAS,QAAQ,MAAM,GACxB,CAAC,QAAQ,QAAS,KAAM,CAAC;AAI3B,IAAMC,uBAAwBC,OAC5BA,KAAK,WACDA,IAAI,QACJ,QAAQA,MAAM,IAAI,OAAO;AAI/B,IAAMC,oBAAoB,CACxB,CAAC,QAAQ,QAAQ,MAAM,GACvB,CAAC,QAAQ,QAAQ,MAAM,GACvB,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAI1B,IAAMC,uBAAwBF,OAC5BA,KAAK,UACDA,IAAI,UACFA,IAAI,SAAS,UAAU;AAGxB,SAASG,SAASC,KAAe;AACtC,QAAMC,MAAMC,MAAM,CAAC;AACnB,QAAMC,YAAYR;AAClB,QAAMS,SAASV;AAGf,WAASW,IAAI,GAAGA,IAAI,GAAG,EAAEA,GAAG;AAE1BJ,QAAII,CAAC,IAAIC,KAAKC,MAAMC,MAAML,UACxBC,OAAOC,CAAC,EAAE,CAAC,IAAIL,IAAI,CAAC,IACpBI,OAAOC,CAAC,EAAE,CAAC,IAAIL,IAAI,CAAC,IACpBI,OAAOC,CAAC,EAAE,CAAC,IAAIL,IAAI,CAAC,CACtB,CAAC,IAAI,GAAG;EACV;AAEA,SAAO;IACLS,GAAGR,IAAI,CAAC;IACRS,GAAGT,IAAI,CAAC;IACRU,GAAGV,IAAI,CAAC;EACV;AACF;AAEO,SAASW,OAAKC,MAAyB;AAAA,MAAvB;IAAEJ;IAAGC;IAAGC;EAAO,IAACE;AACrC,QAAMb,MAAW,CAAC,GAAG,GAAG,CAAC;AACzB,QAAMG,YAAYL;AAClB,QAAMM,SAASP;AAGfY,MAAIN,UAAUM,IAAI,GAAG;AACrBC,MAAIP,UAAUO,IAAI,GAAG;AACrBC,MAAIR,UAAUQ,IAAI,GAAG;AAGrB,WAASN,IAAI,GAAGA,IAAI,GAAG,EAAEA,GAAG;AAC1BL,QAAIK,CAAC,IAAID,OAAOC,CAAC,EAAE,CAAC,IAAII,IAAIL,OAAOC,CAAC,EAAE,CAAC,IAAIK,IAAIN,OAAOC,CAAC,EAAE,CAAC,IAAIM;EAChE;AAEA,SAAOX;AACT;;;ACtDO,SAASc,WAAYC,OAAwC;AAClE,SAAO,CAAC,CAACA,SAAS,6BAA6BC,KAAKD,KAAK;AAC3D;AAEO,SAASE,gBAAiBF,OAAwB;AACvD,SAAOD,WAAWC,KAAK,KAAK,CAAC,2BAA2BC,KAAKD,KAAK;AACpE;AAEA,IAAMG,aAAa;AACnB,IAAMC,UAAU;EACdC,KAAKA,CAACC,GAAWC,GAAWC,GAAWC,OAAgB;IAAEH;IAAGC;IAAGC;IAAGC;EAAE;EACpEC,MAAMA,CAACJ,GAAWC,GAAWC,GAAWC,OAAgB;IAAEH;IAAGC;IAAGC;IAAGC;EAAE;EACrEE,KAAKA,CAACC,IAAWC,GAAWC,GAAWL,MAAeM,SAAS;IAAEH,GAAAA;IAAGC;IAAGC;IAAGL;EAAE,CAAC;EAC7EO,MAAMA,CAACJ,IAAWC,GAAWC,GAAWL,MAAeM,SAAS;IAAEH,GAAAA;IAAGC;IAAGC;IAAGL;EAAE,CAAC;EAC9EQ,KAAKA,CAACL,IAAWC,GAAWK,GAAWT,MAAeU,SAAS;IAAEP,GAAAA;IAAGC;IAAGK;IAAGT;EAAE,CAAC;EAC7EW,MAAMA,CAACR,IAAWC,GAAWK,GAAWT,MAAeU,SAAS;IAAEP,GAAAA;IAAGC;IAAGK;IAAGT;EAAE,CAAC;AAChF;AAEO,SAASY,WAAYrB,OAAmB;AAC7C,MAAI,OAAOA,UAAU,UAAU;AAC7B,QAAIsB,MAAMtB,KAAK,KAAKA,QAAQ,KAAKA,QAAQ,UAAU;AACjDuB,kBAAY,IAAIvB,KAAK,4BAA4B;IACnD;AAEA,WAAO;MACLM,IAAIN,QAAQ,aAAa;MACzBO,IAAIP,QAAQ,UAAW;MACvBQ,GAAIR,QAAQ;IACd;EACF,WAAW,OAAOA,UAAU,YAAYG,WAAWF,KAAKD,KAAK,GAAG;AAC9D,UAAM;MAAEwB;IAAO,IAAIxB,MAAMyB,MAAMtB,UAAU;AACzC,UAAM;MAAEuB;MAAIC;IAAO,IAAIH;AACvB,UAAMI,aAAaD,OAAOE,MAAM,mBAAmB,EAChDC,IAAI,CAACZ,GAAGa,MAAM;AACb,UACEb,EAAEc,SAAS,GAAG;MAEbD,IAAI,KAAKA,IAAI,KAAK,CAAC,OAAO,QAAQ,OAAO,MAAM,EAAEE,SAASP,EAAE,GAC7D;AACA,eAAOQ,WAAWhB,CAAC,IAAI;MACzB,OAAO;AACL,eAAOgB,WAAWhB,CAAC;MACrB;IACF,CAAC;AAEH,WAAOd,QAAQsB,EAAE,EAAE,GAAGE,UAAU;EAClC,WAAW,OAAO5B,UAAU,UAAU;AACpC,QAAImC,MAAMnC,MAAMoC,WAAW,GAAG,IAAIpC,MAAMqC,MAAM,CAAC,IAAIrC;AAEnD,QAAI,CAAC,GAAG,CAAC,EAAEiC,SAASE,IAAIG,MAAM,GAAG;AAC/BH,YAAMA,IAAIN,MAAM,EAAE,EAAEC,IAAIS,UAAQA,OAAOA,IAAI,EAAEC,KAAK,EAAE;IACtD,WAAW,CAAC,CAAC,GAAG,CAAC,EAAEP,SAASE,IAAIG,MAAM,GAAG;AACvCf,kBAAY,IAAIvB,KAAK,+BAA+B;IACtD;AAEA,UAAMyC,MAAMC,SAASP,KAAK,EAAE;AAC5B,QAAIb,MAAMmB,GAAG,KAAKA,MAAM,KAAKA,MAAM,YAAY;AAC7ClB,kBAAY,IAAIvB,KAAK,+BAA+B;IACtD;AAEA,WAAO2C,SAASR,GAAU;EAC5B,WAAW,OAAOnC,UAAU,UAAU;AACpC,QAAI4C,IAAI5C,OAAO,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG;AAC/B,aAAOA;IACT,WAAW4C,IAAI5C,OAAO,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG;AACtC,aAAOmB,SAAS0B,SAAS7C,KAAK,CAAC;IACjC,WAAW4C,IAAI5C,OAAO,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG;AACtC,aAAOmB,SAASnB,KAAK;IACvB;EACF;AAEA,QAAM,IAAI8C,UAAU,kBAAkB9C,SAAS,OAAOA,QAAS+C,OAAO/C,KAAK,KAAMA,MAAcgD,YAAYC,IAAK;qEAAwE;AAC1L;AA6BO,SAASC,SAAUC,MAAgB;AACxC,QAAM;IAAEC,GAAAA;IAAGC;IAAGC;IAAGC;EAAE,IAAIJ;AACvB,QAAMK,IAAKC,OAAc;AACvB,UAAMC,KAAKD,IAAKL,KAAI,MAAO;AAC3B,WAAOE,IAAIA,IAAID,IAAIM,KAAKC,IAAID,KAAKE,IAAIH,GAAG,IAAIA,GAAG,CAAC,GAAG,CAAC;EACtD;AAEA,QAAMI,MAAM,CAACN,EAAE,CAAC,GAAGA,EAAE,CAAC,GAAGA,EAAE,CAAC,CAAC,EAAEO,IAAIT,CAAAA,OAAKK,KAAKK,MAAMV,KAAI,GAAG,CAAC;AAE3D,SAAO;IAAEW,GAAGH,IAAI,CAAC;IAAGI,GAAGJ,IAAI,CAAC;IAAGK,GAAGL,IAAI,CAAC;IAAGP;EAAE;AAC9C;AAEO,SAASa,SAAUC,MAAgB;AACxC,SAAOnB,SAASoB,SAASD,IAAI,CAAC;AAChC;AAGO,SAASE,SAAUC,MAAgB;AACxC,MAAI,CAACA,KAAM,QAAO;IAAEpB,GAAG;IAAGC,GAAG;IAAGC,GAAG;IAAGC,GAAG;EAAE;AAE3C,QAAMU,IAAIO,KAAKP,IAAI;AACnB,QAAMC,IAAIM,KAAKN,IAAI;AACnB,QAAMC,IAAIK,KAAKL,IAAI;AACnB,QAAMP,MAAMD,KAAKC,IAAIK,GAAGC,GAAGC,CAAC;AAC5B,QAAMN,MAAMF,KAAKE,IAAII,GAAGC,GAAGC,CAAC;AAE5B,MAAIf,KAAI;AAER,MAAIQ,QAAQC,KAAK;AACf,QAAID,QAAQK,GAAG;AACbb,MAAAA,KAAI,MAAM,KAAMc,IAAIC,MAAMP,MAAMC;IAClC,WAAWD,QAAQM,GAAG;AACpBd,MAAAA,KAAI,MAAM,KAAMe,IAAIF,MAAML,MAAMC;IAClC,WAAWD,QAAQO,GAAG;AACpBf,MAAAA,KAAI,MAAM,KAAMa,IAAIC,MAAMN,MAAMC;IAClC;EACF;AAEA,MAAIT,KAAI,EAAGA,CAAAA,KAAIA,KAAI;AAEnB,QAAMC,IAAIO,QAAQ,IAAI,KAAKA,MAAMC,OAAOD;AACxC,QAAMa,MAAM,CAACrB,IAAGC,GAAGO,GAAG;AAEtB,SAAO;IAAER,GAAGqB,IAAI,CAAC;IAAGpB,GAAGoB,IAAI,CAAC;IAAGnB,GAAGmB,IAAI,CAAC;IAAGlB,GAAGiB,KAAKjB;EAAE;AACtD;AAEO,SAASmB,SAAUvB,MAAgB;AACxC,QAAM;IAAEC,GAAAA;IAAGC;IAAGC;IAAGC;EAAE,IAAIJ;AAEvB,QAAMwB,IAAIrB,IAAKA,IAAID,IAAI;AAEvB,QAAMuB,SAASD,MAAM,KAAKA,MAAM,IAAI,KAAKrB,IAAIqB,KAAKhB,KAAKE,IAAIc,GAAG,IAAIA,CAAC;AAEnE,SAAO;IAAEvB,GAAAA;IAAGC,GAAGuB;IAAQD;IAAGpB;EAAE;AAC9B;AAEO,SAASe,SAAUO,KAAe;AACvC,QAAM;IAAEzB,GAAAA;IAAGC;IAAGsB;IAAGpB;EAAE,IAAIsB;AAEvB,QAAMvB,IAAIqB,IAAItB,IAAIM,KAAKE,IAAIc,GAAG,IAAIA,CAAC;AAEnC,QAAMC,SAAStB,MAAM,IAAI,IAAI,IAAK,IAAIqB,IAAIrB;AAE1C,SAAO;IAAEF,GAAAA;IAAGC,GAAGuB;IAAQtB;IAAGC;EAAE;AAC9B;AAEO,SAASuB,SAAQC,MAA+B;AAAA,MAA7B;IAAEd;IAAGC;IAAGC;IAAGZ;EAAO,IAACwB;AAC3C,SAAOxB,MAAMyB,SAAY,OAAOf,CAAC,KAAKC,CAAC,KAAKC,CAAC,MAAM,QAAQF,CAAC,KAAKC,CAAC,KAAKC,CAAC,KAAKZ,CAAC;AAChF;AAEO,SAAS0B,SAAU9B,MAAmB;AAC3C,SAAO2B,SAAS5B,SAASC,IAAI,CAAC;AAChC;AAEA,SAAS+B,MAAO5B,GAAW;AACzB,QAAMF,KAAIO,KAAKK,MAAMV,CAAC,EAAE6B,SAAS,EAAE;AACnC,UAAQ,KAAKC,OAAO,GAAG,IAAIhC,GAAEiC,MAAM,IAAIjC,IAAGkC,YAAY;AACxD;AAEO,SAASC,SAAQC,OAA4B;AAAA,MAA1B;IAAEvB;IAAGC;IAAGC;IAAGZ;EAAO,IAACiC;AAC3C,SAAO,IAAI,CACTN,MAAMjB,CAAC,GACPiB,MAAMhB,CAAC,GACPgB,MAAMf,CAAC,GACPZ,MAAMyB,SAAYE,MAAMvB,KAAKK,MAAMT,IAAI,GAAG,CAAC,IAAI,EAAE,EACjDkC,KAAK,EAAE,CAAC;AACZ;AAEO,SAASC,SAAUC,KAAe;AACvCA,QAAMC,SAASD,GAAG;AAClB,MAAI,CAAC1B,GAAGC,GAAGC,GAAGZ,CAAC,IAAIsC,MAAMF,KAAK,CAAC,EAAE5B,IAAK+B,OAAcC,SAASD,GAAG,EAAE,CAAC;AACnEvC,MAAIA,MAAMyB,SAAYzB,IAAKA,IAAI;AAE/B,SAAO;IAAEU;IAAGC;IAAGC;IAAGZ;EAAE;AACtB;AAEO,SAASyC,SAAUL,KAAe;AACvC,QAAM7B,MAAM4B,SAASC,GAAG;AACxB,SAAOpB,SAAST,GAAG;AACrB;AAEO,SAASmC,SAAU9C,MAAgB;AACxC,SAAOoC,SAASrC,SAASC,IAAI,CAAC;AAChC;AAEO,SAASyC,SAAUD,KAAkB;AAC1C,MAAIA,IAAIO,WAAW,GAAG,GAAG;AACvBP,UAAMA,IAAIQ,MAAM,CAAC;EACnB;AAEAR,QAAMA,IAAIS,QAAQ,iBAAiB,GAAG;AAEtC,MAAIT,IAAIN,WAAW,KAAKM,IAAIN,WAAW,GAAG;AACxCM,UAAMA,IAAIU,MAAM,EAAE,EAAEtC,IAAIuC,OAAKA,IAAIA,CAAC,EAAEb,KAAK,EAAE;EAC7C;AAEA,MAAIE,IAAIN,WAAW,GAAG;AACpBM,UAAMY,OAAOA,OAAOZ,KAAK,CAAC,GAAG,GAAG,GAAG;EACrC;AAEA,SAAOA;AACT;AAcO,SAASa,QAASC,OAAYC,QAAqB;AACxD,QAAMC,MAAaC,QAAaC,OAAMJ,KAAK,CAAC;AAC5CE,MAAI,CAAC,IAAIA,IAAI,CAAC,IAAID,SAAS;AAE3B,SAAYE,SAAeC,MAAMF,GAAG,CAAC;AACvC;AAEO,SAASG,OAAQL,OAAYC,QAAqB;AACvD,QAAMC,MAAaC,QAAaC,OAAMJ,KAAK,CAAC;AAC5CE,MAAI,CAAC,IAAIA,IAAI,CAAC,IAAID,SAAS;AAE3B,SAAYE,SAAeC,MAAMF,GAAG,CAAC;AACvC;AAMO,SAASI,QAASC,OAAc;AACrC,QAAMC,MAAMC,WAAWF,KAAK;AAE5B,SAAYH,OAAMI,GAAG,EAAE,CAAC;AAC1B;AAMO,SAASE,YAAaC,OAAcC,QAAe;AACxD,QAAMC,KAAKP,QAAQK,KAAK;AACxB,QAAMG,KAAKR,QAAQM,MAAM;AAEzB,QAAMG,QAAQC,KAAKC,IAAIJ,IAAIC,EAAE;AAC7B,QAAMI,OAAOF,KAAKG,IAAIN,IAAIC,EAAE;AAE5B,UAAQC,QAAQ,SAASG,OAAO;AAClC;AAEO,SAASE,cAAeb,OAAc;AAC3C,QAAMc,gBAAgBL,KAAKM,IAAIC,aAAad,WAAW,CAAC,GAAGA,WAAWF,KAAK,CAAC,CAAC;AAC7E,QAAMiB,gBAAgBR,KAAKM,IAAIC,aAAad,WAAW,QAAQ,GAAGA,WAAWF,KAAK,CAAC,CAAC;AAYpF,SAAOiB,gBAAgBR,KAAKG,IAAIE,eAAe,EAAE,IAAI,SAAS;AAChE;;;ACrSO,IAAMI,qBAAqBC,aAAa;EAC7CC,OAAO,CAACC,QAAQC,OAAOC,MAAM;EAC7BC,OAAO;IACLC,MAAM,CAACJ,QAAQC,OAAOC,MAAM;IAC5BG,SAAS;EACX;AACF,GAAG,WAAW;;;ACbP,SAASC,uBACdC,OAGA;AAAA,MAFAC,MAAGC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AAAK,MACXG,OAAaH,UAAAC,SAAA,IAAAD,UAAA,CAAA,IAAAE;AAEb,SAAOE,iBAAiB,EAAE;IACxBD,MAAMA,QAAQE,WAAWC,SAASR,MAAMS,QAAQ,OAAO,GAAG,CAAC,CAAC;IAE5DC,OAAO;MACLT,KAAK;QACHU,MAAMC;QACNC,SAASZ;MACX;MAEA,GAAGa,mBAAmB;IACxB;IAEAC,MAAOL,OAAKM,MAAa;AAAA,UAAX;QAAEC;MAAM,IAACD;AACrB,aAAO,MAAM;AAzBnB;AA0BQ,eAAOE,EAAER,MAAMT,KAAK;UAClBkB,OAAO,CAACnB,OAAOU,MAAMS,KAAK;UAC1BC,OAAOV,MAAMU;QACf,IAAGH,WAAMJ,YAANI,8BAAiB;MACtB;IACF;EACF,CAAC;AACH;;;AC5BO,SAASI,aAAcC,MAA8C;AAE1E,MAAI,OAAOA,KAAKC,gBAAgB,YAAY;AAE1C,WAAOD,KAAKE,WAAYF,QAAOA,KAAKE;AAGpC,QAAIF,SAASG,SAAU,QAAO;AAE9B,WAAOA;EACT;AAEA,QAAMC,OAAOJ,KAAKC,YAAY;AAG9B,MAAIG,SAASD,YAAYC,KAAKH,YAAY;IAAEI,UAAU;EAAK,CAAC,MAAMF,SAAU,QAAO;AAEnF,SAAOC;AACT;;;ACvBO,IAAME,iBAAiB;AACvB,IAAMC,oBAAoB;AAC1B,IAAMC,oBAAoB;;;ACG1B,SAASC,yBACdC,OACAC,QACAC,SACuC;AACvC,SAAOC,OAAOC,KAAKJ,KAAK,EACrBK,OAAOC,SAAOC,KAAKD,GAAG,KAAKA,IAAIE,SAASP,MAAM,CAAC,EAC/CQ,OAAO,CAACC,KAAUJ,QAAQ;AACzBI,QAAIJ,IAAIK,MAAM,GAAG,CAACV,OAAOW,MAAM,CAAC,IAAKC,WAAiBb,MAAMM,GAAG,EAAEO,OAAOX,QAAQW,KAAK,CAAC;AACtF,WAAOH;EACT,GAAG,CAAC,CAA0C;AAClD;;;AChBO,SAASI,gBAAiBC,IAAyC;AAAA,MAAvBC,gBAAaC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AACjE,SAAOF,IAAI;AACT,QAAIC,gBAAgBI,wBAAwBL,EAAE,IAAIM,aAAaN,EAAE,EAAG,QAAOA;AAC3EA,SAAKA,GAAGO;EACV;AAEA,SAAOC,SAASC;AAClB;AAEO,SAASC,iBAAkBV,IAAqBW,QAAyB;AAC9E,QAAMC,WAA0B,CAAA;AAEhC,MAAID,UAAUX,MAAM,CAACW,OAAOE,SAASb,EAAE,EAAG,QAAOY;AAEjD,SAAOZ,IAAI;AACT,QAAIM,aAAaN,EAAE,EAAGY,UAASE,KAAKd,EAAiB;AACrD,QAAIA,OAAOW,OAAQ;AACnBX,SAAKA,GAAGO;EACV;AAEA,SAAOK;AACT;AAEO,SAASN,aAAcN,IAAqB;AACjD,MAAI,CAACA,MAAMA,GAAGe,aAAaC,KAAKC,aAAc,QAAO;AAErD,QAAMC,QAAQC,OAAOC,iBAAiBpB,EAAE;AACxC,SAAOkB,MAAMG,cAAc,YAAaH,MAAMG,cAAc,UAAUrB,GAAGsB,eAAetB,GAAGuB;AAC7F;AAEA,SAASlB,wBAAyBL,IAAqB;AACrD,MAAI,CAACA,MAAMA,GAAGe,aAAaC,KAAKC,aAAc,QAAO;AAErD,QAAMC,QAAQC,OAAOC,iBAAiBpB,EAAE;AACxC,SAAO,CAAC,UAAU,MAAM,EAAEwB,SAASN,MAAMG,SAAS;AACpD;;;ACnCO,SAASI,gBAAiBC,IAAkB;AACjD,SAAOA,IAAI;AACT,QAAIC,OAAOC,iBAAiBF,EAAE,EAAEG,aAAa,SAAS;AACpD,aAAO;IACT;AACAH,SAAKA,GAAGI;EACV;AACA,SAAO;AACT;;;ACFO,SAASC,UAAWC,QAA2B;AACpD,QAAMC,KAAKC,oBAAmB,WAAW;AACzCD,KAAGD,SAASA;AACd;", "names": ["IN_BROWSER", "window", "SUPPORTS_INTERSECTION", "SUPPORTS_TOUCH", "navigator", "maxTouchPoints", "SUPPORTS_EYE_DROPPER", "getNestedValue", "obj", "path", "fallback", "last", "length", "undefined", "i", "deepEqual", "a", "b", "Date", "getTime", "Object", "props", "keys", "every", "p", "getObjectValueByPath", "replace", "split", "getPropertyFromItem", "item", "property", "value", "Array", "isArray", "createRange", "start", "arguments", "from", "v", "k", "convertToUnit", "str", "unit", "arguments", "length", "undefined", "num", "Number", "isNaN", "String", "isFinite", "isObject", "obj", "Array", "isArray", "isPlainObject", "proto", "Object", "getPrototypeOf", "prototype", "refElement", "el", "$el", "nodeType", "Node", "TEXT_NODE", "nextElement<PERSON><PERSON>ling", "keyCodes", "freeze", "enter", "tab", "delete", "esc", "space", "up", "down", "left", "right", "end", "home", "del", "backspace", "insert", "pageup", "pagedown", "shift", "keyV<PERSON><PERSON>", "keys", "o", "has", "key", "every", "k", "hasOwnProperty", "pick", "paths", "found", "call", "pickWithRest", "exclude", "create", "rest", "some", "path", "RegExp", "test", "omit", "clone", "for<PERSON>ach", "prop", "onRE", "isOn", "bubblingEvents", "compositionIgnore<PERSON><PERSON>s", "isComposingIgnoreKey", "e", "isComposing", "includes", "filterInputAttrs", "attrs", "events", "props", "inputEvents", "rootAttrs", "inputAttrs", "assign", "wrapInArray", "v", "Array", "isArray", "debounce", "fn", "delay", "timeoutId", "wrap", "_len", "arguments", "length", "args", "Array", "_key", "clearTimeout", "setTimeout", "unref", "clear", "immediate", "clamp", "value", "min", "arguments", "length", "undefined", "max", "Math", "getDecimals", "trimmedStr", "toString", "trim", "includes", "indexOf", "padEnd", "str", "char", "repeat", "padStart", "chunk", "size", "chunked", "index", "push", "substr", "humanReadableFileSize", "bytes", "base", "arguments", "length", "undefined", "prefix", "unit", "Math", "abs", "toFixed", "mergeDeep", "source", "target", "arrayFn", "out", "key", "sourceProperty", "targetProperty", "isPlainObject", "Array", "isArray", "flattenFragments", "nodes", "map", "node", "type", "Fragment", "children", "flat", "toKebabCase", "str", "cache", "has", "get", "kebab", "replace", "toLowerCase", "set", "Map", "findChildrenWithProvide", "vnode", "child", "suspense", "ss<PERSON><PERSON><PERSON>", "component", "Object", "getOwnPropertySymbols", "provides", "includes", "subTree", "_arr", "WeakMap", "_pointer", "Circular<PERSON><PERSON>er", "constructor", "size", "_classPrivateFieldInitSpec", "isFull", "_classPrivateFieldGet", "push", "val", "_classPrivateFieldSet", "values", "slice", "concat", "clear", "getEventCoordinates", "e", "clientX", "touches", "clientY", "destructComputed", "getter", "refs", "reactive", "watchEffect", "flush", "obj", "toRef", "arr", "eventName", "propName", "EventProp", "Function", "hasEvent", "props", "name", "capitalize", "callEvent", "handler", "_len2", "args", "_key2", "h", "focusableC<PERSON><PERSON>n", "el", "filterByTabIndex", "targets", "s", "join", "querySelectorAll", "getNextElement", "elements", "location", "condition", "_el", "idx", "indexOf", "document", "activeElement", "inc", "offsetParent", "<PERSON><PERSON><PERSON><PERSON>", "focusable", "contains", "focus", "at", "isEmpty", "trim", "noop", "matchesSelector", "selector", "supportsSelector", "IN_BROWSER", "CSS", "supports", "matches", "err", "ensureValidVNode", "vnodes", "some", "isVNode", "Comment", "defer", "timeout", "cb", "timeoutId", "window", "setTimeout", "clearTimeout", "isClickInsideElement", "event", "targetDiv", "mouseX", "mouseY", "divRect", "getBoundingClientRect", "divLeft", "left", "divTop", "top", "divRight", "right", "divBottom", "bottom", "templateRef", "shallowRef", "fn", "value", "defineProperty", "enumerable", "refElement", "checkPrintable", "isPrintableChar", "noModifier", "ctrl<PERSON>ey", "metaKey", "altKey", "isPrimitive", "extractNumber", "text", "decimalDigitsLimit", "cleanText", "split", "filter", "x", "test", "i", "all", "parts", "substring", "camelizeProps", "prop", "camelize", "onlyDefinedProps", "booleanAttributes", "fromEntries", "entries", "_ref", "v", "getCurrentInstance", "name", "message", "vm", "_getCurrentInstance", "Error", "getCurrentInstanceName", "arguments", "length", "undefined", "type", "toKebabCase", "<PERSON><PERSON><PERSON>", "injectSelf", "key", "vm", "arguments", "length", "undefined", "getCurrentInstance", "provides", "DefaultsSymbol", "Symbol", "for", "createDefaults", "options", "ref", "injectDefaults", "defaults", "inject", "Error", "provideDefaults", "injectedDefaults", "providedDefaults", "newDefaults", "computed", "disabled", "unref", "value", "scoped", "reset", "root", "properties", "mergeDeep", "prev", "len", "Number", "Infinity", "i", "provide", "propIsDefined", "vnode", "prop", "props", "toKebabCase", "internalUseDefaults", "arguments", "length", "undefined", "name", "vm", "getCurrentInstance", "type", "__name", "componentDefaults", "_as", "_props", "Proxy", "get", "target", "propValue", "Reflect", "filter", "v", "_componentDefault", "_globalDefault", "global", "_subcomponentDefaults", "shallowRef", "watchEffect", "subComponents", "Object", "entries", "_ref", "key", "startsWith", "toUpperCase", "fromEntries", "provideSubDefaults", "injected", "injectSelf", "useDefaults", "console<PERSON>arn", "message", "warn", "consoleError", "deprecate", "original", "replacement", "Array", "isArray", "slice", "map", "s", "join", "at", "propsFactory", "props", "source", "defaults", "Object", "keys", "reduce", "obj", "prop", "isObjectDefinition", "Array", "isArray", "definition", "type", "default", "defineComponent", "options", "_setup", "setup", "name", "console<PERSON>arn", "props", "propsFactory", "propKeys", "Object", "keys", "filter", "key", "filterProps", "pick", "_as", "String", "ctx", "defaults", "injectDefaults", "value", "_props", "provideSubDefaults", "internalUseDefaults", "setupBindings", "genericComponent", "exposeDefaults", "arguments", "length", "undefined", "_defineComponent", "defineFunctionalComponent", "render", "block", "inline", "parseAnchor", "anchor", "isRtl", "side", "align", "split", "includes", "toPhysical", "str", "flipSide", "center", "top", "bottom", "left", "right", "flipAlign", "<PERSON><PERSON><PERSON><PERSON>", "getAxis", "Box", "constructor", "_ref", "x", "y", "width", "height", "top", "bottom", "left", "right", "getOverflow", "a", "b", "before", "Math", "max", "after", "getTargetBox", "target", "Array", "isArray", "getBoundingClientRect", "getElementBox", "el", "document", "documentElement", "visualViewport", "clientWidth", "clientHeight", "scale", "offsetLeft", "offsetTop", "rect", "nullifyTransforms", "el", "rect", "getBoundingClientRect", "style", "getComputedStyle", "tx", "transform", "ta", "sx", "sy", "dx", "dy", "startsWith", "slice", "split", "Number", "Box", "to", "transform<PERSON><PERSON>in", "x", "parseFloat", "y", "indexOf", "w", "width", "offsetWidth", "h", "height", "offsetHeight", "animate", "keyframes", "options", "finished", "Promise", "resolve", "animation", "err", "onfinish", "handlers", "WeakMap", "bindProps", "el", "props", "Object", "keys", "for<PERSON>ach", "k", "isOn", "name", "eventName", "handler", "get", "v", "n", "fn", "removeEventListener", "delete", "some", "addEventListener", "_handler", "Set", "add", "has", "set", "removeAttribute", "setAttribute", "unbindProps", "mainTRC", "Rco", "Gco", "Bco", "normBG", "normTXT", "revTXT", "revBG", "blkThrs", "blkClmp", "deltaYmin", "scaleBoW", "scaleWoB", "lo<PERSON>on<PERSON><PERSON><PERSON>", "loConFactor", "loConOffset", "loClip", "APCAcontrast", "text", "background", "Rtxt", "r", "Gtxt", "g", "Btxt", "b", "Rbg", "Gbg", "Bbg", "Ytxt", "Ybg", "Math", "abs", "outputContrast", "SAPC", "delta", "cielabForwardTransform", "t", "Math", "cbrt", "cielabReverseTransform", "fromXYZ", "xyz", "transform", "transformedY", "toXYZ", "lab", "Ln", "srgbForwardMatrix", "srgbForwardTransform", "C", "srgbReverseMatrix", "srgbReverseTransform", "fromXYZ", "xyz", "rgb", "Array", "transform", "matrix", "i", "Math", "round", "clamp", "r", "g", "b", "toXYZ", "_ref", "isCssColor", "color", "test", "isParsableColor", "cssColorRe", "mappers", "rgb", "r", "g", "b", "a", "rgba", "hsl", "h", "s", "l", "HSLtoRGB", "hsla", "hsv", "v", "HSVtoRGB", "hsva", "parseColor", "isNaN", "console<PERSON>arn", "groups", "match", "fn", "values", "realValues", "split", "map", "i", "endsWith", "includes", "parseFloat", "hex", "startsWith", "slice", "length", "char", "join", "int", "parseInt", "HexToRGB", "has", "HSLtoHSV", "TypeError", "String", "constructor", "name", "HSVtoRGB", "hsva", "h", "s", "v", "a", "f", "n", "k", "Math", "max", "min", "rgb", "map", "round", "r", "g", "b", "HSLtoRGB", "hsla", "HSLtoHSV", "RGBtoHSV", "rgba", "hsv", "HSVtoHSL", "l", "sprime", "hsl", "RGBtoCSS", "_ref", "undefined", "HSVtoCSS", "toHex", "toString", "substr", "length", "toUpperCase", "RGBtoHex", "_ref2", "join", "HexToRGB", "hex", "parseHex", "chunk", "c", "parseInt", "HexToHSV", "HSVtoHex", "startsWith", "slice", "replace", "split", "x", "padEnd", "lighten", "value", "amount", "lab", "fromXYZ", "toXYZ", "darken", "getLuma", "color", "rgb", "parseColor", "getContrast", "first", "second", "l1", "l2", "light", "Math", "max", "dark", "min", "getForeground", "blackContrast", "abs", "APCAcontrast", "whiteContrast", "makeComponentProps", "propsFactory", "class", "String", "Array", "Object", "style", "type", "default", "createSimpleFunctional", "klass", "tag", "arguments", "length", "undefined", "name", "genericComponent", "capitalize", "camelize", "replace", "props", "type", "String", "default", "makeComponentProps", "setup", "_ref", "slots", "h", "class", "style", "attachedRoot", "node", "getRootNode", "parentNode", "document", "root", "composed", "standardEasing", "deceleratedEasing", "acceleratedEasing", "getPrefixedEventHandlers", "attrs", "suffix", "getData", "Object", "keys", "filter", "key", "isOn", "endsWith", "reduce", "acc", "slice", "length", "event", "getScrollParent", "el", "includeHidden", "arguments", "length", "undefined", "isPotentiallyScrollable", "hasScrollbar", "parentElement", "document", "scrollingElement", "getScrollParents", "stopAt", "elements", "contains", "push", "nodeType", "Node", "ELEMENT_NODE", "style", "window", "getComputedStyle", "overflowY", "scrollHeight", "clientHeight", "includes", "isFixedPosition", "el", "window", "getComputedStyle", "position", "offsetParent", "useRender", "render", "vm", "getCurrentInstance"]}