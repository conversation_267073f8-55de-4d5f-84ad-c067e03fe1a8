{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../lib/index.ts"], "names": [], "mappings": ";AAAA,uEAAuE;AACvE,gEAAgE;AAChE,uCAAuC;;;AAEvC,yBAAyB;AACzB,0BAA0B;AAC1B,iCAAiC;AACjC,mDAAqD;AACrD,iDAAiD;AAEjD,yDAAkD;AAMlD,gFAAgF;AAChF,SAAS,wBAAwB,CAAC,MAAe;IAC/C,oEAAoE;IACpE,eAAe;IACf,OAAO,0BAA0B,IAAI,cAAc;QACjD,CAAC,CAAC,iEAAiE;YAChE,cAAc,CAAC,wBAAyD,CACvE,MAAM,CACP;QACH,CAAC,CAAC,KAAK,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,MAAa,gBAAgB;IAG3B,yDAAyD;IACxC,IAAI,CAAkB;IAEvC,kDAAkD;IACjC,MAAM,CAAS;IAEhC,yDAAyD;IAChD,KAAK,CAAkB;IAMhC,YACE,OAAe,EACf,aAAkC,EAClC,OAAiB;QAEjB,IAAI,IAAc,CAAC;QACnB,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;YACjC,IAAI,GAAG,aAAa,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,IAAI,GAAG,EAAE,CAAC;YACV,OAAO,GAAG,aAAa,CAAC;QAC1B,CAAC;QAED,MAAM,EAAC,KAAK,EAAE,KAAK,EAAC,GAAG,mCAAe,CAAC,aAAa,EAAE,CAAC;QACvD,IAAI,CAAC,IAAI,GAAG,IAAI,mCAAe,CAAC,KAAK,CAAC,CAAC;QAEvC,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC,EAAE;YACjE,UAAU,EAAE,EAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAC;YACjD,YAAY,EAAE,CAAC,KAAK,CAAC;SACtB,CAAC,CAAC;QAEH,wEAAwE;QACxE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QAEvC,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC;YAC/B,KAAK,EAAE,CAAC,KAAa,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE;gBAC3C,IAAI,CAAC,IAAI,CAAC,WAAW,CACnB;oBACE,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,KAAe;iBACtB,EACD,wBAAwB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CACpE,CAAC;gBACF,QAAQ,EAAE,CAAC;YACb,CAAC;YACD,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAC,IAAI,EAAE,aAAa,EAAC,CAAC;SAC1D,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,IAAI;QACF,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS;YAAE,OAAO,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAC,CAAC;QAEhE,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAmB,CAAC;QAC5D,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACrB,KAAK,QAAQ;gBACX,OAAO;oBACL,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAC;iBAChE,CAAC;YAEJ,KAAK,QAAQ;gBACX,OAAO;oBACL,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAC;iBAChE,CAAC;YAEJ,KAAK,OAAO;gBACV,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,MAAM,OAAO,CAAC,KAAK,CAAC;YAEtB,KAAK,MAAM;gBACT,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,OAAO,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED,wEAAwE;IACxE,kDAAkD;IAElD,6EAA6E;IAE7E;;;;OAIG;IACH,IAAI,CAAC,MAAgC;QACnC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAC,CAAC,CAAC;IAChD,CAAC;IAED,0DAA0D;IAClD,KAAK;QACX,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QAClB,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;CACF;AA5GD,4CA4GC;AAED;;;GAGG;AACH,SAAS,WAAW,CAClB,oBAA4B,EAC5B,OAAsB;IAEtB,0EAA0E;IAC1E,wBAAwB;IACxB,MAAM,MAAM,GAAG,oBAAoB,GAAG,KAAK,CAAC;IAC5C,IAAI,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC;QAAE,OAAO,IAAI,uBAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAE9D,MAAM,MAAM,GAAG,oBAAoB,GAAG,KAAK,CAAC;IAC5C,IAAI,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,OAAO,IAAI,uBAAM,CACf;;kBAEY,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;OACjC,EACD,EAAC,GAAG,OAAO,EAAE,IAAI,EAAE,IAAI,EAAC,CACzB,CAAC;IACJ,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,YAAY,MAAM,qBAAqB,CAAC,CAAC;AAC3D,CAAC"}