{"version": 3, "file": "async.js", "sourceRoot": "", "sources": ["../../../../lib/src/compiler/async.ts"], "names": [], "mappings": ";AAAA,uEAAuE;AACvE,gEAAgE;AAChE,uCAAuC;;;AAmMvC,8CAEC;AAnMD,iDAAoC;AACpC,+BAAgC;AAChC,8CAAyC;AAEzC,6BAA6B;AAC7B,mCAQiB;AACjB,oDAAiD;AACjD,kDAAyD;AACzD,4DAAsD;AACtD,4DAAsD;AACtD,gEAA0D;AAC1D,8DAAwD;AACxD,kCAAkC;AAIlC;;;;GAIG;AACH,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC;AAE1B,6DAA6D;AAC7D,MAAa,aAAa;IACxB,mDAAmD;IAClC,OAAO,GAAG,IAAA,qBAAK,EAC9B,+BAAe,CAAC,CAAC,CAAC,EAClB,CAAC,GAAG,+BAAe,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,EAC3C;QACE,oEAAoE;QACpE,6BAA6B;QAC7B,6EAA6E;QAC7E,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,+BAAe,CAAC,CAAC,CAAC,CAAC;QACrC,4EAA4E;QAC5E,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,CAC9B,IAAI,CAAC,OAAO,CAAC,+BAAe,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAC/C;QACD,WAAW,EAAE,IAAI;KAClB,CACF,CAAC;IAEF,+BAA+B;IACvB,aAAa,GAAG,CAAC,CAAC;IAE1B,qCAAqC;IACpB,YAAY,GAEzB,IAAI,GAAG,EAAE,CAAC;IAEd,0DAA0D;IAClD,QAAQ,GAAG,KAAK,CAAC;IAEzB,0DAA0D;IACzC,kBAAkB,CAAqB;IAExD,sCAAsC;IACrB,KAAK,GAAG,IAAI,OAAO,CAAgB,OAAO,CAAC,EAAE;QAC5D,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,yDAAyD;IACxC,OAAO,GAAG,IAAI,iBAAU,CAAS,QAAQ,CAAC,EAAE;QAC3D,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAA,qBAAS,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAE/B,yDAAyD;IACxC,OAAO,GAAG,IAAI,iBAAU,CAAS,QAAQ,CAAC,EAAE;QAC3D,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAA,qBAAS,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAE/B,oDAAoD;IAC5C,UAAU,CAAC,MAAc;QAC/B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAED,gDAAgD;IACxC,eAAe;QACrB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,KAAK,CAAC,aAAa,CAAC,2CAA2C,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,mBAAmB,CAC/B,OAA4C,EAC5C,SAAoC,EACpC,OAAyD;QAEzD,MAAM,UAAU,GAAG,MAAM,EAAE,CAAC;QAC5B,uCAAwB,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,oCAAgB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAE3D,MAAM,UAAU,GAAG,IAAA,wBAAgB,EACjC,IAAI,CAAC,aAAa,EAAE,EACpB,IAAI,CAAC,kBAAkB,EACvB;gBACE,mBAAmB,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC;gBACzD,uBAAuB,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC;gBACjE,yBAAyB,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC;gBACrE,yBAAyB,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC;aAC9D,CACF,CAAC;YACF,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,IAAA,sBAAc,EAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;YAEzE,MAAM,WAAW,GAAG,IAAI,OAAO,CAC7B,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CAClB,UAAU,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;gBACvD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;gBACtC,kEAAkE;gBAClE,yCAAyC;gBACzC,6EAA6E;gBAC7E,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC;oBAAE,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;gBACzD,IAAI,GAAG,EAAE,CAAC;oBACR,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,QAAS,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC,CAAC,CACL,CAAC;YACF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAEnC,OAAO,IAAA,6BAAqB,EAAC,MAAM,WAAW,CAAC,CAAC;QAClD,CAAC;gBAAS,CAAC;YACT,uCAAwB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,uDAAuD;IACvD,YAAY,IAAwB;QAClC,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;YACtB,MAAM,KAAK,CAAC,aAAa,CACvB,iDAAiD;gBAC/C,gDAAgD,CACnD,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3D,MAAM,iBAAiB,GAAG,IAAI,sCAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE;YACrE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,kBAAkB,GAAG,IAAI,wCAAkB,CAC9C,iBAAiB,CAAC,kBAAkB,EACpC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,MAAM,CAAC,CACzD,CAAC;IACJ,CAAC;IAED,YAAY,CACV,IAAY,EACZ,OAAoC;QAEpC,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,IAAI,oCAAgB,CAAC,OAAO,CAAC,CAAC;QAChD,OAAO,IAAI,CAAC,mBAAmB,CAC7B,IAAA,6BAAqB,EAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,EAC/C,SAAS,EACT,OAAO,CACR,CAAC;IACJ,CAAC;IAED,kBAAkB,CAChB,MAAc,EACd,OAA0C;QAE1C,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,IAAI,oCAAgB,CAAC,OAAO,CAAC,CAAC;QAChD,OAAO,IAAI,CAAC,mBAAmB,CAC7B,IAAA,+BAAuB,EAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,EACnD,SAAS,EACT,OAAO,CACR,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;QACzB,MAAM,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;CACF;AA/JD,sCA+JC;AAEM,KAAK,UAAU,iBAAiB;IACrC,OAAO,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC;AACrC,CAAC"}