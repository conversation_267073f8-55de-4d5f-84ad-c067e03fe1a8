{"version": 3, "file": "sync.js", "sourceRoot": "", "sources": ["../../../../lib/src/compiler/sync.ts"], "names": [], "mappings": ";AAAA,uEAAuE;AACvE,gEAAgE;AAChE,uCAAuC;;;AAgNvC,oCAEC;AAhND,+BAA6B;AAC7B,2DAAoD;AAEpD,6BAA6B;AAC7B,mCAOiB;AACjB,oDAAiD;AACjD,kDAAyD;AAEzD,4DAAsD;AACtD,4DAAsD;AACtD,gEAA0D;AAC1D,8DAAwD;AACxD,kCAAkC;AAKlC;;;;GAIG;AACH,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC;AAE1B,2DAA2D;AAC3D,MAAa,QAAQ;IACnB,mDAAmD;IAClC,OAAO,GAAG,IAAI,qCAAgB,CAC7C,+BAAe,CAAC,CAAC,CAAC,EAClB,CAAC,GAAG,+BAAe,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,EAC3C;QACE,oEAAoE;QACpE,6BAA6B;QAC7B,6EAA6E;QAC7E,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,+BAAe,CAAC,CAAC,CAAC,CAAC;QACrC,4EAA4E;QAC5E,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,CAC9B,IAAI,CAAC,OAAO,CAAC,+BAAe,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAC/C;QACD,WAAW,EAAE,IAAI;KAClB,CACF,CAAC;IAEF,+BAA+B;IACvB,aAAa,GAAG,CAAC,CAAC;IAE1B,oCAAoC;IACnB,WAAW,GAA4B,IAAI,GAAG,EAAE,CAAC;IAElE,yDAAyD;IACxC,OAAO,GAAG,IAAI,cAAO,EAAU,CAAC;IAEjD,yDAAyD;IACxC,OAAO,GAAG,IAAI,cAAO,EAAU,CAAC;IAEjD,0DAA0D;IAClD,QAAQ,GAAG,KAAK,CAAC;IAEzB,0DAA0D;IACzC,kBAAkB,CAAqB;IAExD,oDAAoD;IAC5C,UAAU,CAAC,MAAc;QAC/B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAED,yDAAyD;IACjD,KAAK;QACX,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACnC,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;YAChB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,OAAO,KAAK,CAAC;QACf,CAAC;QACD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC3B,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,QAAQ;gBACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC9B,OAAO,IAAI,CAAC;YAEd,KAAK,QAAQ;gBACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC9B,OAAO,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;IAED,iDAAiD;IACzC,cAAc;QACpB,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACtB,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,CAAC;IACH,CAAC;IAED;;;OAGG;IACK,kBAAkB,CACxB,OAA4C,EAC5C,SAAmC,EACnC,OAAmC;QAEnC,MAAM,UAAU,GAAG,MAAM,EAAE,CAAC;QAC5B,uCAAwB,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,oCAAgB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAE3D,MAAM,UAAU,GAAG,IAAA,wBAAgB,EACjC,IAAI,CAAC,aAAa,EAAE,EACpB,IAAI,CAAC,kBAAkB,EACvB;gBACE,mBAAmB,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC;gBACzD,uBAAuB,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC;gBACjE,yBAAyB,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC;gBACrE,yBAAyB,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC;aAC9D,CACF,CAAC;YACF,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAEjC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,IAAA,sBAAc,EAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;YAEzE,IAAI,KAAc,CAAC;YACnB,IAAI,QAA2D,CAAC;YAChE,UAAU,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;gBAC3D,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBACpC,kEAAkE;gBAClE,wCAAwC;gBACxC,6EAA6E;gBAC7E,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC;oBAAE,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;gBACxD,IAAI,MAAM,EAAE,CAAC;oBACX,KAAK,GAAG,MAAM,CAAC;gBACjB,CAAC;qBAAM,CAAC;oBACN,QAAQ,GAAG,SAAS,CAAC;gBACvB,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,SAAS,CAAC;gBACR,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC;oBAClB,MAAM,KAAK,CAAC,aAAa,CAAC,wCAAwC,CAAC,CAAC;gBACtE,CAAC;gBAED,IAAI,KAAK;oBAAE,MAAM,KAAK,CAAC;gBACvB,IAAI,QAAQ;oBAAE,OAAO,IAAA,6BAAqB,EAAC,QAAQ,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,uCAAwB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,gDAAgD;IACxC,eAAe;QACrB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,KAAK,CAAC,aAAa,CAAC,0CAA0C,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED,uDAAuD;IACvD,YAAY,IAAwB;QAClC,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;YACtB,MAAM,KAAK,CAAC,aAAa,CACvB,4CAA4C;gBAC1C,gDAAgD,CACnD,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3D,MAAM,iBAAiB,GAAG,IAAI,sCAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE;YACrE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,kBAAkB,GAAG,IAAI,wCAAkB,CAC9C,iBAAiB,CAAC,kBAAkB,EACpC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,MAAM,CAAC,CACzD,CAAC;IACJ,CAAC;IAED,OAAO,CAAC,IAAY,EAAE,OAAyB;QAC7C,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,IAAI,oCAAgB,CAAC,OAAO,CAAC,CAAC;QAChD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAA,6BAAqB,EAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,EAC/C,SAAS,EACT,OAAO,CACR,CAAC;IACJ,CAAC;IAED,aAAa,CAAC,MAAc,EAAE,OAAyB;QACrD,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,IAAI,oCAAgB,CAAC,OAAO,CAAC,CAAC;QAChD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAA,+BAAuB,EAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,EACnD,SAAS,EACT,OAAO,CACR,CAAC;IACJ,CAAC;IAED,OAAO;QACL,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;CACF;AA5KD,4BA4KC;AAED,SAAgB,YAAY;IAC1B,OAAO,IAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAChC,CAAC"}