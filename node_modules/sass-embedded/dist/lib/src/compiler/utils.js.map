{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../../lib/src/compiler/utils.ts"], "names": [], "mappings": ";AAAA,uEAAuE;AACvE,gEAAgE;AAChE,uCAAuC;;AAgDvC,4CAWC;AAyCD,sDASC;AAGD,0DAgCC;AAUD,wCAgDC;AAOD,sDAkBC;AAjOD,0BAA0B;AAC1B,gDAAgD;AAChD,iDAA0C;AAE1C,kDAA6E;AAC7E,sDAAsD;AACtD,8CAA6D;AAC7D,4CAAuC;AAEvC,2CAIyB;AACzB,sCAAiC;AAEjC,kCAAkC;AAClC,oDAAoD;AA0BpD;;GAEG;AACH,SAAgB,gBAAgB,CAC9B,aAAqB,EACrB,kBAAsC,EACtC,QAAkC;IAElC,OAAO,IAAI,uBAAU,CACnB,aAAa,EACb,kBAAkB,CAAC,iBAAiB,EACpC,OAAO,CAAC,EAAE,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAC1D,QAAQ,CACT,CAAC;AACJ,CAAC;AAED,2EAA2E;AAC3E,0BAA0B;AAC1B,SAAS,iBAAiB,CACxB,SAA6C,EAC7C,OAAmC;IAEnC,MAAM,OAAO,GAAG,IAAA,iBAAM,EAAC,KAAK,CAAC,mCAAmC,EAAE;QAChE,SAAS,EAAE,SAAS,CAAC,SAAS;QAC9B,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,IAAI,EAAE,CAAC;QACtD,SAAS,EAAE,CAAC,CAAC,OAAO,EAAE,SAAS;QAC/B,uBAAuB,EAAE,CAAC,CAAC,OAAO,EAAE,uBAAuB;QAC3D,UAAU,EAAE,OAAO,EAAE,UAAU,IAAI,CAAC,CAAC,aAAa,CAAC,MAAM;QACzD,UAAU,EAAE,CAAC,CAAC,OAAO,EAAE,UAAU;QACjC,SAAS,EAAE,CAAC,CAAC,OAAO,EAAE,SAAS;QAC/B,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,OAAO;QAC3B,OAAO,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,IAAI,IAAI,CAAC;QACrC,MAAM,EAAE,OAAO,EAAE,MAAM,KAAK,eAAM,CAAC,MAAM;QACzC,gBAAgB,EAAE,IAAA,gCAAiB,EAAC,OAAO,EAAE,iBAAiB,IAAI,EAAE,CAAC;QACrE,kBAAkB,EAAE,IAAA,gCAAiB,EAAC,OAAO,EAAE,mBAAmB,IAAI,EAAE,CAAC;QACzE,iBAAiB,EAAE,IAAA,gCAAiB,EAAC,OAAO,EAAE,kBAAkB,IAAI,EAAE,CAAC;KACxE,CAAC,CAAC;IAEH,QAAQ,OAAO,EAAE,KAAK,IAAI,UAAU,EAAE,CAAC;QACrC,KAAK,UAAU;YACb,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC;YAC3C,MAAM;QAER,KAAK,YAAY;YACf,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,UAAU,CAAC;YAC7C,MAAM;QAER;YACE,MAAM,IAAI,KAAK,CAAC,2BAA2B,OAAO,EAAE,KAAK,GAAG,CAAC,CAAC;IAClE,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,0CAA0C;AAC1C,SAAgB,qBAAqB,CACnC,IAAY,EACZ,SAA6C,EAC7C,OAAmC;IAEnC,MAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAChC,MAAM,OAAO,GAAG,iBAAiB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACtD,OAAO,CAAC,KAAK,GAAG,EAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAC,CAAC;IAC/C,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,4CAA4C;AAC5C,SAAgB,uBAAuB,CACrC,MAAc,EACd,SAA6C,EAC7C,OAAyC;IAEzC,MAAM,KAAK,GAAG,IAAA,iBAAM,EAAC,KAAK,CAAC,+CAA+C,EAAE;QAC1E,MAAM;QACN,MAAM,EAAE,KAAK,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,IAAI,MAAM,CAAC;KACvD,CAAC,CAAC;IAEH,MAAM,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;IACrC,IAAI,GAAG,IAAI,GAAG,KAAK,8BAAsB,EAAE,CAAC;QAC1C,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;IAClB,CAAC;IAED,IAAI,OAAO,IAAI,UAAU,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACzD,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACxD,CAAC;SAAM,IAAI,GAAG,KAAK,8BAAsB,EAAE,CAAC;QAC1C,KAAK,CAAC,QAAQ,GAAG,IAAA,iBAAM,EACrB,KAAK,CAAC,4CAA4C,EAClD;YACE,QAAQ,EAAE,EAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAC;SAChD,CACF,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,gEAAgE;QAChE,uEAAuE;IACzE,CAAC;IAED,MAAM,OAAO,GAAG,iBAAiB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACtD,OAAO,CAAC,KAAK,GAAG,EAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAC,CAAC;IAC/C,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,+DAA+D;AAC/D,SAAS,kBAAkB,CACzB,EAAwC;IAExC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,2BAAY,CAAC;AACpC,CAAC;AAED,kDAAkD;AAClD,SAAgB,cAAc,CAC5B,OAAwD,EACxD,KAAqC;IAErC,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,oCAAmB,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/D,IAAI,IAAI,IAAI,OAAO,EAAE,MAAM;QAAE,IAAI,GAAG,IAAA,oCAA4B,EAAC,IAAI,CAAC,CAAC;IACvE,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;IAC5B,IAAI,OAAO,EAAE,MAAM;QAAE,OAAO,GAAG,IAAA,4BAAoB,EAAC,OAAO,CAAC,CAAC;IAC7D,IAAI,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;IAChC,IAAI,OAAO,EAAE,MAAM;QAAE,SAAS,GAAG,IAAA,4BAAoB,EAAC,SAAS,CAAC,CAAC;IACjE,MAAM,eAAe,GAAG,kBAAkB,CAAC,KAAK,CAAC,eAAe,CAAC;QAC/D,CAAC,CAAC,2BAAY,CAAC,KAAK,CAAC,eAAe,CAAC;QACrC,CAAC,CAAC,IAAI,CAAC;IAET,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC5C,IAAI,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;YAC3B,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE;gBAC5B,IAAI,EAAE,IAAK;aACZ,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;SAAM,CAAC;QACN,IAAI,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;YAC1B,MAAM,MAAM,GASR,eAAe;gBACjB,CAAC,CAAC,EAAC,WAAW,EAAE,IAAI,EAAE,eAAe,EAAE,eAAe,EAAC;gBACvD,CAAC,CAAC,EAAC,WAAW,EAAE,KAAK,EAAC,CAAC;YACzB,IAAI,IAAI;gBAAE,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;YAE7B,MAAM,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC;YAC/B,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,CAAC,KAAK,GAAG,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,IAAA,4BAAoB,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACvE,CAAC;YAED,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACvC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,qBAAqB,CACnC,QAA+C;IAE/C,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QACvC,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC;QACtC,MAAM,MAAM,GAAkB;YAC5B,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;SACzD,CAAC;QAEF,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,IAAI,SAAS;YAAE,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACxD,OAAO,MAAM,CAAC;IAChB,CAAC;SAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAC9C,MAAM,IAAI,qBAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC7C,CAAC;SAAM,CAAC;QACN,MAAM,KAAK,CAAC,aAAa,CAAC,sCAAsC,CAAC,CAAC;IACpE,CAAC;AACH,CAAC"}