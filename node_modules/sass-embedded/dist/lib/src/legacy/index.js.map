{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../lib/src/legacy/index.ts"], "names": [], "mappings": ";AAAA,uEAAuE;AACvE,gEAAgE;AAChE,uCAAuC;;AA2CvC,wBA2BC;AAED,gCAkBC;AAxFD,yBAAyB;AACzB,0BAA0B;AAC1B,6BAAuC;AACvC,4DAAyD;AAEzD,4CAAuC;AACvC,wCAKoB;AACpB,kDAAyE;AACzE,oCAMkB;AAalB,uCAA0C;AAC1C,yCAAoE;AACpE,mCAKiB;AAEjB,SAAgB,MAAM,CACpB,OAA+B,EAC/B,QAAkE;IAElE,IAAI,CAAC;QACH,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;QAEjC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACzB,IAAA,yCAA0B,EACxB,yDAAyD;YACvD,sBAAsB;YACtB,kDAAkD,EACpD,2BAAY,CAAC,eAAe,CAAC,EAC7B,OAAO,CACR,CAAC;QACF,MAAM,WAAW,GAAG,eAAe,CAAC,OAAO,CAAC;YAC1C,CAAC,CAAC,IAAA,4BAAkB,EAAC,OAAO,CAAC,IAAI,EAAE,oBAAoB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACxE,CAAC,CAAC,IAAA,sBAAY,EAAC,OAAO,CAAC,IAAI,EAAE,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QAE/D,WAAW,CAAC,IAAI,CACd,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,EAAE,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,EACtE,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAC7C,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,KAAK;YAAE,QAAQ,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;QAChE,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,SAAgB,UAAU,CAAC,OAA8B;IACvD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACzB,IAAI,CAAC;QACH,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;QACjC,IAAA,yCAA0B,EACxB,yDAAyD;YACvD,sBAAsB;YACtB,kDAAkD,EACpD,2BAAY,CAAC,eAAe,CAAC,EAC7B,OAAO,CACR,CAAC;QACF,MAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC;YACrC,CAAC,CAAC,IAAA,uBAAa,EAAC,OAAO,CAAC,IAAI,EAAE,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAClE,CAAC,CAAC,IAAA,iBAAO,EAAC,OAAO,CAAC,IAAI,EAAE,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QACzD,OAAO,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAAC,OAAO,KAAc,EAAE,CAAC;QACxB,MAAM,kBAAkB,CAAC,KAAc,CAAC,CAAC;IAC3C,CAAC;AACH,CAAC;AAED,8EAA8E;AAC9E,WAAW;AACX,SAAS,aAAa,CACpB,OAA4B;IAE5B,IAAI,CAAC,CAAC,MAAM,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE,CAAC;QACjE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;IACtE,CAAC;IAED,0EAA0E;IAC1E,iBAAiB;IACjB,OAAO,CAAC,YAAY,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,CAAC;IAExE,IACE,CAAC,eAAe,CAAC,OAAO,CAAC;QACzB,8EAA8E;QAC9E,8EAA8E;QAC9E,0EAA0E;QAC1E,iBAAiB;QACjB,CAAE,OAAgD,CAAC,cAAc;YAC/D,SAAS;YACT,OAAO,CAAC,QAAQ,CAAC,EACnB,CAAC;QACD,OAAO;YACL,GAAG,OAAO;YACV,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;YAC3C,cAAc,EAAE,CAAC,CAAE,OAAgD;iBAChE,cAAc;SAClB,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,OAAO,OAAO,CAAC;IACjB,CAAC;AACH,CAAC;AAED,wDAAwD;AACxD,SAAS,eAAe,CACtB,OAA4B;IAE5B,OAAO,MAAM,IAAI,OAAO,CAAC;AAC3B,CAAC;AAED,mDAAmD;AACnD,SAAS,cAAc,CACrB,OAA4B,EAC5B,IAAuB;IAEvB,IACE,aAAa,IAAI,OAAO;QACxB,OAAO,CAAC,WAAW,KAAK,YAAY;QACpC,OAAO,CAAC,WAAW,KAAK,UAAU,EAClC,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,0BAA0B,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC;IACpE,CAAC;IAED,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC;IACjC,MAAM,SAAS,GAAyC,EAAE,CAAC;IAC3D,KAAK,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC,EAAE,CAAC;QAC1E,0EAA0E;QAC1E,YAAY;QACZ,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC;YAAE,SAAS,IAAI,IAAI,CAAC;QAEhD,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAA,mBAAY,EAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;IACvE,CAAC;IAED,MAAM,SAAS,GACb,OAAO,CAAC,QAAQ;QAChB,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,YAAY,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACnE,CAAC,CAAC;YACE,IAAI,gCAAqB,CACvB,IAAI,EACJ,OAAO,CAAC,QAAQ,YAAY,KAAK;gBAC/B,CAAC,CAAC,OAAO,CAAC,QAAQ;gBAClB,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EACtB,OAAO,CAAC,YAAY,IAAI,EAAE,EAC1B,OAAO,CAAC,IAAI,IAAI,OAAO,EACvB,IAAI,CACL;SACF;QACH,CAAC,CAAC,SAAS,CAAC;IAEhB,OAAO;QACL,SAAS;QACT,SAAS,EACP,OAAO,CAAC,WAAW,YAAY,uCAAmB;YAChD,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;YAC7C,CAAC,CAAC,SAAS;QACf,SAAS,EAAE,qBAAqB,CAAC,OAAO,CAAC;QACzC,uBAAuB,EAAE,OAAO,CAAC,iBAAiB;QAClD,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY;QACvD,KAAK,EAAE,OAAO,CAAC,WAAoD;QACnE,SAAS,EAAE,OAAO,CAAC,SAAS;QAC5B,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;QAC5C,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;QAC9C,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;QAChD,MAAM,EAAE,IAAI;KACb,CAAC;AACJ,CAAC;AAED,+DAA+D;AAC/D,SAAS,oBAAoB,CAC3B,OAAkC,EAClC,IAAuB;IAEvB,MAAM,aAAa,GAAG,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAEpD,4EAA4E;IAC5E,0EAA0E;IAC1E,MAAM,QAAQ,GAAG,aAAa,CAAC,SAAS,EAAE,IAAI,CAC5C,QAAQ,CAAC,EAAE,CAAC,QAAQ,YAAY,gCAAqB,CACtD;QACC,CAAC,CAAC;YACE,YAAY;gBACV,OAAO,IAAI,CAAC;YACd,CAAC;YACD,IAAI;gBACF,OAAO,IAAI,CAAC;YACd,CAAC;SACF;QACH,CAAC,CAAC,SAAS,CAAC;IAEd,OAAO;QACL,GAAG,aAAa;QAChB,GAAG,EAAE,OAAO,CAAC,IAAI;YACf,CAAC,CAAC,OAAO,CAAC,QAAQ;gBAChB,CAAC,CAAC,IAAA,2BAAmB,EAAC,OAAO,CAAC,IAAI,CAAC;gBACnC,CAAC,CAAC,IAAA,mBAAa,EAAC,OAAO,CAAC,IAAI,CAAC;YAC/B,CAAC,CAAC,IAAI,SAAG,CAAC,8BAAsB,CAAC;QACnC,QAAQ;QACR,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM;KACrD,CAAC;AACJ,CAAC;AAED,0EAA0E;AAC1E,SAAS,qBAAqB,CAC5B,OAA8C;IAE9C,OAAO,CACL,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ;QACrC,CAAC,OAAO,CAAC,SAAS,KAAK,IAAI,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAClD,CAAC;AACJ,CAAC;AAED,sDAAsD;AACtD,SAAS,UAAU,CACjB,OAAwC;IAExC,MAAM,UAAU,GAAqB;QACnC,OAAO,EAAE;YACP,OAAO,EAAE,SAAwC;YACjD,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,YAAY,EAAE,CAAC,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;YAC5D,SAAS,EAAE,EAAE;YACb,KAAK,EAAE,CAAC;YACR,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,CAAC;YACd,QAAQ,EAAE,IAAI;YACd,MAAM,EAAE;gBACN,KAAK,EAAE;oBACL,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE;oBACjB,KAAK,EAAE,OAAO,CAAC,IAAI,IAAI,MAAM;iBAC9B;aACF;SACF;KACF,CAAC;IACF,UAAU,CAAC,OAAO,CAAC,OAAO,GAAG,UAAU,CAAC;IACxC,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,6EAA6E;AAC7E,cAAc;AACd,SAAS,eAAe,CACtB,OAAwC,EACxC,KAAa,EACb,MAAqB;IAErB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAEvB,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;IACrB,IAAI,cAAkC,CAAC;IACvC,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;QACrB,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QACnC,SAAS,CAAC,UAAU,GAAG,OAAO,CAAC,aAAa,IAAI,EAAE,CAAC;QAEnD,MAAM,aAAa,GACjB,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ;YACnC,CAAC,CAAE,OAAO,CAAC,SAAoB;YAC/B,CAAC,CAAC,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC;QAC/B,MAAM,YAAY,GAAG,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAE9C,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,SAAS,CAAC,IAAI,GAAG,IAAA,uBAAe,EAC9B,CAAC,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,OAAO,CAAC,CAC1C,CAAC;QACJ,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACxB,SAAS,CAAC,IAAI,GAAG,IAAA,uBAAe,EAAC,IAAA,wBAAgB,EAAC,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;QAC5E,CAAC;aAAM,CAAC;YACN,SAAS,CAAC,IAAI,GAAG,WAAW,CAAC;QAC/B,CAAC;QAED,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO;aAClC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,4BAAiB,CAAC,CAAC;aACvD,GAAG,CAAC,MAAM,CAAC,EAAE;YACZ,MAAM,GAAG,IAAA,4BAAoB,EAAC,MAAM,CAAC,CAAC;YACtC,IAAI,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBACjC,OAAO,IAAA,uBAAe,EACpB,CAAC,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAA,kCAA0B,EAAC,MAAM,CAAC,CAAC,CAC7D,CAAC;YACJ,CAAC;iBAAM,IAAI,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;gBACtC,OAAO,OAAO,CAAC;YACjB,CAAC;iBAAM,CAAC;gBACN,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;QAEL,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;QAExD,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC9B,IAAI,GAAG,CAAC;YACR,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;gBAC3B,GAAG,GAAG,gCAAgC,cAAc,CAAC,QAAQ,CAC3D,QAAQ,CACT,EAAE,CAAC;YACN,CAAC;iBAAM,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBAC3B,GAAG,GAAG,IAAA,uBAAe,EACnB,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,aAAa,CAAC,CACtD,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,GAAG,GAAG,IAAA,uBAAe,EAAC,aAAa,CAAC,CAAC;YACvC,CAAC;YACD,GAAG,IAAI,4BAA4B,GAAG,KAAK,CAAC;QAC9C,CAAC;IACH,CAAC;IAED,OAAO;QACL,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;QACrB,GAAG,EAAE,cAAc;QACnB,KAAK,EAAE;YACL,KAAK,EAAE,OAAO,CAAC,IAAI,IAAI,MAAM;YAC7B,KAAK;YACL,GAAG;YACH,QAAQ,EAAE,GAAG,GAAG,KAAK;YACrB,aAAa,EAAE,MAAM,CAAC,UAAU;iBAC7B,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,KAAK,4BAAiB,CAAC;iBACjD,GAAG,CAAC,GAAG,CAAC,EAAE;gBACT,IAAI,GAAG,CAAC,QAAQ,KAAK,8BAAsB,EAAE,CAAC;oBAC5C,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACjC,CAAC;gBAED,MAAM,SAAS,GAAG,IAAA,4BAAoB,EAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACvD,OAAO,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC;oBAClC,CAAC,CAAC,IAAA,kCAA0B,EAAC,SAAS,CAAC;oBACvC,CAAC,CAAC,SAAS,CAAC;YAChB,CAAC,CAAC;SACL;KACF,CAAC;AACJ,CAAC;AAED,gFAAgF;AAChF,SAAS;AACT,SAAS,kBAAkB,CAAC,KAAY;IACtC,IAAI,CAAC,CAAC,KAAK,YAAY,qBAAS,CAAC,EAAE,CAAC;QAClC,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE;YAC1B,SAAS,EAAE,KAAK,CAAC,QAAQ,EAAE;YAC3B,MAAM,EAAE,CAAC;SACV,CAAC,CAAC;IACL,CAAC;IAED,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,oCAA4B,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1E,IAAI,IAAY,CAAC;IACjB,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;QACf,IAAI,GAAG,OAAO,CAAC;IACjB,CAAC;SAAM,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACzC,4EAA4E;QAC5E,yEAAyE;QACzE,uEAAuE;QACvE,sCAAsC;QACtC,IAAI,GAAG,IAAA,kCAA0B,EAAC,IAAI,CAAC,GAAU,CAAC,CAAC;IACrD,CAAC;SAAM,CAAC;QACN,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;IAC7B,CAAC;IAED,MAAM,WAAW,GAAG,IAAA,4BAAoB,EAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC3D,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,EAAE,EAAE;QAChC,MAAM,EAAE,CAAC;QACT,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;QAC5C,SAAS,EAAE,WAAW;QACtB,QAAQ,EAAE,GAAG,EAAE,CAAC,WAAW;QAC3B,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAA,4BAAoB,EAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;QAClE,IAAI,EAAE,IAAA,yBAAiB,EAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC;YAC7C,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,KAAK,CAAC,IAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC;QAC9B,MAAM,EAAE,IAAA,yBAAiB,EAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC;YACjD,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,KAAK,CAAC,IAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;QAChC,IAAI;KACL,CAAC,CAAC;AACL,CAAC"}