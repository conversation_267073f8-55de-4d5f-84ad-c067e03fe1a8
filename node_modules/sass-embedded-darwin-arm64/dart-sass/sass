#!/bin/sh

# This script drives the standalone dart-sass package, which bundles together a
# Dart executable and a snapshot of dart-sass.

follow_links() {
  # Use `readlink -f` if it exists, but fall back to manually following symlnks
  # for systems (like older Mac OS) where it doesn't.
  file="$1"
  if readlink -f "$file" 2>&-; then return; fi

  while [ -h "$file" ]; do
    file="$(readlink "$file")"
  done
  echo "$file"
}

# Unlike $0, $BASH_SOURCE points to the absolute path of this file.
path=`dirname "$(follow_links "$0")"`
exec "$path/src/dart" "$path/src/sass.snapshot" "$@"
