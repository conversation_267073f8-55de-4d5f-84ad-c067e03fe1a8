{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"autoprefixer": "^10.4.20", "axios": "^1.10.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.47", "sass-embedded": "^1.89.2", "tailwindcss": "^3.4.13", "vite": "^6.0.11"}, "dependencies": {"@mdi/font": "^7.4.47", "@vitejs/plugin-vue": "^5.2.4", "pinia": "^2.3.1", "vue": "^3.5.17", "vue-router": "^4.5.1", "vuetify": "^3.8.12"}}