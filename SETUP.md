# POS & Pharmacy Management System Setup Guide

## Overview
This is a complete Laravel 11 + Vue 3 + Vuetify 3 application for POS and pharmacy stock management.

## Technology Stack
- **Backend**: Laravel 11 with Vite
- **Frontend**: Vue 3 with Vuetify 3
- **Database**: MySQL
- **Authentication**: Laravel Sanctum
- **State Management**: Pinia
- **Routing**: Vue Router 4
- **Icons**: Material Design Icons

## Prerequisites
- PHP 8.2 or higher
- Composer
- Node.js 18+ and npm
- MySQL 8.0+

## Installation & Setup

### 1. Database Setup
Create a MySQL database named `medic_app`:
```sql
CREATE DATABASE medic_app;
```

### 2. Environment Configuration
The `.env` file is already configured with:
- MySQL database connection
- Laravel Sanctum settings
- Application-specific settings for pharmacy and POS

Update the database credentials in `.env` if needed:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=medic_app
DB_USERNAME=root
DB_PASSWORD=your_password
```

### 3. Install Dependencies
```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### 4. Run Migrations
```bash
php artisan migrate
```

### 5. Build Assets
```bash
# For development
npm run dev

# For production
npm run build
```

### 6. Start the Application
```bash
# Start Laravel development server
php artisan serve

# In another terminal, start Vite dev server (for development)
npm run dev
```

## Application Structure

### Frontend (Vue 3 + Vuetify 3)
- **Main App**: `resources/js/App.vue`
- **Pages**: `resources/js/pages/`
  - Dashboard.vue
  - POS.vue
  - Pharmacy.vue
  - Inventory.vue
  - Reports.vue
  - Settings.vue
- **Components**: `resources/js/components/`
- **Stores**: `resources/js/stores/` (Pinia)
- **Routes**: `resources/js/routes.js`

### Backend (Laravel 11)
- **API Routes**: `routes/api.php`
- **Web Routes**: `routes/web.php`
- **Controllers**: `app/Http/Controllers/`
- **Models**: `app/Models/`
- **Middleware**: `app/Http/Middleware/`

## Features Implemented

### 1. Authentication System
- Laravel Sanctum for API authentication
- Login/Register/Logout functionality
- Token-based authentication for SPA

### 2. Dashboard
- Sales statistics
- Product count
- Low stock alerts
- Recent transactions
- Quick action buttons

### 3. Point of Sale (POS)
- Product search and selection
- Shopping cart functionality
- Payment processing interface

### 4. Pharmacy Management
- Prescription management
- Status tracking (Pending, Ready, Dispensed)
- Patient and doctor information

### 5. Inventory Management
- Product listing with stock levels
- Low stock indicators
- Expiry date tracking
- Add/Edit/Delete products

### 6. Reports
- Sales reports
- Inventory reports
- Chart placeholders for future implementation

### 7. Settings
- Store configuration
- User management
- System settings

## API Endpoints

### Authentication
- `POST /api/login` - User login
- `POST /api/register` - User registration
- `POST /api/logout` - User logout

### Dashboard
- `GET /api/dashboard/stats` - Dashboard statistics

### Products & POS
- `GET /api/products` - List products
- `POST /api/products` - Create product
- `POST /api/pos/process-sale` - Process sale

### Pharmacy
- `GET /api/prescriptions` - List prescriptions
- `POST /api/prescriptions` - Create prescription

### Inventory
- `GET /api/inventory` - List inventory
- `GET /api/inventory/low-stock` - Low stock items

### Reports
- `GET /api/reports/sales` - Sales reports
- `GET /api/reports/inventory` - Inventory reports

## Configuration Files

### Package.json Dependencies
```json
{
  "devDependencies": {
    "autoprefixer": "^10.4.20",
    "axios": "^1.7.4",
    "concurrently": "^9.0.1",
    "laravel-vite-plugin": "^1.2.0",
    "postcss": "^8.4.47",
    "tailwindcss": "^3.4.13",
    "vite": "^6.0.11",
    "@vitejs/plugin-vue": "latest"
  },
  "dependencies": {
    "vue": "^3.4.0",
    "vuetify": "^3.7.0",
    "@mdi/font": "latest",
    "vue-router": "^4.4.0",
    "pinia": "^2.2.0"
  }
}
```

### Composer.json Requirements
- Laravel Framework 11.x
- Laravel Sanctum 4.x
- Standard Laravel dependencies

## Security Features
- CSRF protection
- API rate limiting
- CORS configuration
- Sanctum token authentication
- Custom API protection middleware

## Development Commands
```bash
# Start development servers
php artisan serve          # Laravel backend (http://localhost:8000)
npm run dev                # Vite frontend dev server

# Build for production
npm run build

# Run tests
php artisan test

# Clear caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
```

## Next Steps
1. Implement actual database models and migrations
2. Add real authentication logic
3. Implement CRUD operations for all entities
4. Add form validation
5. Implement file upload functionality
6. Add chart libraries for reports
7. Implement real payment processing
8. Add unit and feature tests
9. Set up production deployment

## Support
For issues or questions, refer to the Laravel and Vue.js documentation.
