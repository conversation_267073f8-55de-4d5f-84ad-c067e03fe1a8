[2025-07-04 13:19:25] local.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `sessions` where `id` = Y5sgzYjDm1rdAmBELgihjelnLnBtl524ZdLTUXPY limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `sessions` where `id` = Y5sgzYjDm1rdAmBELgihjelnLnBtl524ZdLTUXPY limit 1) at /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', <PERSON><PERSON><PERSON>, Object(Closure))
#1 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#2 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#3 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#10 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('Y5sgzYjDm1rdAmB...')
#11 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('Y5sgzYjDm1rdAmB...')
#12 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#13 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(89): Illuminate\\Session\\Store->loadSession()
#14 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(147): Illuminate\\Session\\Store->start()
#15 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Dipro/Work/personal/medic_app/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Di...')
#53 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#3 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func(Object(Closure))
#7 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getPdo()
#8 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(false)
#9 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#12 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#13 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#14 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#15 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#16 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#17 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#18 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#19 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#20 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('Y5sgzYjDm1rdAmB...')
#21 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('Y5sgzYjDm1rdAmB...')
#22 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#23 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(89): Illuminate\\Session\\Store->loadSession()
#24 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(147): Illuminate\\Session\\Store->start()
#25 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#26 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#27 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#28 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 /Users/<USER>/Dipro/Work/personal/medic_app/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#62 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Di...')
#63 {main}
"} 
[2025-07-04 13:20:16] local.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `sessions` where `id` = zrTNYfCnFP7mRKusHlGxr8V0xyI3f3P0iXpX6rQq limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `sessions` where `id` = zrTNYfCnFP7mRKusHlGxr8V0xyI3f3P0iXpX6rQq limit 1) at /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#2 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#3 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#10 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('zrTNYfCnFP7mRKu...')
#11 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('zrTNYfCnFP7mRKu...')
#12 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#13 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(89): Illuminate\\Session\\Store->loadSession()
#14 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(147): Illuminate\\Session\\Store->start()
#15 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Dipro/Work/personal/medic_app/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Di...')
#53 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#3 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func(Object(Closure))
#7 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getPdo()
#8 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(false)
#9 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#12 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#13 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#14 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#15 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#16 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#17 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#18 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#19 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#20 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('zrTNYfCnFP7mRKu...')
#21 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('zrTNYfCnFP7mRKu...')
#22 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#23 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(89): Illuminate\\Session\\Store->loadSession()
#24 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(147): Illuminate\\Session\\Store->start()
#25 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#26 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#27 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#28 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 /Users/<USER>/Dipro/Work/personal/medic_app/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#62 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Di...')
#63 {main}
"} 
[2025-07-04 13:20:17] local.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `sessions` where `id` = wKJaY1uMJsm2Ey2zxwuk2J2K82bq5JIFS3Izj1Ur limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `sessions` where `id` = wKJaY1uMJsm2Ey2zxwuk2J2K82bq5JIFS3Izj1Ur limit 1) at /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#2 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#3 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#10 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('wKJaY1uMJsm2Ey2...')
#11 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('wKJaY1uMJsm2Ey2...')
#12 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#13 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(89): Illuminate\\Session\\Store->loadSession()
#14 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(147): Illuminate\\Session\\Store->start()
#15 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Dipro/Work/personal/medic_app/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Di...')
#53 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#3 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func(Object(Closure))
#7 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getPdo()
#8 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(false)
#9 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#12 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#13 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#14 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#15 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#16 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#17 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#18 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#19 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#20 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('wKJaY1uMJsm2Ey2...')
#21 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('wKJaY1uMJsm2Ey2...')
#22 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#23 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(89): Illuminate\\Session\\Store->loadSession()
#24 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(147): Illuminate\\Session\\Store->start()
#25 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#26 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#27 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#28 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 /Users/<USER>/Dipro/Work/personal/medic_app/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#62 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Di...')
#63 {main}
"} 
[2025-07-04 13:20:18] local.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `sessions` where `id` = iNnT3DhbT7w8u5Ovqxb6NtnuHRqPdLXcIkQCIiZ0 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `sessions` where `id` = iNnT3DhbT7w8u5Ovqxb6NtnuHRqPdLXcIkQCIiZ0 limit 1) at /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#2 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#3 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#10 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('iNnT3DhbT7w8u5O...')
#11 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('iNnT3DhbT7w8u5O...')
#12 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#13 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(89): Illuminate\\Session\\Store->loadSession()
#14 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(147): Illuminate\\Session\\Store->start()
#15 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Dipro/Work/personal/medic_app/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Di...')
#53 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#3 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func(Object(Closure))
#7 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getPdo()
#8 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(false)
#9 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#12 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#13 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#14 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#15 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#16 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#17 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#18 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#19 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#20 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('iNnT3DhbT7w8u5O...')
#21 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('iNnT3DhbT7w8u5O...')
#22 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#23 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(89): Illuminate\\Session\\Store->loadSession()
#24 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(147): Illuminate\\Session\\Store->start()
#25 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#26 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#27 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#28 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 /Users/<USER>/Dipro/Work/personal/medic_app/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#62 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Di...')
#63 {main}
"} 
[2025-07-04 13:20:19] local.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `sessions` where `id` = 9ixDQydziAN5oTRX9Upk8arQiZ0pj7z5BUgCypQI limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `sessions` where `id` = 9ixDQydziAN5oTRX9Upk8arQiZ0pj7z5BUgCypQI limit 1) at /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#2 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#3 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#10 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('9ixDQydziAN5oTR...')
#11 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('9ixDQydziAN5oTR...')
#12 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#13 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(89): Illuminate\\Session\\Store->loadSession()
#14 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(147): Illuminate\\Session\\Store->start()
#15 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Dipro/Work/personal/medic_app/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Di...')
#53 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#3 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func(Object(Closure))
#7 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getPdo()
#8 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(false)
#9 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#12 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#13 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#14 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#15 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#16 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#17 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#18 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(343): Illuminate\\Database\\Query\\Builder->get(Array)
#19 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3013): Illuminate\\Database\\Query\\Builder->first(Array)
#20 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php(97): Illuminate\\Database\\Query\\Builder->find('9ixDQydziAN5oTR...')
#21 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(117): Illuminate\\Session\\DatabaseSessionHandler->read('9ixDQydziAN5oTR...')
#22 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(105): Illuminate\\Session\\Store->readFromHandler()
#23 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Store.php(89): Illuminate\\Session\\Store->loadSession()
#24 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(147): Illuminate\\Session\\Store->start()
#25 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#26 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(144): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#27 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(116): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#28 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 /Users/<USER>/Dipro/Work/personal/medic_app/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#62 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Di...')
#63 {main}
"} 
[2025-07-04 20:53:18] local.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `cache` where `key` in (<EMAIL>|127.0.0.1)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `cache` where `key` in (<EMAIL>|127.0.0.1)) at /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php:825)
[stacktrace]
#0 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#2 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#3 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php(130): Illuminate\\Database\\Query\\Builder->get()
#9 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php(105): Illuminate\\Cache\\DatabaseStore->many(Array)
#10 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(117): Illuminate\\Cache\\DatabaseStore->get('admin@medipos.c...')
#11 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php(207): Illuminate\\Cache\\Repository->get('admin@medipos.c...', 0)
#12 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php(301): Illuminate\\Cache\\RateLimiter->Illuminate\\Cache\\{closure}()
#13 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php(207): Illuminate\\Cache\\RateLimiter->withoutSerializationOrCompression(Object(Closure))
#14 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php(130): Illuminate\\Cache\\RateLimiter->attempts('admin@medipos.c...')
#15 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Cache\\RateLimiter->tooManyAttempts('admin@medipos.c...', 5)
#16 /Users/<USER>/Dipro/Work/personal/medic_app/app/Http/Controllers/Auth/LoginController.php(33): Illuminate\\Support\\Facades\\Facade::__callStatic('tooManyAttempts', Array)
#17 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(47): App\\Http\\Controllers\\Auth\\LoginController->login(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\LoginController), 'login')
#19 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Route.php(212): Illuminate\\Routing\\Route->runController()
#20 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#21 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Users/<USER>/Dipro/Work/personal/medic_app/app/Http/Middleware/ApiProtection.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): App\\Http\\Middleware\\ApiProtection->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Dipro/Work/personal/medic_app/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Di...')
#55 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#3 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1231): call_user_func(Object(Closure))
#7 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#8 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#9 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#11 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#12 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#13 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#14 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#15 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#16 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#17 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#19 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php(130): Illuminate\\Database\\Query\\Builder->get()
#20 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php(105): Illuminate\\Cache\\DatabaseStore->many(Array)
#21 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cache/Repository.php(117): Illuminate\\Cache\\DatabaseStore->get('admin@medipos.c...')
#22 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php(207): Illuminate\\Cache\\Repository->get('admin@medipos.c...', 0)
#23 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php(301): Illuminate\\Cache\\RateLimiter->Illuminate\\Cache\\{closure}()
#24 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php(207): Illuminate\\Cache\\RateLimiter->withoutSerializationOrCompression(Object(Closure))
#25 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Cache/RateLimiter.php(130): Illuminate\\Cache\\RateLimiter->attempts('admin@medipos.c...')
#26 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Cache\\RateLimiter->tooManyAttempts('admin@medipos.c...', 5)
#27 /Users/<USER>/Dipro/Work/personal/medic_app/app/Http/Controllers/Auth/LoginController.php(33): Illuminate\\Support\\Facades\\Facade::__callStatic('tooManyAttempts', Array)
#28 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(47): App\\Http\\Controllers\\Auth\\LoginController->login(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\LoginController), 'login')
#30 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Route.php(212): Illuminate\\Routing\\Route->runController()
#31 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#32 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Users/<USER>/Dipro/Work/personal/medic_app/app/Http/Middleware/ApiProtection.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): App\\Http\\Middleware\\ApiProtection->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 /Users/<USER>/Dipro/Work/personal/medic_app/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#65 /Users/<USER>/Dipro/Work/personal/medic_app/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Users/<USER>/Di...')
#66 {main}
"} 
