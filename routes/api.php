<?php

use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\PasswordResetController;
use App\Http\Controllers\Auth\ProfileController;
use App\Http\Controllers\Api\V1\ProductController;
use App\Http\Controllers\Api\V1\CategoryController;
use App\Http\Controllers\Api\V1\SupplierController;
use App\Http\Controllers\Api\V1\CustomerController;
use App\Http\Controllers\Api\V1\SaleController;
use App\Http\Controllers\Api\V1\PurchaseController;
use App\Http\Controllers\Api\V1\InventoryController;
use App\Http\Controllers\Api\V1\ReportController;
use App\Http\Controllers\Api\V1\UserController;
use App\Http\Controllers\Api\V1\LocationController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes - Version 1
|--------------------------------------------------------------------------
*/

// API Version 1 Routes
Route::prefix('v1')->group(function () {

    // Public Authentication Routes
    Route::prefix('auth')->group(function () {
        Route::post('/login', [LoginController::class, 'login']);
        Route::post('/register', [RegisterController::class, 'register']);
        Route::post('/bulk-register', [RegisterController::class, 'bulkRegister']);
        Route::post('/forgot-password', [PasswordResetController::class, 'sendResetLink']);
        Route::post('/reset-password', [PasswordResetController::class, 'resetPassword']);

        // Protected Auth Routes
        Route::middleware(['auth:sanctum', 'account.status'])->group(function () {
            Route::post('/logout', [LoginController::class, 'logout']);
            Route::post('/logout-all', [LoginController::class, 'logoutAll']);
            Route::get('/me', [LoginController::class, 'me']);
            Route::post('/change-password', [PasswordResetController::class, 'changePassword']);

            // Profile routes
            Route::get('/profile', [ProfileController::class, 'show']);
            Route::put('/profile', [ProfileController::class, 'update']);
            Route::post('/profile/avatar', [ProfileController::class, 'uploadAvatar']);
            Route::delete('/profile/avatar', [ProfileController::class, 'removeAvatar']);
            Route::get('/profile/activity-logs', [ProfileController::class, 'activityLogs']);
            Route::get('/profile/sessions', [ProfileController::class, 'sessions']);
            Route::delete('/profile/sessions', [ProfileController::class, 'revokeSession']);
        });
    });

    // Protected API Routes
    Route::middleware(['auth:sanctum', 'account.status'])->group(function () {

        // User Management Routes
        Route::middleware(['role:admin,pharmacist', 'activity.log'])->group(function () {
            Route::apiResource('users', UserController::class);
            Route::post('/users/{user}/activate', [UserController::class, 'activate']);
            Route::post('/users/{user}/deactivate', [UserController::class, 'deactivate']);
            Route::post('/users/{user}/unlock', [UserController::class, 'unlock']);
            Route::get('/users/{user}/activity-logs', [UserController::class, 'activityLogs']);
        });

        // Admin-only user management routes
        Route::middleware(['role:admin', 'activity.log'])->group(function () {
            Route::post('/users/{user}/force-password-reset', [PasswordResetController::class, 'forcePasswordReset']);
            Route::post('/users/{user}/generate-temp-password', [PasswordResetController::class, 'generateTempPassword']);
        });

        // Location Management Routes (Admin only)
        Route::middleware(['role:admin', 'activity.log'])->group(function () {
            Route::apiResource('locations', LocationController::class);
            Route::put('/locations/{location}/toggle-status', [LocationController::class, 'toggleStatus']);
        });

        // Category Management Routes (Admin, Pharmacist)
        Route::middleware(['role:admin,pharmacist', 'store.access', 'activity.log'])->group(function () {
            Route::apiResource('categories', CategoryController::class);
            Route::get('/categories/{category}/products', [CategoryController::class, 'products']);
        });

        // Supplier Management Routes (Admin, Pharmacist)
        Route::middleware(['role:admin,pharmacist', 'store.access', 'activity.log'])->group(function () {
            Route::apiResource('suppliers', SupplierController::class);
            Route::get('/suppliers/{supplier}/products', [SupplierController::class, 'products']);
        });

        // Product Management Routes
        Route::middleware(['role:admin,pharmacist', 'store.access', 'activity.log'])->group(function () {
            Route::apiResource('products', ProductController::class);
            Route::get('/products/search/barcode', [ProductController::class, 'searchByBarcode']);
            Route::get('/products/low-stock', [ProductController::class, 'lowStock']);
        });

        // Customer Management Routes
        Route::middleware(['role:admin,pharmacist,cashier', 'store.access', 'activity.log'])->group(function () {
            Route::apiResource('customers', CustomerController::class);
            Route::get('/customers/{customer}/sales', [CustomerController::class, 'sales']);
        });

        // Inventory Management Routes
        Route::middleware(['role:admin,pharmacist', 'store.access', 'activity.log'])->group(function () {
            Route::apiResource('inventory', InventoryController::class)->except(['store', 'destroy']);
            Route::post('/inventory/adjust', [InventoryController::class, 'adjustStock']);
            Route::get('/inventory/low-stock', [InventoryController::class, 'lowStock']);
            Route::get('/inventory/expiring', [InventoryController::class, 'expiring']);
        });

        // Sales Management Routes
        Route::middleware(['role:admin,pharmacist,cashier', 'store.access', 'activity.log'])->group(function () {
            Route::apiResource('sales', SaleController::class);
            Route::post('/sales/{sale}/refund', [SaleController::class, 'refund']);
            Route::get('/sales/{sale}/receipt', [SaleController::class, 'receipt']);
        });

        // Purchase Order Management Routes (Admin, Pharmacist)
        Route::middleware(['role:admin,pharmacist', 'store.access', 'activity.log'])->group(function () {
            Route::apiResource('purchases', PurchaseController::class);
            Route::put('/purchases/{purchase}/receive', [PurchaseController::class, 'receive']);
            Route::put('/purchases/{purchase}/cancel', [PurchaseController::class, 'cancel']);
        });

        // Reports Routes
        Route::prefix('reports')->group(function () {
            Route::get('/dashboard', [ReportController::class, 'dashboard']);
            Route::get('/sales', [ReportController::class, 'sales']);
            Route::get('/inventory', [ReportController::class, 'inventory']);
            Route::get('/financial', [ReportController::class, 'financial']);
            Route::get('/products/top-selling', [ReportController::class, 'topSellingProducts']);
            Route::get('/customers/top', [ReportController::class, 'topCustomers']);

            // Admin only reports
            Route::middleware('role:admin')->group(function () {
                Route::get('/users/performance', [ReportController::class, 'userPerformance']);
                Route::get('/locations/performance', [ReportController::class, 'locationPerformance']);
            });
        });

        // Quick Access Routes for POS
        Route::prefix('pos')->group(function () {
            Route::get('/products/search', [ProductController::class, 'index']);
            Route::get('/products/barcode/{barcode}', [ProductController::class, 'searchByBarcode']);
            Route::post('/sales/process', [SaleController::class, 'store']);
            Route::get('/customers/search', [CustomerController::class, 'index']);
        });
    });
});
