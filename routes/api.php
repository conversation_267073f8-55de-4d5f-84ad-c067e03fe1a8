<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Authentication routes
Route::post('/login', [App\Http\Controllers\AuthController::class, 'login']);
Route::post('/register', [App\Http\Controllers\AuthController::class, 'register']);
Route::post('/logout', [App\Http\Controllers\AuthController::class, 'logout'])->middleware('auth:sanctum');

// Protected API routes
Route::middleware('auth:sanctum')->group(function () {
    // Dashboard routes
    Route::get('/dashboard/stats', [App\Http\Controllers\DashboardController::class, 'stats']);
    
    // POS routes
    Route::apiResource('products', App\Http\Controllers\ProductController::class);
    Route::post('/pos/process-sale', [App\Http\Controllers\POSController::class, 'processSale']);
    
    // Pharmacy routes
    Route::apiResource('prescriptions', App\Http\Controllers\PrescriptionController::class);
    
    // Inventory routes
    Route::get('/inventory/low-stock', [App\Http\Controllers\InventoryController::class, 'lowStock']);
    Route::apiResource('inventory', App\Http\Controllers\InventoryController::class);
    
    // Reports routes
    Route::get('/reports/sales', [App\Http\Controllers\ReportController::class, 'sales']);
    Route::get('/reports/inventory', [App\Http\Controllers\ReportController::class, 'inventory']);
});
