<?php

use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Api\V1\ProductController;
use App\Http\Controllers\Api\V1\CategoryController;
use App\Http\Controllers\Api\V1\SupplierController;
use App\Http\Controllers\Api\V1\CustomerController;
use App\Http\Controllers\Api\V1\SaleController;
use App\Http\Controllers\Api\V1\PurchaseController;
use App\Http\Controllers\Api\V1\InventoryController;
use App\Http\Controllers\Api\V1\ReportController;
use App\Http\Controllers\Api\V1\UserController;
use App\Http\Controllers\Api\V1\LocationController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes - Version 1
|--------------------------------------------------------------------------
*/

// API Version 1 Routes
Route::prefix('v1')->group(function () {

    // Public Authentication Routes
    Route::prefix('auth')->group(function () {
        Route::post('/login', [AuthController::class, 'login']);
        Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
        Route::post('/reset-password', [AuthController::class, 'resetPassword']);

        // Protected Auth Routes
        Route::middleware('auth:sanctum')->group(function () {
            Route::post('/logout', [AuthController::class, 'logout']);
            Route::get('/profile', [AuthController::class, 'profile']);
            Route::put('/profile', [AuthController::class, 'updateProfile']);
            Route::post('/refresh-token', [AuthController::class, 'refreshToken']);
        });
    });

    // Protected API Routes
    Route::middleware('auth:sanctum')->group(function () {

        // User Management Routes (Admin only)
        Route::middleware('role:admin')->group(function () {
            Route::apiResource('users', UserController::class);
            Route::put('/users/{user}/toggle-status', [UserController::class, 'toggleStatus']);
        });

        // Location Management Routes (Admin only)
        Route::middleware('role:admin')->group(function () {
            Route::apiResource('locations', LocationController::class);
            Route::put('/locations/{location}/toggle-status', [LocationController::class, 'toggleStatus']);
        });

        // Category Management Routes (Admin, Pharmacist)
        Route::middleware('role:admin,pharmacist')->group(function () {
            Route::apiResource('categories', CategoryController::class);
            Route::get('/categories/{category}/products', [CategoryController::class, 'products']);
        });

        // Supplier Management Routes (Admin, Pharmacist)
        Route::middleware('role:admin,pharmacist')->group(function () {
            Route::apiResource('suppliers', SupplierController::class);
            Route::get('/suppliers/{supplier}/products', [SupplierController::class, 'products']);
        });

        // Product Management Routes
        Route::middleware('role:admin,pharmacist')->group(function () {
            Route::apiResource('products', ProductController::class);
            Route::get('/products/search/barcode', [ProductController::class, 'searchByBarcode']);
            Route::get('/products/low-stock', [ProductController::class, 'lowStock']);
        });

        // Customer Management Routes
        Route::apiResource('customers', CustomerController::class);
        Route::get('/customers/{customer}/sales', [CustomerController::class, 'sales']);

        // Inventory Management Routes
        Route::apiResource('inventory', InventoryController::class)->except(['store', 'destroy']);
        Route::post('/inventory/adjust', [InventoryController::class, 'adjustStock']);
        Route::get('/inventory/low-stock', [InventoryController::class, 'lowStock']);
        Route::get('/inventory/expiring', [InventoryController::class, 'expiring']);

        // Sales Management Routes
        Route::apiResource('sales', SaleController::class);
        Route::post('/sales/{sale}/refund', [SaleController::class, 'refund']);
        Route::get('/sales/{sale}/receipt', [SaleController::class, 'receipt']);

        // Purchase Order Management Routes (Admin, Pharmacist)
        Route::middleware('role:admin,pharmacist')->group(function () {
            Route::apiResource('purchases', PurchaseController::class);
            Route::put('/purchases/{purchase}/receive', [PurchaseController::class, 'receive']);
            Route::put('/purchases/{purchase}/cancel', [PurchaseController::class, 'cancel']);
        });

        // Reports Routes
        Route::prefix('reports')->group(function () {
            Route::get('/dashboard', [ReportController::class, 'dashboard']);
            Route::get('/sales', [ReportController::class, 'sales']);
            Route::get('/inventory', [ReportController::class, 'inventory']);
            Route::get('/financial', [ReportController::class, 'financial']);
            Route::get('/products/top-selling', [ReportController::class, 'topSellingProducts']);
            Route::get('/customers/top', [ReportController::class, 'topCustomers']);

            // Admin only reports
            Route::middleware('role:admin')->group(function () {
                Route::get('/users/performance', [ReportController::class, 'userPerformance']);
                Route::get('/locations/performance', [ReportController::class, 'locationPerformance']);
            });
        });

        // Quick Access Routes for POS
        Route::prefix('pos')->group(function () {
            Route::get('/products/search', [ProductController::class, 'index']);
            Route::get('/products/barcode/{barcode}', [ProductController::class, 'searchByBarcode']);
            Route::post('/sales/process', [SaleController::class, 'store']);
            Route::get('/customers/search', [CustomerController::class, 'index']);
        });
    });
});
