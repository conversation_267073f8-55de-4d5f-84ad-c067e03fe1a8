<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SupplierSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('suppliers')->insert([
            [
                'name' => 'MediCorp Pharmaceuticals',
                'code' => 'SUP001',
                'contact_person' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'mobile' => '(*************',
                'address' => '100 Pharma Drive',
                'city' => 'Chicago',
                'state' => 'IL',
                'postal_code' => '60601',
                'country' => 'US',
                'tax_id' => '12-3456789',
                'license_number' => 'SUP123456',
                'payment_terms' => 'net_30',
                'credit_limit' => 50000.00,
                'is_active' => true,
                'notes' => 'Primary pharmaceutical supplier',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'HealthSupply Inc.',
                'code' => 'SUP002',
                'contact_person' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'mobile' => '(*************',
                'address' => '200 Medical Plaza',
                'city' => 'Indianapolis',
                'state' => 'IN',
                'postal_code' => '46201',
                'country' => 'US',
                'tax_id' => '98-7654321',
                'license_number' => 'SUP789012',
                'payment_terms' => 'net_15',
                'credit_limit' => 25000.00,
                'is_active' => true,
                'notes' => 'Medical supplies and equipment',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Generic Meds Wholesale',
                'code' => 'SUP003',
                'contact_person' => 'Michael Davis',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'mobile' => '(*************',
                'address' => '300 Generic Way',
                'city' => 'Milwaukee',
                'state' => 'WI',
                'postal_code' => '53201',
                'country' => 'US',
                'tax_id' => '55-1122334',
                'license_number' => 'SUP345678',
                'payment_terms' => 'net_45',
                'credit_limit' => 75000.00,
                'is_active' => true,
                'notes' => 'Specializes in generic medications',
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);
    }
}
