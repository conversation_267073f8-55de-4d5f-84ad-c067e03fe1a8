<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Prescription Medicines',
                'slug' => 'prescription-medicines',
                'description' => 'Medicines that require a prescription from a licensed physician',
                'parent_id' => null,
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Over-the-Counter (OTC)',
                'slug' => 'over-the-counter',
                'description' => 'Medicines available without prescription',
                'parent_id' => null,
                'is_active' => true,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Vitamins & Supplements',
                'slug' => 'vitamins-supplements',
                'description' => 'Nutritional supplements and vitamins',
                'parent_id' => null,
                'is_active' => true,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Personal Care',
                'slug' => 'personal-care',
                'description' => 'Personal hygiene and care products',
                'parent_id' => null,
                'is_active' => true,
                'sort_order' => 4,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Medical Devices',
                'slug' => 'medical-devices',
                'description' => 'Medical equipment and devices',
                'parent_id' => null,
                'is_active' => true,
                'sort_order' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Antibiotics',
                'slug' => 'antibiotics',
                'description' => 'Antibiotic medications',
                'parent_id' => 1, // Child of Prescription Medicines
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Pain Relief',
                'slug' => 'pain-relief',
                'description' => 'Pain management medications',
                'parent_id' => 2, // Child of OTC
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ];

        DB::table('categories')->insert($categories);
    }
}
