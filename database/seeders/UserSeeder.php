<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('users')->insert([
            [
                'name' => 'System Administrator',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('password123'),
                'role' => 'admin',
                'phone' => '(*************',
                'address' => '123 Main Street, Springfield, IL 62701',
                'employee_id' => 'EMP001',
                'salary' => 75000.00,
                'hire_date' => '2024-01-01',
                'is_active' => true,
                'location_id' => 1, // Main Pharmacy
                'permissions' => json_encode([
                    'manage_users' => true,
                    'manage_inventory' => true,
                    'manage_sales' => true,
                    'manage_reports' => true,
                    'manage_settings' => true
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'John Pharmacist',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('password123'),
                'role' => 'pharmacist',
                'phone' => '(*************',
                'address' => '456 Oak Avenue, Springfield, IL 62702',
                'employee_id' => 'EMP002',
                'salary' => 65000.00,
                'hire_date' => '2024-01-15',
                'is_active' => true,
                'location_id' => 1, // Main Pharmacy
                'permissions' => json_encode([
                    'manage_prescriptions' => true,
                    'manage_inventory' => true,
                    'process_sales' => true,
                    'view_reports' => true
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Jane Cashier',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('password123'),
                'role' => 'cashier',
                'phone' => '(*************',
                'address' => '789 Pine Street, Springfield, IL 62703',
                'employee_id' => 'EMP003',
                'salary' => 35000.00,
                'hire_date' => '2024-02-01',
                'is_active' => true,
                'location_id' => 1, // Main Pharmacy
                'permissions' => json_encode([
                    'process_sales' => true,
                    'view_inventory' => true
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);
    }
}
