<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = [
            [
                'name' => 'Amoxicillin 500mg Capsules',
                'generic_name' => 'Amoxicillin',
                'brand' => 'Amoxil',
                'sku' => 'AMX500CAP',
                'barcode' => '1234567890123',
                'category_id' => 6, // Antibiotics
                'supplier_id' => 1, // MediCorp
                'strength' => '500mg',
                'dosage_form' => 'Capsule',
                'unit_price' => 0.50,
                'selling_price' => 1.25,
                'mrp' => 1.50,
                'reorder_level' => 100,
                'max_stock_level' => 1000,
                'is_prescription_required' => true,
                'is_controlled_substance' => false,
                'storage_conditions' => 'Store at room temperature',
                'manufacturer' => 'GlaxoSmithKline',
                'is_active' => true,
                'description' => 'Antibiotic used to treat bacterial infections',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Ibuprofen 200mg Tablets',
                'generic_name' => 'Ibuprofen',
                'brand' => 'Advil',
                'sku' => 'IBU200TAB',
                'barcode' => '2345678901234',
                'category_id' => 7, // Pain Relief
                'supplier_id' => 2, // HealthSupply
                'strength' => '200mg',
                'dosage_form' => 'Tablet',
                'unit_price' => 0.15,
                'selling_price' => 0.35,
                'mrp' => 0.50,
                'reorder_level' => 200,
                'max_stock_level' => 2000,
                'is_prescription_required' => false,
                'is_controlled_substance' => false,
                'storage_conditions' => 'Store in cool, dry place',
                'manufacturer' => 'Pfizer',
                'is_active' => true,
                'description' => 'Pain reliever and fever reducer',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Lisinopril 10mg Tablets',
                'generic_name' => 'Lisinopril',
                'brand' => 'Prinivil',
                'sku' => 'LIS10TAB',
                'barcode' => '3456789012345',
                'category_id' => 1, // Prescription Medicines
                'supplier_id' => 3, // Generic Meds
                'strength' => '10mg',
                'dosage_form' => 'Tablet',
                'unit_price' => 0.25,
                'selling_price' => 0.75,
                'mrp' => 1.00,
                'reorder_level' => 150,
                'max_stock_level' => 1500,
                'is_prescription_required' => true,
                'is_controlled_substance' => false,
                'storage_conditions' => 'Store at room temperature',
                'manufacturer' => 'Merck',
                'is_active' => true,
                'description' => 'ACE inhibitor for high blood pressure',
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ];

        DB::table('products')->insert($products);
    }
}
