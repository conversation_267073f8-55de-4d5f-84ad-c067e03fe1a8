<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('locations')->insert([
            [
                'name' => 'Main Pharmacy',
                'code' => 'MAIN001',
                'address' => '123 Main Street',
                'city' => 'Springfield',
                'state' => 'IL',
                'postal_code' => '62701',
                'country' => 'US',
                'phone' => '(*************',
                'email' => '<EMAIL>',
                'license_number' => 'PH123456789',
                'is_active' => true,
                'is_main' => true,
                'settings' => json_encode([
                    'tax_rate' => 8.5,
                    'currency' => 'USD',
                    'timezone' => 'America/Chicago'
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Branch Pharmacy',
                'code' => 'BRANCH001',
                'address' => '456 Oak Avenue',
                'city' => 'Springfield',
                'state' => 'IL',
                'postal_code' => '62702',
                'country' => 'US',
                'phone' => '(*************',
                'email' => '<EMAIL>',
                'license_number' => 'PH987654321',
                'is_active' => true,
                'is_main' => false,
                'settings' => json_encode([
                    'tax_rate' => 8.5,
                    'currency' => 'USD',
                    'timezone' => 'America/Chicago'
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ]);
    }
}
