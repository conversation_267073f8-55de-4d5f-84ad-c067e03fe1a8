<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add new user management fields
            $table->string('avatar')->nullable()->after('email');
            $table->timestamp('last_login_at')->nullable()->after('updated_at');
            $table->integer('login_count')->default(0)->after('last_login_at');
            $table->integer('failed_login_attempts')->default(0)->after('login_count');
            $table->timestamp('locked_until')->nullable()->after('failed_login_attempts');
            $table->timestamp('password_changed_at')->nullable()->after('locked_until');
            $table->boolean('must_change_password')->default(false)->after('password_changed_at');
            $table->boolean('two_factor_enabled')->default(false)->after('must_change_password');
            $table->string('two_factor_secret')->nullable()->after('two_factor_enabled');
            $table->string('emergency_contact_name')->nullable()->after('two_factor_secret');
            $table->string('emergency_contact_phone')->nullable()->after('emergency_contact_name');
            $table->text('notes')->nullable()->after('emergency_contact_phone');

            // Add indexes for performance
            $table->index(['role', 'is_active']);
            $table->index(['location_id', 'is_active']);
            $table->index('last_login_at');
            $table->index('locked_until');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['role', 'is_active']);
            $table->dropIndex(['location_id', 'is_active']);
            $table->dropIndex(['last_login_at']);
            $table->dropIndex(['locked_until']);

            // Drop columns
            $table->dropColumn([
                'avatar',
                'last_login_at',
                'login_count',
                'failed_login_attempts',
                'locked_until',
                'password_changed_at',
                'must_change_password',
                'two_factor_enabled',
                'two_factor_secret',
                'emergency_contact_name',
                'emergency_contact_phone',
                'notes',
            ]);
        });
    }
};
