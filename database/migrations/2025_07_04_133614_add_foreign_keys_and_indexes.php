<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add foreign key constraint for users.location_id
        Schema::table('users', function (Blueprint $table) {
            $table->foreign('location_id')->references('id')->on('locations')->onDelete('set null');
        });

        // Add foreign key constraint for sales.prescription_id
        Schema::table('sales', function (Blueprint $table) {
            $table->foreign('prescription_id')->references('id')->on('prescriptions')->onDelete('set null');
        });

        // Add additional performance indexes
        Schema::table('products', function (Blueprint $table) {
            $table->index(['is_active', 'reorder_level']); // For low stock queries
            $table->index(['is_prescription_required', 'is_active']); // For prescription products
        });

        Schema::table('inventory', function (Blueprint $table) {
            $table->index(['quantity_available', 'status']); // For stock availability queries
            $table->index(['expiry_date', 'quantity_available']); // For expiry tracking
        });

        Schema::table('sales', function (Blueprint $table) {
            $table->index(['is_prescription_sale', 'sale_date']); // For prescription sales tracking
        });

        Schema::table('customers', function (Blueprint $table) {
            $table->index(['outstanding_balance']); // For customer balance queries
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove foreign key constraints
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['location_id']);
        });

        Schema::table('sales', function (Blueprint $table) {
            $table->dropForeign(['prescription_id']);
        });

        // Remove additional indexes
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex(['is_active', 'reorder_level']);
            $table->dropIndex(['is_prescription_required', 'is_active']);
        });

        Schema::table('inventory', function (Blueprint $table) {
            $table->dropIndex(['quantity_available', 'status']);
            $table->dropIndex(['expiry_date', 'quantity_available']);
        });

        Schema::table('sales', function (Blueprint $table) {
            $table->dropIndex(['is_prescription_sale', 'sale_date']);
        });

        Schema::table('customers', function (Blueprint $table) {
            $table->dropIndex(['outstanding_balance']);
        });
    }
};
