<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_adjustments', function (Blueprint $table) {
            $table->id();
            $table->string('adjustment_number')->unique(); // Adjustment reference number
            $table->unsignedBigInteger('product_id');
            $table->unsignedBigInteger('inventory_id')->nullable(); // Specific batch if applicable
            $table->unsignedBigInteger('location_id');
            $table->unsignedBigInteger('user_id'); // User who made the adjustment
            $table->integer('old_quantity'); // Quantity before adjustment
            $table->integer('new_quantity'); // Quantity after adjustment
            $table->integer('adjustment_quantity'); // Difference (can be negative)
            $table->enum('adjustment_type', ['increase', 'decrease', 'correction', 'damage', 'expiry', 'theft', 'return'])->default('correction');
            $table->string('reason'); // Reason for adjustment
            $table->text('notes')->nullable(); // Additional notes
            $table->string('batch_number')->nullable(); // Batch affected
            $table->decimal('cost_impact', 10, 2)->default(0); // Financial impact of adjustment
            $table->boolean('is_approved')->default(false);
            $table->unsignedBigInteger('approved_by')->nullable(); // User who approved
            $table->timestamp('approved_at')->nullable();
            $table->json('metadata')->nullable(); // Additional adjustment data
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('product_id')->references('id')->on('products');
            $table->foreign('inventory_id')->references('id')->on('inventory')->onDelete('set null');
            $table->foreign('location_id')->references('id')->on('locations');
            $table->foreign('user_id')->references('id')->on('users');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['product_id', 'location_id']);
            $table->index(['user_id', 'created_at']);
            $table->index('adjustment_number');
            $table->index('adjustment_type');
            $table->index('is_approved');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_adjustments');
    }
};
