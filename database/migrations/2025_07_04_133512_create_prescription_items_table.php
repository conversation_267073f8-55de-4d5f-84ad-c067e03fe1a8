<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('prescription_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('prescription_id');
            $table->unsignedBigInteger('product_id');
            $table->string('medicine_name'); // As written on prescription
            $table->string('strength')->nullable(); // Dosage strength
            $table->string('dosage_form')->nullable(); // Tablet, capsule, etc.
            $table->integer('quantity_prescribed');
            $table->integer('quantity_dispensed')->default(0);
            $table->string('dosage_instructions'); // How to take the medicine
            $table->integer('days_supply')->nullable(); // Number of days the medicine should last
            $table->integer('refills_allowed')->default(0);
            $table->integer('refills_used')->default(0);
            $table->boolean('generic_substitution_allowed')->default(true);
            $table->enum('status', ['pending', 'dispensed', 'partially_dispensed', 'cancelled'])->default('pending');
            $table->text('pharmacist_notes')->nullable();
            $table->json('metadata')->nullable(); // Additional item data
            $table->timestamps();

            // Foreign keys
            $table->foreign('prescription_id')->references('id')->on('prescriptions')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('products');

            // Indexes
            $table->index(['prescription_id', 'product_id']);
            $table->index('status');
            $table->index('medicine_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('prescription_items');
    }
};
