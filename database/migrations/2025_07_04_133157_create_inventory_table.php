<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id');
            $table->unsignedBigInteger('location_id');
            $table->string('batch_number'); // Batch/Lot number
            $table->date('expiry_date')->nullable(); // Expiration date
            $table->date('manufacture_date')->nullable(); // Manufacturing date
            $table->string('vendor')->nullable(); // Vendor/Supplier for this batch
            $table->integer('quantity_received')->default(0); // Initial quantity received
            $table->integer('quantity_available')->default(0); // Current available quantity
            $table->integer('quantity_reserved')->default(0); // Reserved for orders
            $table->integer('quantity_sold')->default(0); // Total sold from this batch
            $table->decimal('cost_price', 10, 2); // Cost price for this batch
            $table->decimal('selling_price', 10, 2); // Selling price for this batch
            $table->string('storage_location')->nullable(); // Shelf/Bin location
            $table->enum('status', ['active', 'expired', 'recalled', 'damaged'])->default('active');
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable(); // Additional batch data
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('product_id')->references('id')->on('products');
            $table->foreign('location_id')->references('id')->on('locations');

            // Unique constraint for product, location, and batch
            $table->unique(['product_id', 'location_id', 'batch_number'], 'inventory_unique');

            // Indexes
            $table->index(['product_id', 'location_id']);
            $table->index(['expiry_date', 'status']);
            $table->index(['batch_number', 'status']);
            $table->index('quantity_available');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory');
    }
};
