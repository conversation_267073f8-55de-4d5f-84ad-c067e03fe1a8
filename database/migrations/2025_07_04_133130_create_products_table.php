<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Product name
            $table->string('generic_name')->nullable(); // Generic medicine name
            $table->string('brand')->nullable(); // Brand name
            $table->string('sku')->unique(); // Stock Keeping Unit
            $table->string('barcode')->nullable()->unique(); // Product barcode
            $table->unsignedBigInteger('category_id');
            $table->unsignedBigInteger('supplier_id');
            $table->text('description')->nullable();
            $table->string('strength')->nullable(); // Medicine strength (e.g., 500mg)
            $table->string('dosage_form')->nullable(); // Tablet, Capsule, Syrup, etc.
            $table->string('unit_of_measure')->default('piece'); // piece, bottle, box, etc.
            $table->decimal('unit_price', 10, 2); // Cost price
            $table->decimal('selling_price', 10, 2); // Selling price
            $table->decimal('mrp', 10, 2)->nullable(); // Maximum Retail Price
            $table->decimal('discount_percentage', 5, 2)->default(0);
            $table->integer('reorder_level')->default(10); // Minimum stock level
            $table->integer('max_stock_level')->nullable(); // Maximum stock level
            $table->boolean('is_prescription_required')->default(false);
            $table->boolean('is_controlled_substance')->default(false);
            $table->string('manufacturer')->nullable();
            $table->string('country_of_origin')->nullable();
            $table->string('storage_conditions')->nullable(); // Storage requirements
            $table->boolean('is_active')->default(true);
            $table->boolean('is_taxable')->default(true);
            $table->decimal('tax_rate', 5, 2)->default(0); // Tax percentage
            $table->string('image')->nullable(); // Product image path
            $table->json('metadata')->nullable(); // Additional product data
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('category_id')->references('id')->on('categories');
            $table->foreign('supplier_id')->references('id')->on('suppliers');

            // Indexes
            $table->index(['name', 'is_active']);
            $table->index(['category_id', 'is_active']);
            $table->index(['supplier_id', 'is_active']);
            $table->index('sku');
            $table->index('barcode');
            $table->index('generic_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
