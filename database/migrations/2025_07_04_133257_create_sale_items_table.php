<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sale_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('sale_id');
            $table->unsignedBigInteger('product_id');
            $table->unsignedBigInteger('inventory_id')->nullable(); // Specific batch/inventory record
            $table->integer('quantity');
            $table->decimal('unit_price', 10, 2); // Price at time of sale
            $table->decimal('discount_percentage', 5, 2)->default(0);
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('tax_percentage', 5, 2)->default(0);
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->decimal('line_total', 10, 2); // Total for this line item
            $table->string('batch_number')->nullable(); // Batch sold from
            $table->date('expiry_date')->nullable(); // Expiry of the batch sold
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable(); // Additional item data
            $table->timestamps();

            // Foreign keys
            $table->foreign('sale_id')->references('id')->on('sales')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('products');
            $table->foreign('inventory_id')->references('id')->on('inventory')->onDelete('set null');

            // Indexes
            $table->index(['sale_id', 'product_id']);
            $table->index('inventory_id');
            $table->index('batch_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sale_items');
    }
};
