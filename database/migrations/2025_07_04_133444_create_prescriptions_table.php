<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('prescriptions', function (Blueprint $table) {
            $table->id();
            $table->string('prescription_number')->unique(); // Prescription reference number
            $table->unsignedBigInteger('customer_id');
            $table->unsignedBigInteger('location_id');
            $table->string('doctor_name');
            $table->string('doctor_license')->nullable(); // Doctor's license number
            $table->string('doctor_phone')->nullable();
            $table->text('doctor_address')->nullable();
            $table->date('prescription_date');
            $table->date('expiry_date')->nullable(); // Prescription expiry
            $table->text('diagnosis')->nullable(); // Patient diagnosis
            $table->text('instructions')->nullable(); // General instructions
            $table->enum('status', ['pending', 'in_progress', 'ready', 'dispensed', 'cancelled'])->default('pending');
            $table->enum('priority', ['normal', 'urgent', 'emergency'])->default('normal');
            $table->boolean('is_insurance_claim')->default(false);
            $table->string('insurance_provider')->nullable();
            $table->string('insurance_claim_number')->nullable();
            $table->decimal('insurance_copay', 8, 2)->default(0);
            $table->unsignedBigInteger('dispensed_by')->nullable(); // User who dispensed
            $table->timestamp('dispensed_at')->nullable();
            $table->text('pharmacist_notes')->nullable();
            $table->string('image_path')->nullable(); // Scanned prescription image
            $table->json('metadata')->nullable(); // Additional prescription data
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('customer_id')->references('id')->on('customers');
            $table->foreign('location_id')->references('id')->on('locations');
            $table->foreign('dispensed_by')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['customer_id', 'prescription_date']);
            $table->index(['status', 'priority']);
            $table->index('prescription_number');
            $table->index('doctor_name');
            $table->index('prescription_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('prescriptions');
    }
};
