<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_orders', function (Blueprint $table) {
            $table->id();
            $table->string('po_number')->unique(); // Purchase Order number
            $table->unsignedBigInteger('supplier_id');
            $table->unsignedBigInteger('location_id');
            $table->unsignedBigInteger('created_by'); // User who created the PO
            $table->unsignedBigInteger('approved_by')->nullable(); // User who approved the PO
            $table->decimal('subtotal', 12, 2)->default(0);
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->decimal('shipping_cost', 10, 2)->default(0);
            $table->decimal('total_amount', 12, 2);
            $table->enum('status', ['draft', 'pending', 'approved', 'ordered', 'partially_received', 'received', 'cancelled'])->default('draft');
            $table->date('order_date');
            $table->date('expected_delivery_date')->nullable();
            $table->date('received_date')->nullable();
            $table->text('notes')->nullable();
            $table->text('terms_and_conditions')->nullable();
            $table->string('supplier_invoice_number')->nullable(); // Supplier's invoice number
            $table->json('metadata')->nullable(); // Additional PO data
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('supplier_id')->references('id')->on('suppliers');
            $table->foreign('location_id')->references('id')->on('locations');
            $table->foreign('created_by')->references('id')->on('users');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['order_date', 'status']);
            $table->index(['supplier_id', 'order_date']);
            $table->index(['location_id', 'status']);
            $table->index('po_number');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_orders');
    }
};
