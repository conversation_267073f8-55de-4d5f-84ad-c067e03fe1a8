<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pos_records', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // Cashier/User operating POS
            $table->unsignedBigInteger('sale_id'); // Related sale
            $table->unsignedBigInteger('location_id');
            $table->string('terminal_id')->nullable(); // POS terminal identifier
            $table->decimal('total_amount', 12, 2);
            $table->enum('payment_method', ['cash', 'card', 'check', 'insurance', 'credit', 'mixed'])->default('cash');
            $table->decimal('cash_received', 10, 2)->default(0);
            $table->decimal('card_amount', 10, 2)->default(0);
            $table->decimal('check_amount', 10, 2)->default(0);
            $table->decimal('insurance_amount', 10, 2)->default(0);
            $table->decimal('credit_amount', 10, 2)->default(0);
            $table->decimal('change_given', 10, 2)->default(0);
            $table->string('card_transaction_id')->nullable(); // Card payment reference
            $table->string('check_number')->nullable(); // Check number
            $table->enum('transaction_status', ['pending', 'completed', 'failed', 'cancelled', 'refunded'])->default('completed');
            $table->timestamp('transaction_date')->useCurrent();
            $table->text('notes')->nullable();
            $table->json('payment_details')->nullable(); // Detailed payment breakdown
            $table->json('metadata')->nullable(); // Additional POS data
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('user_id')->references('id')->on('users');
            $table->foreign('sale_id')->references('id')->on('sales');
            $table->foreign('location_id')->references('id')->on('locations');

            // Indexes
            $table->index(['user_id', 'transaction_date']);
            $table->index(['location_id', 'transaction_date']);
            $table->index('sale_id');
            $table->index('payment_method');
            $table->index('transaction_status');
            $table->index('terminal_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pos_records');
    }
};
