<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales', function (Blueprint $table) {
            $table->id();
            $table->string('sale_number')->unique(); // Sale/Invoice number
            $table->unsignedBigInteger('customer_id')->nullable(); // Nullable for walk-in customers
            $table->unsignedBigInteger('user_id'); // User who processed the sale
            $table->unsignedBigInteger('location_id');
            $table->decimal('subtotal', 12, 2)->default(0);
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('total_amount', 12, 2);
            $table->decimal('amount_paid', 12, 2)->default(0);
            $table->decimal('change_amount', 10, 2)->default(0);
            $table->enum('payment_method', ['cash', 'card', 'check', 'insurance', 'credit', 'mixed'])->default('cash');
            $table->string('payment_reference')->nullable(); // Card transaction ID, check number, etc.
            $table->enum('status', ['pending', 'completed', 'cancelled', 'refunded'])->default('completed');
            $table->text('notes')->nullable();
            $table->boolean('is_prescription_sale')->default(false);
            $table->unsignedBigInteger('prescription_id')->nullable(); // Link to prescription if applicable
            $table->json('payment_details')->nullable(); // Detailed payment information
            $table->timestamp('sale_date')->useCurrent();
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('set null');
            $table->foreign('user_id')->references('id')->on('users');
            $table->foreign('location_id')->references('id')->on('locations');

            // Indexes
            $table->index(['sale_date', 'status']);
            $table->index(['customer_id', 'sale_date']);
            $table->index(['user_id', 'sale_date']);
            $table->index(['location_id', 'sale_date']);
            $table->index('sale_number');
            $table->index('payment_method');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales');
    }
};
